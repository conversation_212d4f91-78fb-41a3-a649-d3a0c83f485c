import request from '@/utils/request'

// 查询系统协议列表
export function listAgreement(query) {
  return request({
    url: '/system/agreement/list',
    method: 'get',
    params: query
  })
}

// 查询系统协议详细
export function getAgreement(id) {
  return request({
    url: '/system/agreement/' + id,
    method: 'get'
  })
}

// 新增系统协议
export function addAgreement(data) {
  return request({
    url: '/system/agreement',
    method: 'post',
    data: data
  })
}

// 修改系统协议
export function updateAgreement(data) {
  return request({
    url: '/system/agreement',
    method: 'put',
    data: data
  })
}

// 删除系统协议
export function delAgreement(id) {
  return request({
    url: '/system/agreement/' + id,
    method: 'delete'
  })
}

// 导出系统协议
export function exportAgreement(query) {
  return request({
    url: '/system/agreement/export',
    method: 'post',
    params: query
  })
}
