<template>
    <view class="vip-buy-container">
        <!-- 提示信息 -->
        <view class="notice-card">
            <view class="notice-content">
                <text class="notice-text">
                    {{ noticeText }}
                </text>
            </view>
        </view>

        <!-- 表单区域 -->
        <view class="form-container">
            <!-- 用户手机号 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/shifu.png" mode="aspectFit"></image>用户手机号
                </view>
                <view class="word">{{ userPhone || '未获取到手机号' }}</view>
            </view>

            <!-- 场库选择 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/changku.png" mode="aspectFit"></image>场库名称
                </view>
                <view class="word"
                    :class="{ 'clickable-text': canSelectWarehouse, 'red': packageOrder.warehouseName && packageOrder.warehouseName !== '--' }"
                    @tap="handleWarehouseClick">
                    {{ packageOrder.warehouseName || '请选择场库' }}
                    <u-icon v-if="canSelectWarehouse" name="arrow-down" size="12" color="#4BA1FC"
                        style="margin-left: 8rpx;"></u-icon>
                </view>
            </view>

            <!-- 车牌号 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/chepai.png" mode="aspectFit"></image>车牌牌号
                </view>
                <view class="word" :class="[canSelectPlate ? 'clickable-text' : '']" @tap="handlePlateClick">
                    {{ packageOrder.plateNo || '请输入车牌号' }}
                    <u-icon v-if="canSelectPlate" name="edit-pen" size="12" color="#4BA1FC"
                        style="margin-left: 8rpx;"></u-icon>
                </view>
            </view>

            <!-- 套餐类型 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/taocantype.png" mode="aspectFit"></image>套餐类型
                </view>
                <view class="word" v-if="packageOrder.vipType === 2 && packageOrder.isRenewal">{{ packageOrder.packageName || '-' }}</view>
                <view class="word" v-else :class="[canSelectPackage ? 'clickable-text' : '']" @tap="handlePackageClick">
                    {{ packageOrder.packageName || '请选择套餐类型' }}
                    <u-icon v-if="canSelectPackage" name="arrow-down" size="12" color="#4BA1FC" style="margin-left: 8rpx;"></u-icon>
                </view>
            </view>

            <!-- 开始时间 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>开始时间
                </view>
                <view class="word" :class="[canSelectTime ? 'clickable-text' : '']">
                    <picker v-if="canSelectTime" mode="date" :value="selectedDate" :start="minDate" :end="maxDate"
                        @change="onDateChange">
                        <view class="picker-display">
                            {{ packageOrder.beginVipTime || '请选择开始时间' }}
                        </view>
                    </picker>
                    <view v-else class="picker-display">
                        {{ packageOrder.beginVipTime || '请选择开始时间' }}
                    </view>
                </view>
            </view>

            <!-- 当前到期时间（仅续费时显示） -->
            <view v-if="packageOrder.isRenewal" class="content_item">
                <view class="title">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>当前到期时间
                </view>
                <view class="word">{{ packageOrder.expirationTime }}</view>
            </view>

            <!-- 到期时间 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>{{ packageOrder.isRenewal ?
                    '续费后到期时间' : '到期时间' }}
                </view>
                <view class="word">
                    {{ packageOrder.isRenewal ? (packageOrder.newExpirationTime || '--') : (packageOrder.expirationTime || '--') }}
                </view>
            </view>

            <!-- 套餐价格 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/taocanprice.png" mode="aspectFit"></image>套餐价格
                </view>
                <view class="word">
                    <text class="tips">¥</text> 
                    {{ packageOrder.packagePrice !== null && packageOrder.packagePrice !== undefined ? packageOrder.packagePrice : '0.00' }}
                </view>
            </view>

            <!-- 实付金额 -->
            <view class="content_item">
                <view class="title">
                    <image src="/static/package/shifu.png" mode="aspectFit"></image>实付金额
                </view>
                <view class="word money">
                    <text class="tips red">¥</text> {{ packageOrder.packagePrice !== null && packageOrder.packagePrice !== undefined ? packageOrder.packagePrice : '0.00' }}
                </view>
            </view>
        </view>

        <!-- 支付按钮 -->
        <view class="button-section">
            <button class="pay-button" @tap="submitOrder" :disabled="!canSubmit">
                {{ packageOrder.isRenewal ? '确认续费' : '立即购买' }}
            </button>
        </view>

        <!-- 车牌输入键盘 -->
        <plate-input @typeChange="typeChange" v-if="plateShow" :plate="packageOrder.plateNo || ''" @export="setPlate"
            @close="plateShow = false" />

        <!-- 场库选择器 -->
        <WarehouseSelector :show="showSelector" :warehouseList="warehouseList" :currentWarehouse="currentWarehouse"
            :windowHeightHalf="400" @close="closeWarehouseSelector" @select="selectWarehouse" />
        
        <!-- 套餐选择器 -->
        <up-popup :show="showPackageSelector" mode="bottom" round="20" :safeAreaInsetBottom="true" @close="showPackageSelector = false">
            <view class="package-popup">
                <view class="popup-header">
                    <text class="popup-title">选择套餐类型</text>
                    <up-icon name="close" size="18" color="#999" @click="showPackageSelector = false"></up-icon>
                </view>
                <scroll-view class="package-list" scroll-y style="max-height: 400px;">
                    <view v-for="(option, index) in packageOptions" :key="index" 
                          class="package-item"
                          :class="{ active: selectedPackage && selectedPackage.name === option.name }" 
                          @click="selectPackageOption(option)">
                        <view class="package-item-left">
                            <text class="package-item-name">{{ option.name }}</text>
                            <text class="package-item-days">{{ option.days }}天</text>
                        </view>
                        <view class="package-item-right">
                            <text class="package-item-price">¥{{ option.price }}</text>
                            <!-- <up-icon v-if="selectedPackage && selectedPackage.name === option.name" name="checkmark" size="16" color="#40a9ff"></up-icon> -->
                        </view>
                    </view>
                </scroll-view>
            </view>
        </up-popup>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import plateInput from '@/components/uni-plate-input/uni-plate-input.vue'
import WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue'
import { getParkWareHouseList } from '@/api/warehouse'
import { checkCarInWarehouse, createOrder, updateOrder } from '@/api/package'
import { getOpenid } from '@/api/login'
import { formatDate, extractDatePart, isSameDate } from '@/utils/utils'

// 响应式数据
const userPhone = ref('')
const plateShow = ref(false)
const warehouseList = ref([])
const showSelector = ref(false)
const currentWarehouse = ref({ id: 0, name: '' })

// 核心数据
const packageOrder = ref({
    plateNo: '',
    warehouseId: null,
    warehouseName: '',
    packageId: null,
    packageName: '',
    packagePrice: 0,
    packageDays: null,
    beginVipTime: '',
    expirationTime: '',
    vipType: 0,
    isRenewal: false,
    index: 1
})
const packageJudge = ref({})

// 状态控制
const isCarInWarehouse = ref(false)
const canSelectTime = ref(false)
const canSelectWarehouse = ref(true)
const canSelectPlate = ref(true)
const canSelectPackage = ref(false)

// 时间选择相关
const selectedDate = ref('')
const minDate = ref('')
const maxDate = ref('')

// 套餐选择相关
const showPackageSelector = ref(false)
const packageOptions = ref([])
const selectedPackage = ref(null)

// 计算属性
const canSubmit = computed(() => {
    const basicRequirements = packageOrder.value.plateNo && 
                             packageOrder.value.warehouseId && 
                             packageOrder.value.beginVipTime
    
    // VIP用户需要有套餐价格
    if (packageOrder.value.vipType === 2) {
        return basicRequirements && packageOrder.value.packagePrice !== null
    }
    
    // 集团客户需要选择套餐类型
    if (packageOrder.value.vipType === 1) {
        const packageRequirements = packageOrder.value.packageName && 
                                   packageOrder.value.packageDays !== null &&
                                   packageOrder.value.packagePrice !== null
        
        // 续费情况下还需要有新的结束时间
        if (packageOrder.value.isRenewal) {
            return basicRequirements && packageRequirements && packageOrder.value.newExpirationTime !== null
        }
        
        return basicRequirements && packageRequirements
    }
    
    return basicRequirements
})

// 提示文本
const noticeText = computed(() => {
    const userType = packageOrder.value.vipType === 1 ? '集团客户' : 'VIP用户'
    const packageInfo = packageOrder.value.packageName ? 
        `${packageOrder.value.packageName}(${packageOrder.value.packageDays || ''}个自然日)` : 
        '套餐类型未选择'
    
    if (packageOrder.value.isRenewal) {
        if (packageOrder.value.vipType === 1) {
            // 集团客户续费，可以选择套餐类型
            return `当前的续费套餐是：${packageInfo}。
            续费将在原有套餐到期日期基础上延长，无法修改时间。`;
        } else {
            // VIP用户续费，不可选择套餐类型
            return `当前的续费套餐是：${packageInfo}。
            续费将在原有套餐到期日期基础上延长，无法修改时间。`;
        }
    }
    
    if (isCarInWarehouse.value) {
        const hasExpiredMember = packageJudge.value.endVipTime !== null;
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        
        if (hasExpiredMember || hasParkingPayment) {
            return `当前${userType}套餐是：${packageInfo}。
            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;
        } else {
            return `当前${userType}套餐是：${packageInfo}。
            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;
        }
    }
    
    return `当前${userType}开通套餐是：${packageInfo}。
    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;
})

// 页面加载
onLoad((options) => {
    loadUserInfo()
    loadWarehouseList()
    console.log('接收到的参数:', options)
    
    if (options.packageOrder) {
        try {
            const parsedOrder = JSON.parse(decodeURIComponent(options.packageOrder))
            // 合并传入的数据和默认值，确保所有属性都有值
            packageOrder.value = Object.assign({}, packageOrder.value, parsedOrder)
            packageOrder.value.phoneNumber = userPhone.value
            
            // 如果是集团客户续费且没有选择套餐，初始化newExpirationTime为null
            if (packageOrder.value.isRenewal && packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {
                packageOrder.value.newExpirationTime = null
            }
            
            console.log('解析的套餐订单:', packageOrder.value)
            
            // 初始化控制状态
            initControlStates()
            
            // 如果是续费，直接处理续费逻辑
            if (packageOrder.value.isRenewal) {
                handleRenewalOrder()
            } else {
                // 非续费情况，检查是否需要调用packageJudge
                checkPackageJudge()
            }
        } catch (error) {
            console.error('解析套餐订单失败:', error)
        }
    }
})


// 初始化控制状态
const initControlStates = () => {
    // 续费时：车辆，场库，时间都不可选择
    if (packageOrder.value.isRenewal) {
        canSelectWarehouse.value = false
        canSelectPlate.value = false
        canSelectTime.value = false
        // 集团客户续费时，套餐类型可以选择
        canSelectPackage.value = packageOrder.value.vipType === 1
        if (canSelectPackage.value) {
            generatePackageOptions()
        }
        return
    }
    
    // 非续费时：根据传入参数决定
    // 有车牌号：不允许选择车牌，只允许选择场库，时间
    if (packageOrder.value.plateNo && packageOrder.value.plateNo !== '--') {
        canSelectPlate.value = false
        canSelectWarehouse.value = true
    } else {
        // 没有车牌号：允许选择车牌，场库，时间
        canSelectPlate.value = true
        canSelectWarehouse.value = true
    }
    
    // 集团客户需要选择套餐类型
    if (packageOrder.value.vipType === 1) {
        canSelectPackage.value = true
        generatePackageOptions()
    }
}

// 生成套餐选项
const generatePackageOptions = () => {
    const index = packageOrder.value.index || 1
    const options = []
    
    // if (index === 1) {
    //     // 第一辆车：只能选择年度套餐，365天，0元
    //     options.push({
    //         name: '年度套餐',
    //         days: 365,
    //         price: 0
    //     })
    // } else if (index === 2) {
    //     // 第二辆车：年度套餐（365天，600元）或半年度套餐（183天，300元）
    //     options.push({
    //         name: '年度套餐',
    //         days: 365,
    //         price: 600
    //     })
    //     options.push({
    //         name: '半年度套餐',
    //         days: 183,
    //         price: 300
    //     })
    // } else if (index === 3) {
    //     // 第三辆车：年度套餐（365天，1800元）或半年度套餐（183天，900元）
    //     options.push({
    //         name: '年度套餐',
    //         days: 365,
    //         price: 1800
    //     })
    //     options.push({
    //         name: '半年度套餐',
    //         days: 183,
    //         price: 900
    //     })
    // }
    if (index === 1) {
        // 第一辆车：只能选择年度套餐，365天，0元
        options.push({
            id: 1,
            name: '年度套餐',
            days: 1,
            price: 0
        })
    } else if (index === 2) {
        // 第二辆车：年度套餐（365天，600元）或半年度套餐（183天，300元）
        options.push({
            id: 4,
            name: '年度套餐',
            days: 2,
            price: 0.01
        })
        options.push({
            id: 2,
            name: '半年度套餐',
            days: 1,
            price: 0.01
        })
    } else if (index === 3) {
        // 第三辆车：年度套餐（365天，1800元）或半年度套餐（183天，900元）
        options.push({
            id: 4,  
            name: '年度套餐',
            days: 2,
            price: 0.01
        })
        options.push({
            id: 2,
            name: '半年度套餐',
            days: 1,
            price: 0.01
        })
    }
    
    packageOptions.value = options
}

// 检查是否需要调用packageJudge
const checkPackageJudge = () => {
    // 当车牌和场库都存在时，调用packageJudge
    if (packageOrder.value.plateNo && packageOrder.value.warehouseId) {
        performPackageJudge()
    } else {
        // 车辆不在场，允许选择开始时间
        handleCarNotInWarehouse()
    }
}

// 执行packageJudge检查
const performPackageJudge = async () => {
    try {
        const res = await checkCarInWarehouse({
            plateNo: packageOrder.value.plateNo,
            warehouseId: packageOrder.value.warehouseId
        })
        
        packageJudge.value = res.data
        isCarInWarehouse.value = res.data && res.data.isCarInWarehouse
        
        console.log('packageJudge结果:', res.data)
        console.log('车辆是否在场:', isCarInWarehouse.value)
        
        if (isCarInWarehouse.value) {
            handleCarInWarehouse()
        } else {
            handleCarNotInWarehouse()
        }
    } catch (error) {
        console.error('检查车辆失败:', error)
        isCarInWarehouse.value = false
        handleCarNotInWarehouse()
    }
}

// 处理续费订单
const handleRenewalOrder = () => {
    if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {
        uni.showToast({
            title: '续费订单数据异常，请重新操作',
            icon: 'none',
            duration: 2000
        })
        return
    }
    
    // 如果是集团客户续费，需要选择套餐类型后才能计算结束时间
    if (packageOrder.value.vipType === 1 && !packageOrder.value.packageDays) {
        // 集团客户续费，暂时不计算结束时间，等选择套餐后再计算
        packageOrder.value.newExpirationTime = null
        console.log('集团客户续费，等待选择套餐类型')
        return
    }
    
    // 续费：在原结束时间基础上延长
    const originalEndDate = new Date(packageOrder.value.expirationTime)
    const newEndDate = new Date(originalEndDate)
    newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays)
    
    const endDateStr = formatDate(newEndDate)
    packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`
    
    console.log('续费订单处理完成，新结束时间:', packageOrder.value.newExpirationTime)
}

// 处理车辆在场的开通会员
const handleCarInWarehouse = () => {
    // 检查是否有会员过期记录
    const hasExpiredMember = packageJudge.value.endVipTime !== null;
    
    if (hasExpiredMember && packageJudge.value.finalBeginTime) {
        // 如果有会员过期记录，需要判断finalBeginTime是来自会员过期还是临停缴费
        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);
        let startDate = new Date(finalBeginDate);
        
        // 判断finalBeginTime是否来自临停缴费（临停缴费时间更晚）
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        const isFinalBeginFromParking = hasParkingPayment && 
            packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;
        
        if (isFinalBeginFromParking) {
            // 如果finalBeginTime来自临停缴费时间，使用finalBeginTime的0点（不加一天）
            startDate.setHours(0, 0, 0, 0);
        } else {
            // 如果finalBeginTime来自会员过期时间，使用finalBeginTime+1天的0点
            startDate.setDate(finalBeginDate.getDate() + 1);
            startDate.setHours(0, 0, 0, 0);
        }
        
        const startDateStr = formatDate(startDate);
        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;
        
        // 计算结束时间：需要判断开始时间是否是今天
        if (packageOrder.value.packageDays) {
            const today = new Date();
            const endDate = new Date(startDate);
            const isToday = isSameDate(startDate, today);
            
            if (isToday) {
                // 如果是今天，送用户0-1天优惠
                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
            } else {
                // 如果不是今天，正常计算（不送优惠天数）
                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
            }
            
            const endDateStr = formatDate(endDate);
            packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
        }
    } else {
        // 没有会员过期记录，使用接口返回的finalBeginTime作为开始时间
        if (packageJudge.value.finalBeginTime) {
            // 将finalBeginTime转换为日期部分并设置为00:00:00
            const datePart = extractDatePart(packageJudge.value.finalBeginTime);
            packageOrder.value.beginVipTime = `${datePart} 00:00:00`;
            
            // 计算结束时间：开始时间+套餐天数（车辆在场额外优惠）
            if (packageOrder.value.packageDays) {
                const startDate = new Date(packageOrder.value.beginVipTime);
                const endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
                
                const endDateStr = formatDate(endDate);
                packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
            }
        } else {
            uni.showToast({
                title: '参数错误，请联系客服',
                icon: 'none',
                duration: 2000
            });
            return;
        }
    }
    
    canSelectTime.value = false;
}

// 处理车辆不在场开通会员
const handleCarNotInWarehouse = () => {
    canSelectTime.value = true
    
    // 设置默认开始时间为今天
    const today = new Date()
    const dateStr = formatDate(today)
    packageOrder.value.beginVipTime = `${dateStr} 00:00:00`
    selectedDate.value = dateStr
    
    // 设置日期选择范围
    updateDateRange()
    calculateEndDate()
    
    console.log('车辆不在场，允许选择开始时间')
}

// 加载用户信息（从缓存获取手机号）
const loadUserInfo = () => {
    try {
        const wxUser = uni.getStorageSync('wxUser')
        if (wxUser && wxUser.phoneNumber) {
            userPhone.value = wxUser.phoneNumber
        } else {
            userPhone.value = '未获取到手机号'
        }
        console.log('获取用户手机号:', userPhone.value)
    } catch (error) {
        console.error('获取用户信息失败:', error)
        userPhone.value = '获取失败'
    }
}

// 加载场库列表
const loadWarehouseList = async () => {
    try {
        const res = await getParkWareHouseList()
        warehouseList.value = res.data.map(item => ({
            id: item.id,
            name: item.warehouseName,
            latitude: item.latitude,
            longitude: item.longitude
        }))
        
        // 设置当前场库
        if (packageOrder.value.warehouseId && packageOrder.value.warehouseName) {
            currentWarehouse.value = {
                id: packageOrder.value.warehouseId,
                name: packageOrder.value.warehouseName
            }
        }
    } catch (error) {
        console.error('获取场库列表失败:', error)
        uni.showToast({
            title: '场库数据加载失败',
            icon: 'none'
        })
    }
}

// 场库选择相关方法
const handleWarehouseClick = () => {
    if (!canSelectWarehouse.value) return
    
    if (!uni.getStorageSync("token")) {
        uni.showModal({
            title: "提示",
            content: "请先登录",
            success: (res) => {
                if (res.confirm) {
                    uni.navigateTo({
                        url: "/pages/login/login",
                    })
                }
            },
        })
        return
    }
    showSelector.value = true
}

const closeWarehouseSelector = () => {
    showSelector.value = false
}

const selectWarehouse = (warehouse) => {
    packageOrder.value.warehouseId = warehouse.id
    packageOrder.value.warehouseName = warehouse.name
    currentWarehouse.value = warehouse
    
    uni.setStorageSync('currentWarehouse', currentWarehouse.value)
    closeWarehouseSelector()
    
    uni.showToast({
        title: `已选择${warehouse.name}`,
        icon: "success",
        duration: 1500,
    })
    
    // 选择场库后，检查是否需要调用packageJudge
    checkPackageJudge()
}

// 车牌选择相关方法
const handlePlateClick = () => {
    if (!canSelectPlate.value) return
    plateShow.value = true
}

const setPlate = (plate) => {
    if (plate.length >= 7) {
        packageOrder.value.plateNo = plate
        plateShow.value = false
        
        // 选择车牌后，检查是否需要调用packageJudge
        checkPackageJudge()
    }
}

const typeChange = (e) => {
    // 重置车牌号
    packageOrder.value.plateNo = ''
}

// 套餐选择相关方法
const handlePackageClick = () => {
    if (!canSelectPackage.value) return
    showPackageSelector.value = true
}

const selectPackageOption = (option) => {
    selectedPackage.value = option
    packageOrder.value.packageId = option.id
    packageOrder.value.packageName = option.name
    packageOrder.value.packagePrice = option.price
    packageOrder.value.packageDays = option.days
    showPackageSelector.value = false
    
    // 选择套餐后重新计算结束时间
    if (packageOrder.value.isRenewal) {
        // 续费：在原结束时间基础上延长
        const originalEndDate = new Date(packageOrder.value.expirationTime)
        const newEndDate = new Date(originalEndDate)
        newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays)
        
        const endDateStr = formatDate(newEndDate)
        packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`
        
        console.log('续费套餐选择完成，新结束时间:', packageOrder.value.newExpirationTime)
    } else if (packageOrder.value.beginVipTime) {
        // 非续费：正常计算结束时间
        calculateEndDate()
    }
    
    uni.showToast({
        title: `已选择${option.name}`,
        icon: 'success',
        duration: 1500
    })
}

// 时间相关方法
const updateDateRange = () => {
    if (!canSelectTime.value) return
    
    const today = new Date()
    minDate.value = formatDate(today)
    
    const maxDateObj = new Date()
    maxDateObj.setMonth(today.getMonth() + 3)
    maxDate.value = formatDate(maxDateObj)
    
    selectedDate.value = formatDate(today)
}

const onDateChange = (e) => {
    if (!canSelectTime.value) return
    
    const selectedDateStr = e.detail.value
    packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`
    selectedDate.value = selectedDateStr
    
    calculateEndDate()
}

const calculateEndDate = () => {
    if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {
        const startDate = new Date(packageOrder.value.beginVipTime)
        const today = new Date()
        const endDate = new Date(startDate)
        
        const isToday = isSameDate(startDate, today)
        
        if (isToday) {
            // 如果是今天，送用户一天（套餐天数+1天-1）
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays)
            console.log('车辆不在场，开始时间是今天，送用户一天，截止日期', endDate)
        } else {
            // 如果不是今天，正常计算（套餐天数-1天）
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1)
        }

        const endDateStr = formatDate(endDate)
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`
    }
}

// 提交订单
const submitOrder = () => {
    // 验证必填项
    if (!packageOrder.value.plateNo) {
        uni.showToast({
            title: '请选择车牌号',
            icon: 'none'
        })
        return
    }
    
    if (!packageOrder.value.warehouseId) {
        uni.showToast({
            title: '请选择场库',
            icon: 'none'
        })
        return
    }
    
    if (packageOrder.value.vipType === 1 && !packageOrder.value.packageName) {
        uni.showToast({
            title: '请选择套餐类型',
            icon: 'none'
        })
        return
    }
    
    if (!packageOrder.value.beginVipTime) {
        uni.showToast({
            title: '请选择开始时间',
            icon: 'none'
        })
        return
    }
    
    // 校验到期时间
    const currentTime = new Date()
    let endTime
    
    if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {
        endTime = new Date(packageOrder.value.newExpirationTime)
    } else if (packageOrder.value.expirationTime) {
        endTime = new Date(packageOrder.value.expirationTime)
    } else {
        uni.showToast({
            title: '套餐到期时间异常，请重新选择',
            icon: 'none',
            duration: 2000
        })
        return
    }
    
    if (endTime <= currentTime) {
        uni.showToast({
            title: '套餐到期时间不能早于当前时间，请重新选择',
            icon: 'none',
            duration: 3000
        })
        return
    }
    
    // 微信登录获取openid
    uni.login({
        success: async (loginRes) => {
            console.log('登录成功 res:', loginRes)
            try {
                const openidRes = await getOpenid({
                    wxCode: loginRes.code
                })
                console.log('获取openid res:', openidRes)
                
                packageOrder.value.openid = openidRes.data
                packageOrder.value.userPhone = userPhone.value
                
                createOrderWithOpenid()
            } catch (error) {
                console.error('获取openid失败:', error)
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                    duration: 2000
                })
            }
        },
        fail: (error) => {
            console.error('登录失败:', error)
            uni.showToast({
                title: '登录失败，请重试',
                icon: 'none',
                duration: 2000
            })
        }
    })
}

// 创建订单
const createOrderWithOpenid = () => {
    uni.showLoading({
        title: '加载中...',
        mask: true
    })
    
    createOrder(packageOrder.value).then(res => {
        console.log('创建订单 res:', res)
        if (res.data.needPay) {
            uni.requestPayment({
                timeStamp: res.data.timeStamp,
                nonceStr: res.data.nonceStr,
                package: res.data.package,
                signType: res.data.signType,
                paySign: res.data.paySign,
                success: function (result) {
                    // 延迟一下显示toast，避免与complete中的hideLoading冲突
                    uni.hideLoading()
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付成功~',
                            icon: 'none',
                            duration: 2000
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                },
                fail: function (err) {
                    uni.hideLoading()
                    console.log('支付失败的回调：', err)

                    // 调用更新订单接口，将状态改为已取消
                    if (res.data.orderId) {
                        updateOrder({
                            id: res.data.orderId,
                            payStatus: 3  // 已取消
                        }).then(updateRes => {
                            console.log('订单状态更新为已取消：', updateRes)
                        }).catch(updateErr => {
                            console.log('订单状态更新失败：', updateErr)
                            // 即使更新失败也不影响用户体验
                        })
                    }

                    // 延迟一下显示toast，避免与hideLoading冲突
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付失败',
                            icon: 'none',
                            duration: 1500
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                },
                complete: function (res) {
                    uni.hideLoading()
                }
            })
        } else if (!res.data.needPay){
            uni.hideLoading()
            setTimeout(() => {
                uni.showToast({
                    title: '开通成功~',
                    icon: 'none',
                    duration: 2000
                })
            }, 100)
            setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        }
    }).catch(err => {
        console.log(err)
        uni.hideLoading()
        setTimeout(() => {
            uni.showToast({
                title: '订单创建失败',
                icon: 'none',
                duration: 1500
            })
        }, 100)
        setTimeout(() => {
            uni.navigateBack()
        }, 2000)
    })
}
</script>

<style lang="scss" scoped>
.vip-buy-container {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20rpx;
    padding-bottom: 40rpx;
}

.notice-card {
    background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    
    .notice-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        
        .notice-text {
            font-size: 26rpx;
            color: #e65100;
            line-height: 1.5;
            flex: 1;
        }
    }
}

.form-container {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.content_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s ease;

    &:last-child {
        border-bottom: none;
    }
}

.title {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333;
    min-width: 160rpx;
    
    image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 15rpx;
    }
}

.word {
    font-size: 28rpx;
    color: #666;
    text-align: right;
    flex: 1;
    
    &.red {
        color: #ff4757;
        font-weight: bold;
    }
    
    &.money {
        font-size: 32rpx;
        font-weight: bold;
        color: #ff4757;
    }
    
    &.clickable-text {
        color: #4BA1FC;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        text-decoration: underline;
        text-decoration-color: rgba(75, 161, 252, 0.3);
        text-underline-offset: 4rpx;
    }
    
    .picker-display {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        color: inherit;
    }
}

.tips {
    font-size: 24rpx;
    
    &.red {
        color: #ff4757;
    }
}

.button-section {
    margin-top: 40rpx;

    .pay-button {
        width: 100%;
        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
        border-radius: 44rpx;
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        height: 88rpx;
        line-height: 88rpx;
        border: none;
        box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

        &:active {
            transform: translateY(1rpx);
            box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
        }

        &[disabled] {
            background: #ccc;
            color: #999;
            box-shadow: none;
        }
    }
}

// 套餐选择器样式
.package-popup {
    background: #fff;
    
    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 40rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .popup-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }
    }
    
    .package-list {
        .package-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30rpx 40rpx;
            border-bottom: 1rpx solid #f0f0f0;
            transition: background-color 0.2s;
            
            &:last-child {
                border-bottom: none;
            }
            
            &:active {
                background-color: #f8f9fa;
            }
            
            &.active {
                background-color: #f0f8ff;
            }
            
            .package-item-left {
                display: flex;
                flex-direction: column;
                
                .package-item-name {
                    font-size: 28rpx;
                    color: #333;
                    font-weight: 500;
                    margin-bottom: 8rpx;
                }
                
                .package-item-days {
                    font-size: 24rpx;
                    color: #666;
                }
            }
            
            .package-item-right {
                display: flex;
                align-items: center;
                gap: 16rpx;
                
                .package-item-price {
                    font-size: 28rpx;
                    color: #ff4757;
                    font-weight: bold;
                }
            }
        }
    }
}
</style>