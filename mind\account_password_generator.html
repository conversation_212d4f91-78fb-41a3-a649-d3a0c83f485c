<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccountCraft - 账号密码生成器</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #007AFF;
            --primary-purple: #5856D6;
            --primary-teal: #32D74B;
            --primary-orange: #FF9500;
            --primary-red: #FF3B30;
            --primary-pink: #FF2D92;
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --surface-primary: rgba(255, 255, 255, 0.95);
            --surface-secondary: rgba(255, 255, 255, 0.8);
            --surface-tertiary: rgba(255, 255, 255, 0.6);
            --border-color: rgba(0, 0, 0, 0.1);
            --shadow-small: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
            --shadow-large: 0 20px 60px rgba(0, 0, 0, 0.15);
            --blur-strong: blur(20px);
            --blur-medium: blur(10px);
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 20px;
            --radius-xlarge: 28px;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #000;
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 星空背景 */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
            z-index: -2;
        }

        .starfield::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 20s linear infinite;
        }

        @keyframes sparkle {
            from { transform: translateX(0); }
            to { transform: translateX(-200px); }
        }

        /* 流星效果 */
        .meteor {
            position: absolute;
            width: 300px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fff, transparent);
            border-radius: 50px;
            animation: meteor 3s linear infinite;
            opacity: 0;
        }

        .meteor:nth-child(1) {
            top: 10%;
            left: -300px;
            animation-delay: 0s;
        }

        .meteor:nth-child(2) {
            top: 30%;
            left: -300px;
            animation-delay: 2s;
        }

        .meteor:nth-child(3) {
            top: 60%;
            left: -300px;
            animation-delay: 4s;
        }

        @keyframes meteor {
            0% {
                transform: translateX(0) translateY(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateX(calc(100vw + 300px)) translateY(300px);
                opacity: 0;
            }
        }

        /* 主容器 */
        .main-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            z-index: 1;
        }

        /* 头部区域 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
            border-radius: 2px;
        }

        .header h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            background: linear-gradient(135deg, #fff 0%, #e1e1e6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.125rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* 控制面板 */
        .controls-panel {
            background: var(--surface-primary);
            backdrop-filter: var(--blur-strong);
            border-radius: var(--radius-xlarge);
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-large);
            border: 1px solid var(--border-color);
        }

        .control-row {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
            min-width: 200px;
        }

        .control-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-input {
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-medium);
            font-size: 16px;
            font-family: inherit;
            background: white;
            color: var(--text-primary);
            transition: all 0.2s ease;
            width: 100%;
            text-align: center;
        }

        .control-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        /* 苹果风格按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            text-decoration: none;
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            min-width: 120px;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.1s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            color: white;
            border: 1px solid rgba(0, 122, 255, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056CC 0%, #003D99 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #32D74B 0%, #28A745 100%);
            color: white;
            border: 1px solid rgba(50, 215, 75, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #28A745 0%, #1E7E34 100%);
        }

        .btn-purple {
            background: linear-gradient(135deg, #5856D6 0%, #4A47B8 100%);
            color: white;
            border: 1px solid rgba(88, 86, 214, 0.3);
        }

        .btn-purple:hover {
            background: linear-gradient(135deg, #4A47B8 0%, #3C3A9A 100%);
        }

        .btn-orange {
            background: linear-gradient(135deg, #FF9500 0%, #E6850E 100%);
            color: white;
            border: 1px solid rgba(255, 149, 0, 0.3);
        }

        .btn-orange:hover {
            background: linear-gradient(135deg, #E6850E 0%, #CC750C 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #FF3B30 0%, #E6342A 100%);
            color: white;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #E6342A 0%, #CC2E24 100%);
        }

        /* 结果面板 */
        .results-panel {
            background: var(--surface-primary);
            backdrop-filter: var(--blur-strong);
            border-radius: var(--radius-xlarge);
            padding: 32px;
            box-shadow: var(--shadow-large);
            border: 1px solid var(--border-color);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .result-card {
            background: var(--surface-secondary);
            backdrop-filter: var(--blur-medium);
            border-radius: var(--radius-large);
            padding: 20px;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .result-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-blue);
        }

        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .result-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .copy-btn {
            padding: 6px 12px;
            background: var(--primary-blue);
            color: white;
            border: none;
            border-radius: var(--radius-small);
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .copy-btn:hover {
            background: var(--primary-purple);
        }

        /* 苹果风格的复制按钮 */
        .copy-field-btn {
            padding: 4px 8px;
            background: rgba(0, 122, 255, 0.1);
            color: var(--primary-blue);
            border: 1px solid rgba(0, 122, 255, 0.2);
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            transition: all 0.15s ease;
            margin-left: 8px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            min-width: 50px;
            justify-content: center;
        }

        .copy-field-btn:hover {
            background: rgba(0, 122, 255, 0.15);
            border-color: rgba(0, 122, 255, 0.3);
            transform: scale(1.02);
        }

        .copy-field-btn:active {
            transform: scale(0.98);
            background: rgba(0, 122, 255, 0.2);
        }

        .result-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'SF Mono', Monaco, monospace;
            word-break: break-all;
            background: rgba(0, 122, 255, 0.05);
            padding: 12px 16px;
            border-radius: var(--radius-small);
            border: 1px solid rgba(0, 122, 255, 0.1);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: var(--radius-medium);
            color: white;
            font-weight: 500;
            z-index: 9999;
            backdrop-filter: var(--blur-medium);
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            background: rgba(50, 215, 75, 0.9);
        }

        .notification.error {
            background: rgba(255, 59, 48, 0.9);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 16px;
            }

            .controls-panel, .results-panel {
                padding: 24px;
                border-radius: var(--radius-large);
            }

            .control-row {
                flex-direction: column;
                align-items: stretch;
            }

            .control-group {
                min-width: auto;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 星空背景 -->
    <div class="starfield">
        <div class="meteor"></div>
        <div class="meteor"></div>
        <div class="meteor"></div>
    </div>

    <div class="main-container">
        <!-- 头部 -->
        <header class="header">
            <h1>AccountCraft</h1>
            <p>专业的账号密码生成器，安全可靠的凭据管理工具</p>
        </header>

        <!-- 控制面板 -->
        <div class="controls-panel">
            <div class="control-row">
                <div class="control-group">
                    <label class="control-label">起始编号</label>
                    <input type="number" class="control-input" id="startNumber" value="1" min="1">
                </div>
                <div class="control-group">
                    <label class="control-label">生成数量</label>
                    <input type="number" class="control-input" id="generateCount" value="5" min="1" max="50">
                </div>
                <div class="control-group">
                    <label class="control-label">密码长度</label>
                    <input type="number" class="control-input" id="passwordLength" value="12" min="8" max="20">
                </div>
            </div>
            
            <div class="control-row">
                <button class="btn btn-primary" onclick="generateAccounts()">
                    <span>🔐</span>
                    生成账号密码
                </button>
                <button class="btn btn-success" onclick="copyAllAccounts()">
                    <span>📋</span>
                    复制全部
                </button>
                <button class="btn btn-purple" onclick="exportToFile()">
                    <span>💾</span>
                    导出文件
                </button>
                <button class="btn btn-orange" onclick="generatePasswords()">
                    <span>🔑</span>
                    重新生成密码
                </button>
                <button class="btn btn-danger" onclick="clearResults()">
                    <span>🗑️</span>
                    清空结果
                </button>
            </div>
        </div>

        <!-- 结果面板 -->
        <div class="results-panel">
            <div id="resultsContainer">
                <div style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
                    <div style="font-size: 3rem; margin-bottom: 16px;">🔐</div>
                    <div style="font-size: 18px;">点击"生成账号密码"开始创建安全凭据</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let generatedAccounts = [];

        // 生成安全随机数
        function getSecureRandom(min, max) {
            const array = new Uint32Array(1);
            crypto.getRandomValues(array);
            return (array[0] % (max - min)) + min;
        }

        // 生成安全密码
        function generateSecurePassword(length = 12) {
            const uppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const lowercaseLetters = "abcdefghijklmnopqrstuvwxyz";
            const numbers = "**********";
            const specialChars = "!@#$%^&*()-_=+[]{}:,.?/";

            // 确保每种字符类型至少包含一个
            let password = '';
            password += uppercaseLetters[getSecureRandom(0, uppercaseLetters.length)];
            password += lowercaseLetters[getSecureRandom(0, lowercaseLetters.length)];
            password += numbers[getSecureRandom(0, numbers.length)];
            password += specialChars[getSecureRandom(0, specialChars.length)];

            // 合并所有字符集
            const allChars = uppercaseLetters + lowercaseLetters + numbers + specialChars;

            // 填充剩余位置
            for (let i = 4; i < length; i++) {
                password += allChars[getSecureRandom(0, allChars.length)];
            }

            // 打乱密码字符顺序
            const passwordArray = password.split('');
            for (let i = passwordArray.length - 1; i > 0; i--) {
                const j = getSecureRandom(0, i + 1);
                [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
            }

            return passwordArray.join('');
        }

        // 生成账号密码
        function generateAccounts() {
            const startNumber = parseInt(document.getElementById('startNumber').value) || 1;
            const count = parseInt(document.getElementById('generateCount').value) || 5;
            const passwordLength = parseInt(document.getElementById('passwordLength').value) || 12;

            if (count > 50) {
                showNotification('❌ 生成数量不能超过50个！', 'error');
                return;
            }

            if (passwordLength < 8 || passwordLength > 20) {
                showNotification('❌ 密码长度必须在8-20位之间！', 'error');
                return;
            }

            generatedAccounts = [];

            for (let i = 0; i < count; i++) {
                const accountNumber = startNumber + i;
                const username = `Lgjyadmin${accountNumber}`;
                const password = generateSecurePassword(passwordLength);

                generatedAccounts.push({
                    id: accountNumber,
                    username: username,
                    password: password
                });
            }

            renderResults();
            showNotification(`✅ 成功生成 ${count} 个账号密码！`, 'success');
        }

        // 渲染结果
        function renderResults() {
            const container = document.getElementById('resultsContainer');

            if (generatedAccounts.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
                        <div style="font-size: 3rem; margin-bottom: 16px;">🔐</div>
                        <div style="font-size: 18px;">点击"生成账号密码"开始创建安全凭据</div>
                    </div>
                `;
                return;
            }

            const resultsHtml = `
                <div class="results-grid">
                    ${generatedAccounts.map(account => `
                        <div class="result-card">
                            <div class="result-header">
                                <span class="result-title">账号 #${account.id}</span>
                                <button class="copy-btn" onclick="copyAccount(${account.id})">复制全部</button>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px; display: flex; align-items: center; justify-content: space-between;">
                                    <span>用户名</span>
                                    <button class="copy-field-btn" onclick="copyUsername(${account.id})">
                                        <span>📋</span>
                                        复制
                                    </button>
                                </div>
                                <div class="result-value">${account.username}</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px; display: flex; align-items: center; justify-content: space-between;">
                                    <span>密码</span>
                                    <button class="copy-field-btn" onclick="copyPassword(${account.id})">
                                        <span>🔑</span>
                                        复制
                                    </button>
                                </div>
                                <div class="result-value">${account.password}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            container.innerHTML = resultsHtml;
        }

        // 复制单个账号
        function copyAccount(accountId) {
            const account = generatedAccounts.find(acc => acc.id === accountId);
            if (!account) return;

            const text = `用户名: ${account.username}\n密码: ${account.password}`;

            navigator.clipboard.writeText(text).then(() => {
                showNotification(`✅ 账号 #${accountId} 已复制到剪贴板！`, 'success');
            }).catch(() => {
                showNotification('❌ 复制失败，请手动复制！', 'error');
            });
        }

        // 复制用户名
        function copyUsername(accountId) {
            const account = generatedAccounts.find(acc => acc.id === accountId);
            if (!account) return;

            navigator.clipboard.writeText(account.username).then(() => {
                showNotification(`✅ 用户名已复制：${account.username}`, 'success');
            }).catch(() => {
                showNotification('❌ 复制失败，请手动复制！', 'error');
            });
        }

        // 复制密码
        function copyPassword(accountId) {
            const account = generatedAccounts.find(acc => acc.id === accountId);
            if (!account) return;

            navigator.clipboard.writeText(account.password).then(() => {
                showNotification(`✅ 密码已复制到剪贴板！`, 'success');
            }).catch(() => {
                showNotification('❌ 复制失败，请手动复制！', 'error');
            });
        }

        // 复制全部账号
        function copyAllAccounts() {
            if (generatedAccounts.length === 0) {
                showNotification('❌ 没有可复制的账号！', 'error');
                return;
            }

            const text = generatedAccounts.map(account =>
                `账号 #${account.id}:\n用户名: ${account.username}\n密码: ${account.password}`
            ).join('\n\n');

            navigator.clipboard.writeText(text).then(() => {
                showNotification(`✅ 已复制 ${generatedAccounts.length} 个账号到剪贴板！`, 'success');
            }).catch(() => {
                showNotification('❌ 复制失败，请手动复制！', 'error');
            });
        }

        // 导出到文件
        function exportToFile() {
            if (generatedAccounts.length === 0) {
                showNotification('❌ 没有可导出的账号！', 'error');
                return;
            }

            const data = {
                generatedTime: new Date().toISOString(),
                accounts: generatedAccounts
            };

            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `accounts_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification(`✅ 账号文件导出成功！`, 'success');
        }

        // 重新生成密码
        function generatePasswords() {
            if (generatedAccounts.length === 0) {
                showNotification('❌ 请先生成账号！', 'error');
                return;
            }

            const passwordLength = parseInt(document.getElementById('passwordLength').value) || 12;

            if (passwordLength < 8 || passwordLength > 20) {
                showNotification('❌ 密码长度必须在8-20位之间！', 'error');
                return;
            }

            generatedAccounts.forEach(account => {
                account.password = generateSecurePassword(passwordLength);
            });

            renderResults();
            showNotification(`✅ 已重新生成 ${generatedAccounts.length} 个密码！`, 'success');
        }

        // 清空结果
        function clearResults() {
            if (generatedAccounts.length === 0) {
                showNotification('❌ 没有需要清空的数据！', 'error');
                return;
            }

            if (confirm(`⚠️ 确定要清空所有生成的账号密码吗？\n\n这将删除 ${generatedAccounts.length} 个账号，此操作不可撤销！`)) {
                generatedAccounts = [];
                renderResults();
                showNotification('✅ 所有数据已清空！', 'success');
            }
        }

        // 显示通知
        function showNotification(message, type) {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideInRight 0.3s ease reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认值
            document.getElementById('startNumber').value = 1;
            document.getElementById('generateCount').value = 5;
            document.getElementById('passwordLength').value = 12;

            // 显示欢迎信息
            setTimeout(() => {
                showNotification('🌟 欢迎使用 AccountCraft 账号密码生成器！', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
