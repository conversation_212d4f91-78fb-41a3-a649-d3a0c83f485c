[{"id": "TC_HOME_001", "scenario": "首页地图初始化和定位功能", "preconditions": "1.网络正常\n2.用户已登录\n3.已授权位置权限", "steps": "1.打开小程序\n2.进入首页\n3.观察地图加载\n4.检查当前位置标记", "inputData": "无", "expectedResult": "1.地图正常加载\n2.显示当前位置标记\n3.地图中心定位到用户当前位置\n4.显示停车场库标记", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_002", "scenario": "位置权限拒绝处理", "preconditions": "1.网络正常\n2.用户未授权位置权限", "steps": "1.打开小程序\n2.进入首页\n3.拒绝位置权限授权\n4.观察地图显示", "inputData": "无", "expectedResult": "1.弹出位置权限提示\n2.拒绝后显示提示信息\n3.地图使用默认位置\n4.功能正常可用", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_HOME_003", "scenario": "停车场库和充电场库切换功能", "preconditions": "1.网络正常\n2.首页已加载完成", "steps": "1.点击停车场库按钮\n2.观察地图标记变化\n3.点击充电场库按钮\n4.观察地图标记变化", "inputData": "无", "expectedResult": "1.停车场库按钮高亮显示\n2.地图显示停车场标记\n3.充电场库按钮高亮显示\n4.地图显示充电站标记", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_004", "scenario": "浮动面板拖拽展开收起功能", "preconditions": "1.首页已加载完成\n2.浮动面板处于初始状态", "steps": "1.向下拖拽浮动面板手柄\n2.观察面板收起效果\n3.向上拖拽浮动面板手柄\n4.观察面板展开效果", "inputData": "无", "expectedResult": "1.面板平滑收起，只显示功能按钮\n2.轮播图隐藏\n3.面板平滑展开，显示完整内容\n4.轮播图重新显示", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_HOME_005", "scenario": "已登录用户点击停车缴费功能", "preconditions": "1.网络正常\n2.用户已登录\n3.token有效", "steps": "1.点击停车缴费按钮\n2.观察页面跳转", "inputData": "无", "expectedResult": "1.成功跳转到停车缴费查询页面\n2.页面标题显示'停车缴费查询'", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_006", "scenario": "未登录用户点击停车缴费功能", "preconditions": "1.网络正常\n2.用户未登录或token无效", "steps": "1.点击停车缴费按钮\n2.观察弹窗提示\n3.点击确认按钮", "inputData": "无", "expectedResult": "1.弹出'请先登录'提示框\n2.点击确认跳转到登录页面\n3.页面标题显示'登录'", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_007", "scenario": "扫码充电功能点击", "preconditions": "1.网络正常\n2.首页已加载完成", "steps": "1.点击扫码充电按钮\n2.观察提示信息", "inputData": "无", "expectedResult": "1.显示'暂未开放'提示\n2.提示持续2秒后消失", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_HOME_008", "scenario": "轮播图广告展示功能", "preconditions": "1.网络正常\n2.首页已加载完成\n3.有广告数据", "steps": "1.观察轮播图加载\n2.等待自动轮播\n3.手动滑动轮播图", "inputData": "无", "expectedResult": "1.轮播图正常加载显示\n2.每3秒自动切换\n3.支持手动滑动切换\n4.循环播放", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_HOME_009", "scenario": "轮播图广告数据加载失败处理", "preconditions": "1.网络异常或服务器错误\n2.首页已加载", "steps": "1.进入首页\n2.观察轮播图区域\n3.检查错误提示", "inputData": "无", "expectedResult": "1.显示'广告数据加载失败'提示\n2.轮播图区域显示空白或默认内容", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_HOME_010", "scenario": "底部导航栏-场库页面切换", "preconditions": "1.当前在首页\n2.网络正常", "steps": "1.点击底部导航栏'场库'按钮\n2.观察页面切换\n3.检查导航栏状态", "inputData": "无", "expectedResult": "1.成功切换到场库页面\n2.场库按钮高亮显示\n3.页面标题显示'场库列表'", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_011", "scenario": "底部导航栏-套餐页面切换", "preconditions": "1.当前在首页\n2.网络正常", "steps": "1.点击底部导航栏'套餐'按钮\n2.观察页面切换\n3.检查导航栏状态", "inputData": "无", "expectedResult": "1.成功切换到套餐页面\n2.套餐按钮高亮显示\n3.页面标题显示'套餐列表'", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_HOME_012", "scenario": "底部导航栏-我的页面切换", "preconditions": "1.当前在首页\n2.网络正常", "steps": "1.点击底部导航栏'我的'按钮\n2.观察页面切换\n3.检查导航栏状态", "inputData": "无", "expectedResult": "1.成功切换到个人中心页面\n2.我的按钮高亮显示\n3.页面标题显示'个人中心'", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_001", "scenario": "场库页面停车场库列表展示", "preconditions": "1.网络正常\n2.已进入场库页面\n3.默认选中停车场库", "steps": "1.观察页面加载\n2.检查场库列表\n3.查看场库信息", "inputData": "无", "expectedResult": "1.显示停车场库列表\n2.每个场库显示名称、时间、地址\n3.显示距离和咨询按钮", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_002", "scenario": "场库页面停车场库和充电场库切换", "preconditions": "1.网络正常\n2.已进入场库页面", "steps": "1.点击充电场库标签\n2.观察列表变化\n3.点击停车场库标签\n4.观察列表变化", "inputData": "无", "expectedResult": "1.充电场库标签高亮\n2.显示充电场库列表或空状态\n3.停车场库标签高亮\n4.显示停车场库列表", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_003", "scenario": "场库详情页面跳转", "preconditions": "1.网络正常\n2.场库列表已加载\n3.有可用场库", "steps": "1.点击任意场库项\n2.观察页面跳转", "inputData": "场库ID", "expectedResult": "1.成功跳转到场库详情页\n2.页面标题显示'场库详情'\n3.显示对应场库的详细信息", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_004", "scenario": "场库地图导航功能", "preconditions": "1.网络正常\n2.场库列表已加载\n3.场库有经纬度信息", "steps": "1.点击场库的距离区域\n2.观察地图应用启动", "inputData": "场库经纬度信息", "expectedResult": "1.成功打开系统地图应用\n2.显示场库位置\n3.可进行导航", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_005", "scenario": "场库电话咨询功能", "preconditions": "1.网络正常\n2.场库有联系电话\n3.设备支持拨打电话", "steps": "1.点击咨询按钮\n2.确认拨打电话\n3.观察拨号界面", "inputData": "场库联系电话", "expectedResult": "1.弹出拨打电话确认框\n2.显示电话号码\n3.确认后启动拨号界面", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_001", "scenario": "套餐页面停车套餐和充电套餐切换", "preconditions": "1.网络正常\n2.已进入套餐页面", "steps": "1.观察默认选中状态\n2.点击充电套餐标签\n3.观察页面变化\n4.点击停车套餐标签", "inputData": "无", "expectedResult": "1.默认选中停车套餐\n2.充电套餐标签高亮\n3.显示充电套餐内容\n4.停车套餐标签高亮", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_002", "scenario": "停车套餐子分类切换", "preconditions": "1.网络正常\n2.已选中停车套餐\n3.用户已登录", "steps": "1.点击普通套餐\n2.观察内容变化\n3.点击VIP套餐\n4.点击集团套餐", "inputData": "无", "expectedResult": "1.显示普通套餐列表\n2.显示VIP套餐列表\n3.显示集团套餐列表\n4.每种套餐显示对应的价格和天数", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_003", "scenario": "套餐购买流程-场库选择", "preconditions": "1.网络正常\n2.用户已登录\n3.已选择套餐类型", "steps": "1.点击场库选择区域\n2.选择目标场库\n3.确认选择", "inputData": "场库信息", "expectedResult": "1.弹出场库选择器\n2.显示可用场库列表\n3.选中场库后更新显示\n4.套餐列表根据场库更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_004", "scenario": "套餐购买流程-车辆选择", "preconditions": "1.网络正常\n2.用户已登录\n3.用户已添加车辆", "steps": "1.点击车辆选择区域\n2.选择目标车辆\n3.确认选择", "inputData": "车牌号信息", "expectedResult": "1.弹出车辆选择器\n2.显示用户车辆列表\n3.选中车辆后更新显示\n4.显示该车辆的套餐状态", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_005", "scenario": "套餐购买流程-无车辆时的处理", "preconditions": "1.网络正常\n2.用户已登录\n3.用户未添加车辆", "steps": "1.点击车辆选择区域\n2.观察提示信息\n3.点击添加车辆", "inputData": "无", "expectedResult": "1.显示'请先添加车辆'提示\n2.提供添加车辆入口\n3.跳转到添加车辆页面", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MINE_001", "scenario": "个人中心页面信息展示", "preconditions": "1.网络正常\n2.用户已登录", "steps": "1.进入个人中心页面\n2.观察用户信息展示\n3.检查功能列表", "inputData": "无", "expectedResult": "1.显示用户头像和昵称\n2.显示用户类型标签\n3.显示车辆管理和其他功能入口", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_MINE_002", "scenario": "我的车辆管理功能", "preconditions": "1.网络正常\n2.用户已登录", "steps": "1.点击车辆管理卡片\n2.观察页面跳转", "inputData": "无", "expectedResult": "1.成功跳转到我的车辆页面\n2.页面标题显示'我的车辆'\n3.显示用户的车辆列表", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_MINE_003", "scenario": "抬头管理功能", "preconditions": "1.网络正常\n2.用户已登录", "steps": "1.点击抬头管理功能\n2.观察页面跳转", "inputData": "无", "expectedResult": "1.成功跳转到抬头管理页面\n2.显示发票抬头信息\n3.支持添加和编辑抬头", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MINE_004", "scenario": "停车发票功能", "preconditions": "1.网络正常\n2.用户已登录", "steps": "1.点击停车发票功能\n2.观察页面跳转", "inputData": "无", "expectedResult": "1.成功跳转到发票管理页面\n2.显示用户的发票记录\n3.支持发票申请和查看", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MINE_005", "scenario": "已登录用户退出登录功能", "preconditions": "1.网络正常\n2.用户已登录", "steps": "1.点击退出登录功能\n2.确认退出\n3.观察状态变化", "inputData": "无", "expectedResult": "1.弹出退出确认框\n2.确认后清除登录状态\n3.显示退出成功提示\n4.页面信息更新为未登录状态", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_MINE_006", "scenario": "未登录用户点击退出登录", "preconditions": "1.网络正常\n2.用户未登录", "steps": "1.点击退出登录功能\n2.观察提示信息\n3.选择去登录", "inputData": "无", "expectedResult": "1.弹出登录提示框\n2.提示'您还未登录，是否先去登录？'\n3.确认后跳转到登录页面", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PAY_001", "scenario": "停车缴费查询页面车牌输入功能", "preconditions": "1.网络正常\n2.用户已登录\n3.已进入停车缴费查询页面", "steps": "1.点击车牌号输入框\n2.使用自定义键盘输入车牌\n3.确认输入", "inputData": "车牌号：京A12345", "expectedResult": "1.弹出自定义车牌键盘\n2.支持省份和字母数字输入\n3.车牌格式正确显示\n4.键盘关闭后保存输入", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PAY_002", "scenario": "停车缴费查询页面场库选择功能", "preconditions": "1.网络正常\n2.已进入停车缴费查询页面", "steps": "1.点击场库选择区域\n2.选择目标场库\n3.确认选择", "inputData": "场库信息", "expectedResult": "1.弹出场库选择器\n2.显示可用场库列表\n3.选中场库后更新显示\n4.缓存选择的场库信息", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PAY_003", "scenario": "停车缴费查询功能", "preconditions": "1.网络正常\n2.已输入有效车牌号\n3.已选择场库", "steps": "1.点击查询按钮\n2.观察查询结果", "inputData": "车牌号：京A12345\n场库：测试停车场", "expectedResult": "1.显示查询加载状态\n2.返回停车记录信息\n3.显示停车时间和费用\n4.提供缴费入口", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PAY_004", "scenario": "停车缴费查询无记录处理", "preconditions": "1.网络正常\n2.已输入车牌号\n3.该车牌无停车记录", "steps": "1.点击查询按钮\n2.观察查询结果", "inputData": "车牌号：京B99999", "expectedResult": "1.显示查询加载状态\n2.返回无记录提示\n3.显示'暂无停车记录'信息", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_NAVIGATION_001", "scenario": "底部导航栏状态保持", "preconditions": "1.小程序已启动\n2.网络正常", "steps": "1.在各页面间切换\n2.观察导航栏状态\n3.检查页面对应关系", "inputData": "无", "expectedResult": "1.当前页面对应的导航按钮高亮\n2.切换页面时状态正确更新\n3.图标和文字显示正确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_NETWORK_001", "scenario": "网络异常时的错误处理", "preconditions": "1.设备网络异常或断开\n2.用户尝试使用各功能", "steps": "1.断开网络连接\n2.尝试加载首页\n3.尝试使用各功能\n4.恢复网络连接", "inputData": "无", "expectedResult": "1.显示网络错误提示\n2.功能按钮显示相应错误信息\n3.网络恢复后功能正常\n4.数据重新加载成功", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PERFORMANCE_001", "scenario": "页面加载性能测试", "preconditions": "1.网络正常\n2.设备性能正常", "steps": "1.启动小程序\n2.记录首页加载时间\n3.切换各页面\n4.记录响应时间", "inputData": "无", "expectedResult": "1.首页加载时间<3秒\n2.页面切换响应时间<1秒\n3.地图加载时间<5秒\n4.无明显卡顿现象", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_COMPATIBILITY_001", "scenario": "不同设备兼容性测试", "preconditions": "1.不同型号的移动设备\n2.不同版本的微信", "steps": "1.在不同设备上启动小程序\n2.测试主要功能\n3.检查界面适配", "inputData": "设备信息", "expectedResult": "1.界面在不同屏幕尺寸下正常显示\n2.功能在不同设备上正常工作\n3.无兼容性错误", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_DATA_001", "scenario": "数据缓存和恢复功能", "preconditions": "1.用户已使用过小程序\n2.有缓存数据", "steps": "1.关闭小程序\n2.重新打开小程序\n3.检查数据恢复情况", "inputData": "缓存的用户数据", "expectedResult": "1.用户登录状态保持\n2.选择的场库信息保持\n3.车辆信息正确恢复\n4.用户偏好设置保持", "priority": "P1", "tested": false, "testResult": null}]