import request from '@/utils/request'

// 查询错误数据日志列表
export function listErrorDataLog(query) {
  return request({
    url: '/system/errorDataLog/list',
    method: 'get',
    params: query
  })
}

// 查询错误数据日志详细
export function getErrorDataLog(id) {
  return request({
    url: '/system/errorDataLog/' + id,
    method: 'get'
  })
}

// 新增错误数据日志（已注释，只保留查看功能）
// export function addErrorDataLog(data) {
//   return request({
//     url: '/system/errorDataLog',
//     method: 'post',
//     data: data
//   })
// }

// 修改错误数据日志（已注释，只保留查看功能）
// export function updateErrorDataLog(data) {
//   return request({
//     url: '/system/errorDataLog',
//     method: 'put',
//     data: data
//   })
// }

// 删除错误数据日志
export function delErrorDataLog(id) {
  return request({
    url: '/system/errorDataLog/' + id,
    method: 'delete'
  })
}

// 根据车牌号查询错误数据日志列表
export function getErrorDataLogByPlateNum(plateNum) {
  return request({
    url: '/system/errorDataLog/plateNum/' + plateNum,
    method: 'get'
  })
}

// 根据场库ID查询错误数据日志列表
export function getErrorDataLogByParkingId(parkingId) {
  return request({
    url: '/system/errorDataLog/parking/' + parkingId,
    method: 'get'
  })
}

// 根据错误码查询错误数据日志列表
export function getErrorDataLogByErrCode(errCode) {
  return request({
    url: '/system/errorDataLog/errCode/' + errCode,
    method: 'get'
  })
}

// 统计错误数据日志数量
export function countErrorDataLog(query) {
  return request({
    url: '/system/errorDataLog/count',
    method: 'get',
    params: query
  })
}

// 统计错误数据日志按错误码分组
export function countErrorDataLogByErrCode(query) {
  return request({
    url: '/system/errorDataLog/countByErrCode',
    method: 'get',
    params: query
  })
}

// 统计错误数据日志按场库分组
export function countErrorDataLogByParking(query) {
  return request({
    url: '/system/errorDataLog/countByParking',
    method: 'get',
    params: query
  })
}

// 导出错误数据日志
export function exportErrorDataLog(query) {
  return request({
    url: '/system/errorDataLog/export',
    method: 'post',
    data: query
  })
}

// 查询异常订单详细
export function getExceptionOrder(id) {
  return request({
    url: '/system/errorDataLog/' + id,
    method: 'get'
  })
}

// 处理异常订单
export function handleExceptionOrder(data) {
  return request({
    url: '/system/errorDataLog/handle',
    method: 'post',
    data: data
  })
}

// 获取异常订单统计数据
export function getExceptionOrderStatistics() {
  return request({
    url: '/system/errorDataLog/statistics',
    method: 'get'
  })
}

// 获取异常订单趋势数据
export function getExceptionOrderTrend(days) {
  return request({
    url: '/system/errorDataLog/trend',
    method: 'get',
    params: { days }
  })
}

// 获取待处理异常订单数量
export function getPendingExceptionOrderCount() {
  return request({
    url: '/system/errorDataLog/pendingCount',
    method: 'get'
  })
}


