[{"id": "TC_PACKAGE_001", "scenario": "套餐页面初始化和用户权限检查", "preconditions": "1.网络正常\n2.用户已登录\n3.已进入套餐页面", "steps": "1.观察页面加载\n2.检查用户类型识别\n3.查看可用套餐类型", "inputData": "用户类型：普通用户/VIP客户/集团客户", "expectedResult": "1.页面正常加载\n2.根据用户类型显示对应套餐选项\n3.普通用户只显示普通套餐\n4.VIP客户显示VIP套餐和普通套餐\n5.集团客户显示集团套餐和普通套餐", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_002", "scenario": "停车套餐和充电套餐切换", "preconditions": "1.网络正常\n2.套餐页面已加载完成", "steps": "1.点击停车套餐标签\n2.观察内容变化\n3.点击充电套餐标签\n4.观察内容变化", "inputData": "无", "expectedResult": "1.停车套餐标签高亮显示\n2.显示停车套餐相关内容\n3.充电套餐标签高亮显示\n4.显示充电套餐内容（暂无数据提示）", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_003", "scenario": "停车套餐子分类切换", "preconditions": "1.网络正常\n2.已选中停车套餐\n3.用户为VIP客户或集团客户", "steps": "1.点击普通套餐子分类\n2.观察内容变化\n3.点击VIP套餐或集团套餐\n4.观察内容变化", "inputData": "用户类型：VIP客户或集团客户", "expectedResult": "1.普通套餐子分类高亮\n2.显示普通套餐列表\n3.VIP/集团套餐子分类高亮\n4.显示对应的VIP/集团套餐内容", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_004", "scenario": "普通套餐列表展示和选择", "preconditions": "1.网络正常\n2.已选择普通套餐\n3.已选择场库", "steps": "1.观察套餐列表加载\n2.查看套餐信息显示\n3.点击选择套餐\n4.确认选择状态", "inputData": "场库信息", "expectedResult": "1.显示该场库的普通套餐列表\n2.每个套餐显示名称和价格\n3.选中套餐后高亮显示\n4.购买按钮变为可用状态", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_005", "scenario": "场库选择功能", "preconditions": "1.网络正常\n2.已进入套餐页面\n3.有多个可用场库", "steps": "1.点击场库选择区域\n2.选择目标场库\n3.确认选择\n4.观察套餐列表更新", "inputData": "场库列表", "expectedResult": "1.弹出场库选择器\n2.显示可用场库列表\n3.选中场库后更新显示\n4.套餐列表根据新场库刷新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_006", "scenario": "车辆选择功能", "preconditions": "1.网络正常\n2.用户已添加车辆\n3.已选择套餐类型", "steps": "1.点击车辆选择区域\n2.选择目标车辆\n3.确认选择\n4.观察车辆套餐状态", "inputData": "用户车辆列表", "expectedResult": "1.弹出车辆选择器\n2.显示用户车辆列表\n3.选中车辆后更新显示\n4.显示该车辆的套餐状态信息", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_007", "scenario": "无车辆时的处理", "preconditions": "1.网络正常\n2.用户未添加车辆\n3.尝试购买套餐", "steps": "1.点击车辆选择区域\n2.观察提示信息\n3.点击添加车辆链接", "inputData": "无", "expectedResult": "1.显示'请先添加车辆'提示\n2.提供添加车辆入口\n3.跳转到添加车辆页面", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_008", "scenario": "普通套餐购买流程", "preconditions": "1.网络正常\n2.用户已登录\n3.已选择场库、车辆和套餐", "steps": "1.点击购买按钮\n2.跳转到购买页面\n3.确认订单信息\n4.提交订单", "inputData": "套餐信息：场库、车辆、套餐类型、价格", "expectedResult": "1.成功跳转到购买页面\n2.显示正确的订单信息\n3.价格计算正确\n4.订单创建成功", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_009", "scenario": "VIP套餐购买流程", "preconditions": "1.网络正常\n2.用户为VIP客户\n3.已选择VIP套餐", "steps": "1.点击购买或续费按钮\n2.跳转到VIP购买页面\n3.确认套餐信息\n4.提交订单", "inputData": "VIP套餐信息：固定价格0.01元，1天时长", "expectedResult": "1.成功跳转到VIP购买页面\n2.显示VIP年度套餐信息\n3.价格为0.01元\n4.订单创建成功", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_010", "scenario": "集团套餐购买流程", "preconditions": "1.网络正常\n2.用户为集团客户\n3.已选择集团套餐", "steps": "1.点击购买或续费按钮\n2.跳转到集团购买页面\n3.选择套餐类型\n4.确认订单信息\n5.提交订单", "inputData": "集团套餐选项：不同天数和价格的套餐", "expectedResult": "1.成功跳转到集团购买页面\n2.显示套餐选择器\n3.可选择不同的套餐类型\n4.价格根据选择动态计算\n5.订单创建成功", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_011", "scenario": "套餐续费功能", "preconditions": "1.网络正常\n2.用户已有有效套餐\n3.套餐即将到期或已到期", "steps": "1.查看套餐状态\n2.点击续费按钮\n3.选择续费套餐\n4.确认续费信息\n5.提交续费订单", "inputData": "现有套餐信息：开始时间、结束时间", "expectedResult": "1.显示当前套餐状态\n2.续费按钮可用\n3.续费时间在原到期时间基础上延长\n4.价格计算正确\n5.续费订单创建成功", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_012", "scenario": "车辆在场状态检查", "preconditions": "1.网络正常\n2.用户车辆当前在停车场\n3.准备购买套餐", "steps": "1.选择车辆和场库\n2.系统检查车辆在场状态\n3.观察处理结果", "inputData": "车辆在场信息：入场时间、是否缴费", "expectedResult": "1.系统自动检查车辆状态\n2.如车辆在场且未缴费，提示需先缴费\n3.如车辆在场已缴费，正常购买流程\n4.套餐开始时间计算正确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_013", "scenario": "支付流程测试", "preconditions": "1.网络正常\n2.已创建套餐订单\n3.需要支付的订单", "steps": "1.确认订单信息\n2.点击支付按钮\n3.调起微信支付\n4.完成支付\n5.观察支付结果", "inputData": "订单金额、支付方式", "expectedResult": "1.订单信息显示正确\n2.成功调起微信支付\n3.支付成功后显示成功提示\n4.自动返回套餐页面\n5.套餐状态更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_014", "scenario": "支付失败处理", "preconditions": "1.网络正常\n2.已创建套餐订单\n3.支付过程中失败", "steps": "1.点击支付按钮\n2.支付过程中取消或失败\n3.观察错误处理", "inputData": "支付失败原因", "expectedResult": "1.显示支付失败提示\n2.订单状态更新为已取消\n3.用户可重新尝试购买\n4.不影响其他功能使用", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_015", "scenario": "零元支付处理", "preconditions": "1.网络正常\n2.特殊套餐价格为0或优惠后为0\n3.创建零元订单", "steps": "1.选择零元套餐\n2.提交订单\n3.观察处理流程", "inputData": "零元套餐信息", "expectedResult": "1.不调起支付界面\n2.直接显示开通成功\n3.套餐立即生效\n4.返回套餐页面", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_016", "scenario": "套餐购买记录查看", "preconditions": "1.网络正常\n2.用户已有套餐购买记录\n3.在套餐页面", "steps": "1.点击查看续费与开通记录链接\n2.观察记录页面\n3.检查记录信息", "inputData": "用户套餐购买历史", "expectedResult": "1.成功跳转到记录页面\n2.显示用户的套餐购买历史\n3.记录信息完整准确\n4.包含时间、套餐类型、金额等", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_017", "scenario": "套餐状态显示", "preconditions": "1.网络正常\n2.用户已购买套餐\n3.套餐处于不同状态", "steps": "1.查看套餐状态显示\n2.检查开始时间\n3.检查结束时间\n4.确认状态标识", "inputData": "套餐状态：有效、即将到期、已到期", "expectedResult": "1.准确显示套餐状态\n2.开始时间显示正确\n3.结束时间显示正确\n4.状态标识清晰明确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_018", "scenario": "充电套餐功能", "preconditions": "1.网络正常\n2.已选择充电套餐标签", "steps": "1.观察充电套餐内容\n2.检查功能可用性", "inputData": "无", "expectedResult": "1.显示'暂无充电套餐'提示\n2.显示空状态图标\n3.页面布局正常", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_019", "scenario": "网络异常处理", "preconditions": "1.网络异常或断开\n2.尝试加载套餐数据", "steps": "1.进入套餐页面\n2.观察加载状态\n3.检查错误提示", "inputData": "无", "expectedResult": "1.显示加载失败提示\n2.提供重试机制\n3.不影响页面基本结构", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_020", "scenario": "套餐价格计算准确性", "preconditions": "1.网络正常\n2.已选择套餐\n3.有优惠或折扣", "steps": "1.选择不同套餐\n2.检查价格显示\n3.确认计算逻辑\n4.对比实际扣费", "inputData": "套餐原价、折扣、优惠信息", "expectedResult": "1.价格显示准确\n2.折扣计算正确\n3.最终价格与实际扣费一致\n4.价格变化实时更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_021", "scenario": "套餐时间计算准确性", "preconditions": "1.网络正常\n2.购买或续费套餐\n3.有现有套餐", "steps": "1.查看套餐开始时间\n2.查看套餐结束时间\n3.验证时间计算逻辑", "inputData": "套餐天数、当前时间、现有套餐到期时间", "expectedResult": "1.新购套餐从当前时间开始\n2.续费套餐从原到期时间延续\n3.时间计算准确无误\n4.考虑车辆在场情况", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_022", "scenario": "多车辆套餐管理", "preconditions": "1.网络正常\n2.VIP或集团客户\n3.有多辆车", "steps": "1.查看多车辆套餐卡片\n2.为不同车辆购买套餐\n3.检查套餐状态\n4.验证车辆限制", "inputData": "多辆车信息、套餐限制规则", "expectedResult": "1.显示多个车辆卡片\n2.每辆车独立管理套餐\n3.套餐状态正确显示\n4.遵守车辆数量限制", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_023", "scenario": "套餐购买权限验证", "preconditions": "1.网络正常\n2.不同类型用户\n3.尝试购买不同套餐", "steps": "1.普通用户尝试购买VIP套餐\n2.观察权限检查\n3.检查错误提示", "inputData": "用户类型、套餐类型", "expectedResult": "1.系统正确识别用户权限\n2.阻止无权限的套餐购买\n3.显示相应的权限提示\n4.引导用户升级账户", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_024", "scenario": "套餐数据缓存机制", "preconditions": "1.网络正常\n2.已加载套餐数据\n3.切换页面后返回", "steps": "1.加载套餐页面\n2.切换到其他页面\n3.返回套餐页面\n4.观察数据加载", "inputData": "缓存的套餐数据", "expectedResult": "1.首次加载从服务器获取\n2.返回时优先使用缓存\n3.缓存数据准确有效\n4.必要时刷新数据", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_025", "scenario": "套餐页面性能测试", "preconditions": "1.网络正常\n2.大量套餐数据\n3.多次操作", "steps": "1.加载套餐页面\n2.快速切换套餐类型\n3.频繁选择套餐\n4.观察响应时间", "inputData": "大量套餐数据", "expectedResult": "1.页面加载时间<3秒\n2.切换响应时间<1秒\n3.无明显卡顿现象\n4.内存使用合理", "priority": "P2", "tested": false, "testResult": null}]