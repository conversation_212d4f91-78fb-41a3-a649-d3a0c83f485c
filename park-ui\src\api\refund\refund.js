import request from '@/utils/request'

// 查询退款记录列表
export function listRefund(query) {
  return request({
    url: '/system/refund/list',
    method: 'get',
    params: query
  })
}

// 查询退款记录详细
export function getRefund(id) {
  return request({
    url: '/system/refund/' + id,
    method: 'get'
  })
}

// 根据订单号查询退款记录
export function getRefundByTradeId(tradeId) {
  return request({
    url: '/system/refund/tradeId/' + tradeId,
    method: 'get'
  })
}

// 导出退款记录
export function exportRefund(query) {
  return request({
    url: '/system/refund/export',
    method: 'post',
    params: query
  })
}
