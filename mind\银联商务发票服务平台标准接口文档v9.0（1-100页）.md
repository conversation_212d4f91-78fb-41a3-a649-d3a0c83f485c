<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<table border="1" ><tr>
<td>文件编号：</td>
<td></td>
</tr><tr>
<td>当前版本：</td>
<td></td>
</tr><tr>
<td>文件状态</td>
<td>［］草稿［］修改版［O ］正式版</td>
</tr></table>

<!-- 银联商务 China ums -->
![](https://web-api.textin.com/ocr_image/external/fd8076efe79aa585.jpg)

# 银联商务发票服务平台标准接口文档

银联商务技术开发中心

2024年10月

<!-- 1 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**版本历史**

<table border="1" ><tr>
<td>版本</td>
<td>日期</td>
<td>拟稿和修改</td>
<td>说明</td>
</tr><tr>
<td>8.1 </td>
<td>2024/08/27</td>
<td>印家荣</td>
<td>更改扫码开票请求发票类型支持类别<br>新增异步开票接口</td>
</tr><tr>
<td>8.2 </td>
<td>2024/09/10</td>
<td>印家荣</td>
<td>补充版式文件下载接口响应xml 字段</td>
</tr><tr>
<td>8.3 </td>
<td>2024/09/18</td>
<td>刘明</td>
<td>1 ．直接开票、生成二维码、发票上传接口新增数电差额凭证参数invoiceBalancelnfo；<br>2 ．新增数电认证相关接口，及相关实例报文<br>3 ．删除数电红字确认单操作接口</td>
</tr><tr>
<td>8.4 </td>
<td>2024/09/29</td>
<td>印家荣</td>
<td>查验接口新增类型二手车15 </td>
</tr><tr>
<td>8.5 </td>
<td>2024/10/21</td>
<td>印家荣</td>
<td>标注查验接口03 和15 暂时无商品明细返回</td>
</tr><tr>
<td>8.6 </td>
<td>2024/10/21</td>
<td>刘明</td>
<td>数电不动产租赁开具使用3040502020200000000 税编时，<br>特定要素起止时间格式调整为年月日时分，并增加车牌号字段。<br>详见******* </td>
</tr><tr>
<td>9.7 </td>
<td>2024/11/6</td>
<td>孙泉新</td>
<td>10 . 1  新增是否数据字段</td>
</tr><tr>
<td>8.9 </td>
<td>2025/04/24</td>
<td>印家荣</td>
<td>新增查验响应机动车、二手车明细字段</td>
</tr><tr>
<td>9.0 </td>
<td>2025/06/27</td>
<td>印家荣</td>
<td>新增开票、扫码开票、待开上传特殊票种机动车、二手车明细字段</td>
</tr></table>

# 目录

前言......................................................................4

1 对接资料．................................................................5

1.1 接入参数..........................................................5

1.2 仿真测试工具.....................................................5

1.3 微信测试公众号...................................................5

1.4 电子发票样张．.....................................................6

2 通讯协议．...............................................................11

2.1 通讯方式.........................................................11

<!-- 2 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

2.2 报文签名..........................................................11

3 报文说明．...............................................................12

3.1 请求报文..........................................................12

3.2 应答报文..........................................................12

4 公共报文．...............................................................12

4.1 请求．..............................................................12

4.2 响应．..............................................................13

5 直接开票．...............................................................13

5.1 场景说明..........................................................13

5.2 开具发票..........................................................14

6 异步开票．...............................................................27

7 扫码开票．...............................................................27

7.1 场景说明.........................................................27

7.2 生成开票二维码...................................................28

8 预制二维码开票．.........................................................31

8.1 场景说明．.........................................................31

8.2 二维码规范.......................................................32

9 销项接口................................................................33

9.1 查询发票状态．.....................................................33

9.2 开票结果通知【回调】.............................................38

9.3 待开数据上传．.....................................................41

**9.4** 待开数据批量上传.................................................44

9.5 红冲发票.........................................................47

**9.6** 退货发票.........................................................50

**9.7** 作废发票.........................................................53

9.8 撤销发票.........................................................56

**9.9** 撤销恢复发票．.....................................................59

9.10 打印发票．........................................................62

9.11 推送发票．........................................................63

**9.12** 下载版式文件....................................................64

9.13 批量下载版式文件．................................................65

**9.14** 抬头模糊查询....................................................67

**9.15** 抬头上传．........................................................68

**9.16** 微信抬头识别....................................................69

**9.17** 获取微信插卡授权链接...........................................69

9.18自动插入微信卡包．................................................70

**9.19** 心跳检测．........................................................71

**9.20** 商户自定义商品信息查询.........................................72

9.21 商户开通状态查询．................................................74

**9.22** 商户库存信息查询．................................................74

**9.23** 商户用户名、密码查询状态．.......................................76

**9.24** 更新三方系统合并的子订单状态...................................77

**9.25** 红字信息表申请..................................................78

**9.26** 红字信息表查询..................................................79

<!-- 3 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

9.27 红字信息表撤销．.................................................81

9.28 全电红字确认表申请............................................82

9.29 全电红字确认表查询............................................83

10 进项接口.............................................................85

10.1 发票查验．.......................................................85

10.2 发票OCR上传接口..............................................88

10.3 发票OCR识别接口..............................................89

11 数电认证接口.........................................................91

11.1 数电登录认证获取短信验证码....................................91

11.2 数电登录认证...................................................92

11.3 获取数电身份认证二维码........................................92

11.4 全电确认是否刷脸...............................................93

12 示例报文.............................................................94

12.1 开具发票．.......................................................94

12.2 生成开票二维码.................................................97

12.3 待开数据上传...................................................98

12.4 查询发票状态..................................................100

12.5 开票结果通知【回调】..........................................102

12.6 红冲发票．......................................................104

12.7 退货发票．......................................................106

12.8 作废发票．......................................................107

12.9 打印发票．......................................................110

12.10 下载版式文件.................................................110

12.11 推送发票．.....................................................111

12.12 抬头模糊查询.................................................112

12.13 发票查验.....................................................114

12.14 数电登陆获取短信验证码．......................................116

12.15 数电登陆认证.................................................117

12.16获取数电身份认证二维码．......................................117

12.17 全电确认是否刷脸.............................................118

13 错误码说明..........................................................119

14 常见错误............................................................120

# 前言

本文档描述了银联商务发票服务平台（E开票）对外提供服务的接入形式和接口形式，供内部和外部开发者使用。

<!-- **4** -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 1 对接资料

**1.1 接入参数**

<table border="1" ><tr>
<td>名称</td>
<td>说明</td>
</tr><tr>
<td>统一接口地址ur1 </td>
<td>测试地址：<br>https://mobl-test.chinaums.com/fapiao-api-test/<br>生产地址：上线前由对接人员提供</td>
</tr><tr>
<td>报文格式format </td>
<td>JSON </td>
</tr><tr>
<td>报文编码 charset </td>
<td>UTF -8 ，请显式指定，防止中文乱码</td>
</tr><tr>
<td>消息来源msgSrc </td>
<td>测试消息来源：对接前由对接人员提供<br>生产消息来源：上线前由对接人员提供</td>
</tr><tr>
<td>签名密钥key<br>签名字符集 charset </td>
<td>签名字符集：UTF -8<br>测试签名密钥：对接前由对接人员提供<br>生产密钥：上线前由对接人员提供</td>
</tr><tr>
<td>商户终端号<br>merchantId </td>
<td>测试商户终端号：对接前由对接人员提供<br>生产商户终端号：发票云平台入网时需要配置，可</td>
</tr><tr>
<td>terminalId </td>
<td>以复用银行卡支付（或pos 通支付）的商户终端号</td>
</tr></table>

注：

1、来源系统和签名密钥参数一个系统使用一套即可；

2、商户终端号用于标识开票终端或开票点，每个商户终端对应的纳税主体（税号）可以相同也可以不同，在发票云平台进行配置

### 1.2 仿真测试工具

对接系统可以使用提供的测试页面进行仿真测试，协助开发，提高对接效率。

地址为：

https://mobl-test.chinaums.com/fapiao-test/test/index.do

### 1.3 微信测试公众号

使用微信扫码开票或者打开发票预览链接等进行测试时，都需要关注微信测试公众号（微信公众平台要求），正式环境无需关注公众号。

<!-- 5 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->


![](https://web-api.textin.com/ocr_image/external/d1ae3192375f22c0.jpg)

### 1.4 电子发票样张

单行商品的发票样张：

发票代码：03

#### 上海增值税电子普通发票 发票号码：3


![](https://web-api.textin.com/ocr_image/external/cf8694773df733c4.jpg)

开票日期：2020年01月08日


![](https://web-api.textin.com/ocr_image/external/970de74a5f629ca2.jpg)

国家税务总局

校验码：622159 515

机器编号： 661 HI

<table border="1" ><tr>
<td>购买<br>方</td>
<td colspan="10">名 称： 科技有限公司 +23/-*888/&lt;47126+&gt;/12*49+3/<br>密<br>纳税人识别号：914 8014G 1521-+/46&lt;1 2023+<br>码<br>地址、电话：广州市 三街1 号H 栋401 房 8104&gt;+ 84463&lt;971-0-/&lt;/&gt;7-<br>开户行及账号：广州 行3 001 9/7117003&lt;-14&lt;5-&lt;787&lt;2&gt;4/&lt;6<br>区</td>
</tr><tr>
<td colspan="3">货物或应税劳务、服务名称<br>＊餐饮服务＊餐饮服务<br>合 计</td>
<td>规格型号</td>
<td>单位</td>
<td>数量<br>1 </td>
<td colspan="3">单价 金额<br>45.28301887 45.28<br>¥45.28</td>
<td>税率%9</td>
<td>税额<br>2.72<br>¥2.72</td>
</tr><tr>
<td colspan="3">价税合计（大写）</td>
<td colspan="8">⊗ 肆拾捌圆整 （小写） ¥48.00<br><img src="https://web-api.textin.com/ocr_image/external/ec00afb9233c4b9a.jpg"></td>
</tr><tr>
<td colspan="2">销<br>售<br>方</td>
<td colspan="5">名 称： 餐饮管理有限公司<br>纳税人识别号：9131 3FT03<br>地址、电话：上海市徐汇区长 元021<br>开户行及账号：中国银行股份有限公司 74 </td>
<td>备注</td>
<td colspan="3"></td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/7c5334dfff72c704.jpg)

收款人：倪 复核：李园 开票人：顾 销售方：（章）

发票专用章

多行商品的发票样张：

<!-- 6 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 发票代码： 03 11 -->
![](https://web-api.textin.com/ocr_image/external/58a240cd858c5497.jpg)

# 上海增值税电子普通发票

<!-- 发票号码： 31 -->
![](https://web-api.textin.com/ocr_image/external/7ca5e7349c445fc4.jpg)


![](https://web-api.textin.com/ocr_image/external/254ee350cac95e1f.jpg)

开票日期：2020年01月07日


![](https://web-api.textin.com/ocr_image/external/e22961e52b0a1ee3.jpg)

国家税务总局

校验码：64357 2083

机器编号：66. 067

<table border="1" ><tr>
<td>购买<br>方</td>
<td colspan="6">名 称：上 限公司<br>纳税人识别号：913 4.<br>地址、电话：<br>开户行及账号：</td>
<td>密码区</td>
<td colspan="3">2、 &gt;8/6569408/7/+1&gt;/<br>2785&lt;-795++269+000 57/2<br>96&gt;/7&lt;7/4 5*613/61457&gt;<br>-3&gt;5+5+52/4-5-62&gt;/-19&lt;937+9</td>
</tr><tr>
<td colspan="3">货物或应税劳务、服务名称<br>＊移动通信设备＊华为手机<br>＊配电控制设备＊数据线<br>合 计</td>
<td>规格型号m005G 版<br>(256G) </td>
<td>单位台<br>台</td>
<td>数量<br>I 1 </td>
<td colspan="3">单价 金额<br>4690.26548673 4690.27<br>176.10619469 176.11<br>¥4866.38</td>
<td>税率13%<br>13%</td>
<td>税额<br>609.73<br>22.89<br>¥632.62</td>
</tr><tr>
<td colspan="3">价税合计（大写）</td>
<td colspan="8">⊗伍仟肆佰玖拾玖圆整 （小写） ¥5499.00<br><img src="https://web-api.textin.com/ocr_image/external/587a4fe59dd16e0c.jpg"></td>
</tr><tr>
<td colspan="2">销<br>售<br>方</td>
<td colspan="5">名 称：1 公司<br>纳税人识别号：91 17<br>地址、电话：上海市 39 号0<br>开户行及账号：招商银行上海 行12 1 </td>
<td colspan="4">备WJ 87.80 5<br>注</td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/c228d971f8205019.jpg)

收款人：林 复核： 开票人：张 销售方：（

带折扣行商品的发票样张：

<!-- 发票代码： 0 1 -->
![](https://web-api.textin.com/ocr_image/external/e10a9dfea2471494.jpg)


![](https://web-api.textin.com/ocr_image/external/5959bfaace56893a.jpg)


![](https://web-api.textin.com/ocr_image/external/16864a8266f8691b.jpg)

<table border="1" ><tr>
<td>购<br>买<br>方</td>
<td colspan="6">名 称：北京 备有限公司<br>纳税人识别号：91 /<br>地址、电话：<br>开户行及账号：</td>
<td>密码区</td>
<td colspan="2">833&gt;*8&gt;6-&gt;-84*779<br>3*89&lt;106&gt;-8- 0&lt;753&gt;&gt;&lt;<br>/44- 2+66-8&lt;3-0&lt;-6-3-81<br>/7-51&lt;+86173&lt;+8-*4+0+34&gt;7*0</td>
</tr><tr>
<td colspan="3">货物或应税劳务、服务名称<br>中油信设务为HUAWE 机NOVA6EB-12G 动通信设西为HUAWE 手机NOVA65E8-128G 合 计</td>
<td>规格型号</td>
<td>单位<br>个</td>
<td>数量<br>1 </td>
<td colspan="3">单价 金额<br>1946.01769912 1946.02<br>-1035.40<br>¥910.62</td>
<td>税率 税额<br>13% 252.98<br>13% -134.60<br>¥118.38</td>
</tr><tr>
<td colspan="3">价税合计（大写）</td>
<td colspan="7">⊗壹仟零贰拾玖圆整 （小写） ¥1029.00</td>
</tr><tr>
<td colspan="2">销<br>售<br>方</td>
<td colspan="5">名 称：北 公司<br>纳税人识别号：91<br>地址、电话：北京市 号3 号楼 01<br>开户行及账号：北京 行01090359 2 </td>
<td colspan="3">备 2<br>注</td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/53dea1a0958daf7c.jpg)


![](https://web-api.textin.com/ocr_image/external/d1d23e915486df35.jpg)

带销货清单（商品超过8行）的发票样张：

<!-- 7 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 发票代码： 0 -->
![](https://web-api.textin.com/ocr_image/external/adfe15d69a6d5903.jpg)

# 上海增值税电子普通发票


![](https://web-api.textin.com/ocr_image/external/43a1044b8879bd80.jpg)

发票号码：40

国家税务总局


![](https://web-api.textin.com/ocr_image/external/015b6a82d07cadfc.jpg)

开票日期：2020年01月07日

机器编号：49 5 校验码： 0 529

<table border="1" ><tr>
<td>购<br>买方</td>
<td colspan="4">名 称： 上海 公司<br>纳税人识别号：913 5133<br>地址、电话： 林泉路 572<br>开户行及账号： 建行 01574 749 </td>
<td>密码区</td>
<td colspan="4">4273666+3501969&gt;9978<br>01+9-1&gt;*92/140* 1/260172&lt;<br>191+50 43163*7*152*3440/<br>42+99-060401916419703&lt;0&lt;&lt;577</td>
</tr><tr>
<td colspan="2">货物或应税劳务、服务名称<br>（详见销货清单）<br>合 计</td>
<td>规格型号</td>
<td colspan="2">单位 数量</td>
<td colspan="2">单价</td>
<td>金额<br>1416.65<br>¥1416.65</td>
<td>税率</td>
<td>税额<br>25.30<br>¥25.30</td>
</tr><tr>
<td colspan="2">价税合计（大写）</td>
<td colspan="8">⊗壹仟肆佰肆拾壹圆玖角伍分 （小写）¥1441.95</td>
</tr><tr>
<td>销售<br>方</td>
<td colspan="4">名 称：上海 限公司<br>纳税人识别号：913 313<br>地址、电话：上海市闵行 3331 室<br>开户行及账号：中国工商银行 :0012427 7 </td>
<td>备注</td>
<td colspan="4">913 59R </td>
</tr><tr>
<td colspan="2">收款人： 配</td>
<td>复核：</td>
<td></td>
<td colspan="3">开票人： -</td>
<td>) 销售方</td>
<td>： （章）</td>
<td></td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/e039dc2e843266a1.jpg)

<!-- 8 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

销售货物或者提供应税劳务清单

购买方名称：上 限公司

销售方名称：上 公司

<table border="1" ><tr>
<td colspan="2">所属增值税普通发票代码：031 </td>
<td colspan="7">号码：40 共1 页第1 页</td>
</tr><tr>
<td>序号</td>
<td>货物（劳务）名称</td>
<td>规格型号</td>
<td>单位</td>
<td>数量</td>
<td>单价</td>
<td>金额</td>
<td>税率</td>
<td>税额</td>
</tr><tr>
<td>1 </td>
<td>“肉及肉”，牛、羊、鸡、鸭、鹅、冷、肉</td>
<td></td>
<td>Kg </td>
<td>0.234 </td>
<td>120 </td>
<td>28.08 </td>
<td>0%</td>
<td>***</td>
</tr><tr>
<td>2 </td>
<td>“两及肉”，牛、、、鸭、、冷、肉</td>
<td>kg </td>
<td>kg </td>
<td>0.318 </td>
<td>1980 </td>
<td>629.64 </td>
<td>0%</td>
<td>***</td>
</tr><tr>
<td>3 </td>
<td>“及肉”，、、鸡、鸭、鹅、冷、肉</td>
<td>kg </td>
<td>kg </td>
<td>0.278 </td>
<td>1980 </td>
<td>550.44 </td>
<td>0%</td>
<td>***</td>
</tr><tr>
<td>4 </td>
<td>＊方便食品＊即食方便食品</td>
<td></td>
<td>百</td>
<td>0.28 </td>
<td>352.212389381 </td>
<td>98.62 </td>
<td>13%</td>
<td>12.82 </td>
</tr><tr>
<td>5 </td>
<td>＊方便食品＊即食方便食品</td>
<td></td>
<td>kg </td>
<td>0.272 </td>
<td>352.192868298 </td>
<td>95.80 </td>
<td>13%</td>
<td>12.45 </td>
</tr><tr>
<td>6 </td>
<td>＊蔬菜＊芹菜等叶菜类蔬菜</td>
<td>250g </td>
<td>g </td>
<td>1 </td>
<td>13.8 </td>
<td>13.80 </td>
<td>0%</td>
<td>***</td>
</tr><tr>
<td>7 </td>
<td>＊塑料制品＊塑料丝、绳、袋及编织品</td>
<td></td>
<td>个</td>
<td>1 </td>
<td>0.26548672566 </td>
<td>0.27 </td>
<td>13%</td>
<td>0.03 </td>
</tr><tr>
<td>8 </td>
<td>＊预付卡销售＊预付卡销售和充值</td>
<td>0g </td>
<td>张</td>
<td>5 </td>
<td>0.1 </td>
<td>0.50 </td>
<td>不征税</td>
<td>***</td>
</tr><tr>
<td>9 </td>
<td>＊预付卡销售＊预付卡销售和充值</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>-0.50 </td>
<td>不能税</td>
<td>***</td>
</tr><tr>
<td>小计合计</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>1416.65<br>1416.65 </td>
<td></td>
<td>25.30<br>25.30 </td>
</tr><tr>
<td>备注</td>
<td colspan="8"></td>
</tr></table>

销售方（章）： 开票日期： 2020年01月07日


![](https://web-api.textin.com/ocr_image/external/ae088d2ed47a1fe7.jpg)

成品油的发票样张：

<!-- 9 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 发票代码： 04 11 -->
![](https://web-api.textin.com/ocr_image/external/ca4556da6866d985.jpg)

## 湖北增值税电子普通发票 发票号码：3.


![](https://web-api.textin.com/ocr_image/external/acba6cd95448a8d2.jpg)

成品油

开票日期：2020年01月08日


![](https://web-api.textin.com/ocr_image/external/621dda11e09a1da9.jpg)

国家税务总局

校验码：82 42

机器编号： 66

<table border="1" ><tr>
<td>购<br>买<br>方</td>
<td colspan="5">名 称：荆 有限公司<br>纳税人识别号：914 71 1<br>地址、电话：荆门市泉 1 0<br>开户行及账号：建行期 支行4200！ 01 </td>
<td colspan="5">密<br>/60*0/6*6&lt;8-005<br>码<br>7&gt;/96+5*&lt;436&lt;* /96-8<br>8429*6 0/6/+/0*69+4&gt;4*+96<br>区<br>979/1/&gt;9660/&gt;572*-51*24*0&lt;-</td>
</tr><tr>
<td colspan="2">货物或应税劳务、服务名称<br>＊汽油＊汽92 ＃<br>合 计</td>
<td>规格型号</td>
<td>单位升</td>
<td>数量<br>36.43 </td>
<td colspan="3">单价<br>6.19469027 </td>
<td>金额<br>225.67<br>¥225.67</td>
<td>税率13%</td>
<td>税额<br>29.34<br>¥29.34</td>
</tr><tr>
<td colspan="2">价税合计（大写）</td>
<td colspan="9">⊗ 贰佰伍拾伍圆零壹分 （小写） ¥255.01<br><img src="https://web-api.textin.com/ocr_image/external/45b4eba86e7c3a05.jpg"></td>
</tr><tr>
<td rowspan="2">销<br>售<br>方</td>
<td colspan="5">名 称：汉 公司</td>
<td rowspan="2">备注</td>
<td colspan="4" rowspan="2"></td>
</tr><tr>
<td>纳税人识别号： J7BU 地址、电话：汉川市仙女山街<br>开户行及账号：中国工商银行股份</td>
<td>U6Q<br>支行</td>
<td>交181 </td>
<td>汇处0 </td>
<td></td>
</tr><tr>
<td colspan="2">收款人：<br><img src="https://web-api.textin.com/ocr_image/external/5beca18db5668323.jpg"></td>
<td>复核：陕</td>
<td></td>
<td>开</td>
<td colspan="3">票人：陈<br><img src="https://web-api.textin.com/ocr_image/external/7e763a4fbfa5c394.jpg"></td>
<td>销售方：（章</td>
<td colspan="2">914205<br>）</td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/4d1a92a021101f18.jpg)

差额征税的发票样张：

发票代码：03100

## 上海增值税电子普通发票 发票号码：38


![](https://web-api.textin.com/ocr_image/external/c257e046936c5142.jpg)

开票日期：2020年01月07日


![](https://web-api.textin.com/ocr_image/external/971be4f017394deb.jpg)

国家税务总局

校验码：04 5852

机器编号： 58. 20

<table border="1" ><tr>
<td>购<br>买方</td>
<td colspan="2">名 称：上海 所（ ( 26&gt;8/&gt;+684&gt;94406<br>密<br>纳税人识别号：913 1090 64/+*4+5-87-/1*<br>码<br>地址、电话：上海市松 -- 10 幢 563&lt;&gt; 06137-91+/2&gt;13/*446<br>开户行及账号：中 -东支行02 W -8-9&lt;-5/68019205197-499&lt;-278<br>区</td>
</tr><tr>
<td colspan="2">货物或应税劳务、服务名称<br>＊旅游服务＊住宿费<br>合 计</td>
<td>规格型号 单位 数量 单价 金额 税率 税额<br>次 1 9783.36 9783.36 *** 16.64<br>¥9783.36</td>
</tr><tr>
<td colspan="2">价税合计（大写）</td>
<td>⊗ 玖仟捌佰圆整 （小写） ¥9800.00</td>
</tr><tr>
<td>销售<br>方</td>
<td colspan="2">名 称：上海 有限公司<br>纳税人识别号：913 33<br>地址、电话：上海市长宁区通 021-<br>开户行及账号：上海 长宁支行0 ． 7 </td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/70ff81081612ec7f.jpg)

收款人：倪 复核：边 开票人：倪 销售方：（

免税的发票样张：

<!-- 10 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

发票代码：0

## 湖南增值税电子普通发票 发票号码：18


![](https://web-api.textin.com/ocr_image/external/4d99093c998e5aab.jpg)

开票日期：2020年07月06日


![](https://web-api.textin.com/ocr_image/external/e8dc43433dd4946d.jpg)

国家税务总局

校验码：56

机器编号： （票）

<table border="1" ><tr>
<td>购买方</td>
<td colspan="6">名 称：华 湖南分公司<br>纳税人识别号：91430 23151<br>地址、电话：<br>开户行及账号：</td>
<td>密码区</td>
<td colspan="4">//&gt;+0*33&gt;1**87195+90&gt;/42/8*<br>45/41-8/*2/1*&gt;6-18-&lt;68-&gt;3+2<br>9*36*96+0+*&lt;2-18&gt;1&gt;589*3/1<br>3*0827&gt;41&gt;///319&lt;*6-5*&lt;8&gt;&lt;&lt;</td>
</tr><tr>
<td colspan="3">货物或应税劳务、服务名称<br>＊餐饮服务＊餐费<br>合 计</td>
<td>规格型号</td>
<td>单位</td>
<td>数量<br>1 </td>
<td colspan="3">单价<br>1519 </td>
<td>金额<br>1519.00<br>¥1519.00</td>
<td>税率免税</td>
<td>税额<br>***<br>***</td>
</tr><tr>
<td colspan="3">价税合计（大写）</td>
<td colspan="9">⊗ 壹仟伍佰壹拾玖圆整 (小写) ¥1519.00</td>
</tr><tr>
<td colspan="2">销<br>售<br>方</td>
<td colspan="5">名 称：国<br>纳税人识别号：9 1<br>地址、电话： 858<br>开户行及账号：中国 -</td>
<td>备注</td>
<td colspan="4"></td>
</tr></table>


![](https://web-api.textin.com/ocr_image/external/449567a6a1d08e62.jpg)

<!-- 收款人： -->
![](https://web-api.textin.com/ocr_image/external/cbc450d72033927e.jpg)

复核：桐 开票人：王 销售方：（

发票专用章

## 2 通讯协议

### 2.1 通讯方式

$$\text {HTTPS+JSON}$$

### 2.2 报文签名

报文请求和应答需要经过签名验签，签名数据放在报文的sign字段里，具体签名算法如下：

签名生成的通用步骤如下：

1、设所有发送或者接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到大排序（字典序），使用URL键值对的格式（即2⋯拼接成字符串 stringA。

特别注意以下重要规则：

（1）参数名ASCII码从小到大排序（字典序）；

（2）如果参数的值为空不参与签名；

（3）参数名区分大小写；

（4）对于JSON子域（JSONObject和JSONArray)，签名时整个子域作为字符串参与签名，需要注意的是fastjson会对字段重新排序，建议用json-lib进行序列化。

（5）验证调用返回或主动通知签名时，传送的sign参数不参与签名，将生成的签名与该sign值作校验。

（6）接口可能增加字段，验证签名时必须支持增加的扩展字段

<!-- 11 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

2、在stringA最后拼接上key得到stringSignTemp字符串，并对stringSignTemp进行SHA-256运算，再将得到的字符串所有字符转换为大写，得到sign值signValue。

## 3 报文说明

### 3.1 请求报文

报文包含msgType, msgld, msgSrc, srcReserve,requestTimestamp 几个基本字段，所有报文都应正确填写这四个基本字段。

msgType为业务类型字段，用来区分不同的请求。

msgld表示消息ID，类型String，发送方用来唯一标识一个请求，应答报文中会原样返回，通常用来作为唯一key来匹配请求报文，用在TCP长连接模式。

srcReserve是发送方自定义域，会原样返回。

requestTimestamp为请求时间，发票服务平台会检查时间的合法性。

### 3.2 应答报文

应答报文resultCode代表本次请求的受理状态，如果请求被正确受理，就返回成功SUCCESS，如果未被受理，返回对应的错误码和错误信息。返回信息在resultMsg字段。msgType,msgld,srcReserve 三个字段会被原样返回，另外平台还会返回responseTimestamp。

## 4 公共报文

请求和响应的公共报文，所有接口都要带上。

**4.1 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>msgId </td>
<td>消息ID </td>
<td>String(1,64) </td>
<td>消息的唯一标识，应答报文会原样返回。<br>可以使用消息来源系统的流水号，或使用UUID 随机生成。</td>
<td>Y </td>
</tr><tr>
<td>msgSrc </td>
<td>消息来源</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>msgType </td>
<td>消息类型</td>
<td>String(1,64) </td>
<td>业务种类</td>
<td>Y </td>
</tr><tr>
<td>requestTimest amp </td>
<td>请求时间</td>
<td>Date </td>
<td>yyyy-MM-dd HH:mm:ss</td>
<td>Y </td>
</tr><tr>
<td>srcReserve </td>
<td>预留信息</td>
<td>String(1,255) </td>
<td>请求系统预留字段，可以存放扩展信息，应答报文中会原样返回。格式由发送方自定义，建议用JSON 或者分隔符格式以便于扩展。</td>
<td>N </td>
</tr></table>

<!-- 12 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**4.2 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>msgId </td>
<td>消息ID </td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>msgSrc </td>
<td>消息来源</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>msgType </td>
<td>消息类型</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>resultCode </td>
<td>错误码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>错误信息</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>responseTimes tamp </td>
<td>应答时间</td>
<td>Date </td>
<td>yyyy-MM-dd HH:mm:ss</td>
<td>Y </td>
</tr><tr>
<td>srcReserve </td>
<td>预留信息</td>
<td>String(1,255) </td>
<td></td>
<td>N </td>
</tr></table>

## 5 直接开票

### 5.1 场景说明

1、直接开票适用于对接系统的任何线上开票的场景，自行开发开票页面即可。

2、对接系统需要对接直接开票接口。如果需要开票结果，则需要对接发票状态查询或开票结果通知【回调】接口，建议采用回调为主，查询为辅的方式。

3、直接开票接口支持幂等，支持已关闭发票重新发起开票。

可参考时序图如下：

<!-- 13 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 交易客户端 交易后台系统 发票平台 交易 交易请求 消费者 交易结果 开票 消费者／商户 开票请求 开票请求 开票请求结果 开票请求结果 开票请求异步处理 短信／邮件推送电 子发票 消费者 1）开票结果通知 获取开票结果的两种方式： 每隔n秒 2）查询发票状态 开票结果 -->
![](https://web-api.textin.com/ocr_image/external/a8c283d23c339fde.jpg)

### 5.2 开具发票

#### 5.2.1 消息类型

### issue

#### 5.2.2 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceMater ial </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT<br>支持以下类型的发票：电子普通发票／电子专用发票／纸质普通发票／纸质增值税专用发票／数电普票／数电专票</td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelepho ne </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBrevity Code </td>
<td>买方简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerGroupBr evityCode </td>
<td>买方集团简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>amount </td>
<td>含税总金额</td>
<td>Long </td>
<td>单位为分</td>
<td>Y </td>
</tr><tr>
<td>deductionAmo unt </td>
<td>扣除额</td>
<td>Long </td>
<td>单位为分<br>为空：普通征税<br>不为空：差额征税<br>扣除额必须小于开票金额</td>
<td>N </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,150 00) </td>
<td>为空：使用平台配置的单个默认商品（注：对接系统最好自行上送，灵活性更高，不要依赖平台默认配置，如有多个商品，也必须通过接口上送）<br>不为空：单个商品或多个商品，参考5.2.2.1 节</td>
<td>N </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td>可填写房间号或桌号，体现在票面备注栏</td>
<td>N </td>
</tr><tr>
<td>notifyMobile No </td>
<td>消费者手机号，<br>用于短信通知电票</td>
<td>String(1,16) </td>
<td>测试环境暂时无短信资源，收不到发票短信，请用邮件接收</td>
<td>N </td>
</tr><tr>
<td>notifyEMail </td>
<td>消费者邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td>推送手机号和邮箱，必填一项，前端自行控制</td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地址，<br>用于开票结果通知</td>
<td>String(1,256 ) </td>
<td>商户接收开票结果通知的地<br>址，按开票结果通知接口开发</td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td>要求自动插入微信卡包时<br>merWxAppId 和merWxOrderId<br>必填，由商户平台进行授权，<br>开票平台进行插卡</td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>alipayUserId </td>
<td>用户支付宝<br>userId，用于支付宝插卡</td>
<td>String(1,16) </td>
<td>要求自动插入支付宝卡包时<br>alipayUserId 必填，由商户平台进行授权，开票平台进行插卡</td>
<td>N </td>
</tr><tr>
<td>storeId </td>
<td>门店号</td>
<td>String(1,32) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>operateId </td>
<td>营运ID </td>
<td>String(1,18) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>vehicleNo </td>
<td>车牌号</td>
<td>String(1,10) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>outTradeNo </td>
<td>三方自定义的订单号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notFaceRemar k </td>
<td>非票面备注</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>specialInvoi ceFlag </td>
<td>特殊票种标识</td>
<td>String(2) </td>
<td>00 普通发票（默认）<br>全电发票支持：03  建筑服务发票，04 货物运输发票，05 不动产销售服务发票，06 不动产租赁服务发票，09  旅客运输发票，81 成品油发票，14 机动车，15 二手车</td>
<td>N </td>
</tr><tr>
<td>invoiceSpeci alInfo </td>
<td>特殊票种信息</td>
<td>String(1,150 00) </td>
<td>Json 列表，全电发票特殊票种标志为03、04、05、06、09时必填<br>，具体信息参考5 . 2 . 2 . 3 节</td>
<td>N </td>
</tr><tr>
<td>confirmIssue </td>
<td>全电成品油单价过低是否确认开具标识</td>
<td>String(2) </td>
<td>0 ：否，1 ：是，默认为0 （备注：专票单价过低强制开票会记入异常发票，红冲需要在电子税 务局申请解除然后再红冲；普票单价过低开具成功发票可以正常红冲）</td>
<td>N </td>
</tr><tr>
<td>invoiceBalan ceInfo </td>
<td>数电发票差额扣除额凭证明细</td>
<td>JsonArray </td>
<td>数电开票，当deductionAmount 不为空时必填，最大支持100<br>行<br>具体信息参考5 . 2 . 2 . 4 节</td>
<td>N </td>
</tr></table>

<!-- 14 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 15 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 16 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

##### 5.2.2.1商品明细

商品明细goodsDetail的格式是JSONArray，把每件商品的描述作为一个JSONObject加入JSONArray。单件商品描述信息以下两种方式，每项参数以key-value方式存入JSONObject。

<table border="1" ><tr>
<td rowspan="2">参数<br>index </td>
<td rowspan="2">名称<br>行号</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>Integer </td>
<td>行号，从1 开始</td>
<td>Y </td>
</tr><tr>
<td>attribute </td>
<td>发票行性质</td>
<td>String(1) </td>
<td>0 ：正常行<br>1 ：折扣行<br>2 ：被折扣行</td>
<td>Y </td>
</tr><tr>
<td>discountIndex </td>
<td>折行对应行号</td>
<td>Integer </td>
<td>有折扣时必填，为另一行的行号index ，形成交叉对应</td>
<td>N </td>
</tr><tr>
<td>name </td>
<td>商品名称</td>
<td>String(1,64) </td>
<td>折扣行与被折扣行一致<br>注：商品名称可以自定义，但不要超出该商品分类范围，票面上会自动附上商品分类</td>
<td>Y </td>
</tr><tr>
<td>sn </td>
<td>商品编码</td>
<td>String(19) </td>
<td>参考国税总局出具的《税收分类编码》.xls<br>注：必须使用文件中19 位的税收分类编码，对接系统的商品库中如果没有该编码，需要自行进行映射维护</td>
<td>Y </td>
</tr><tr>
<td>taxRate </td>
<td>税率</td>
<td>Double </td>
<td>支持整数和小数，如6 、6 . 0 、1 . 5<br>等</td>
<td>Y </td>
</tr><tr>
<td>priceIncluding Tax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元<br>折扣行为负数</td>
<td>Y </td>
</tr><tr>
<td>quantity </td>
<td>数量</td>
<td>Double </td>
<td>1 ．若不传单价，则数量默认为1 ；<br>2 ．若传单价，则后台根据单价计算数量</td>
<td>N </td>
</tr><tr>
<td>unitPriceInclu dingTax </td>
<td>含税单价</td>
<td>Double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>unit </td>
<td>单位</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>model </td>
<td>规格型号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>freeTaxType </td>
<td>免税类型</td>
<td>String(1) </td>
<td>空：正常非零税率<br>0 ：出口退税<br>1 ：免税<br>2 ：不征税<br>3 ：普通零税率</td>
<td>N </td>
</tr><tr>
<td>preferPolicyF1 ag </td>
<td>是否使用优惠政策</td>
<td>String(1) </td>
<td>0：否<br>1：是</td>
<td>N </td>
</tr><tr>
<td>vatSpecial </td>
<td>增值税特殊管理</td>
<td>String(1,64) </td>
<td>取值参考5 . 2 . 2 . 2 节</td>
<td>N </td>
</tr></table>

<!-- 17 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**若已在银商云平台商品档案中维护好商品信息并设置好商品简码，则单件商品描述信**息可以按如下简化方式上传。

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>index </td>
<td>行号</td>
<td>Integer </td>
<td>行号，从1 开始</td>
<td>Y </td>
</tr><tr>
<td>attribute </td>
<td>发票行性质</td>
<td>String(1) </td>
<td>0 ：正常行<br>1 ：折扣行，在云平台配置单价为负数，编码名称与被折扣一致。<br>2 ：被折扣行</td>
<td>Y </td>
</tr><tr>
<td>discountIndex </td>
<td>折行对应行号</td>
<td>Integer </td>
<td>有折扣时必填，为另一行的行号index ，形成交叉对应</td>
<td>N </td>
</tr><tr>
<td>priceIncluding Tax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元<br>折扣行为负数</td>
<td>Y </td>
</tr><tr>
<td>brevityCode </td>
<td>商品简码</td>
<td>String(1,32) </td>
<td>云平台商品档案管理中维护</td>
<td>Y </td>
</tr></table>

《税收分类编码》.xls：

可以找对接人员提供，商品明细中的商品名称，商品编码，商品税率等需要参考该表进行设置。

<table border="1" ><tr>
<td></td>
<td></td>
<td>B </td>
<td>C </td>
<td>D </td>
<td>E </td>
<td></td>
<td>G </td>
<td>H </td>
<td>I </td>
<td>J </td>
<td>K </td>
<td>L </td>
<td>M </td>
<td></td>
</tr><tr>
<td>1 </td>
<td>日</td>
<td>HBEM </td>
<td>MC </td>
<td>SM </td>
<td>SLV </td>
<td>FLBMJC </td>
<td>ZZSZCYJ </td>
<td>ZZSTSNRDW </td>
<td>XIFSGL </td>
<td>XFSZCYJ </td>
<td>XFSTSNRDF </td>
<td>GJZ </td>
<td>HZX </td>
<td>TJ </td>
</tr><tr>
<td>2 </td>
<td></td>
<td>1000000000000000000 </td>
<td>货物</td>
<td></td>
<td></td>
<td>货物</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td></td>
</tr><tr>
<td></td>
<td>101 </td>
<td>1010000000000000000 </td>
<td>农、林、牧、渔业类产品</td>
<td></td>
<td></td>
<td>产品</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>01 </td>
</tr><tr>
<td></td>
<td>10101 </td>
<td>1010100000000000000 </td>
<td>据名称，可以自定</td>
<td>税率</td>
<td></td>
<td>农业品商，会自</td>
<td>动附在票</td>
<td>面上</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td></td>
</tr><tr>
<td></td>
<td>10100 品</td>
<td>1010101006000000000 编码</td>
<td>-</td>
<td>包括稻</td>
<td></td>
<td>谷物如＊谷物＊玉米</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>10 </td>
</tr><tr>
<td></td>
<td>101010101 </td>
<td>1010101010000000000 </td>
<td></td>
<td>包括早籼和</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>稻谷、早机</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>7 </td>
<td>101010101 </td>
<td>1010101020000000000 </td>
<td>小麦</td>
<td>包括硬质小</td>
<td>10％</td>
<td>谷物</td>
<td>＜财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>小麦、硬目</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>8 </td>
<td>101010100 </td>
<td>1010101030000000000 </td>
<td>玉米</td>
<td>包括白玉米</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>00010116 </td>
<td>06、000101</td>
<td>0504 </td>
<td></td>
<td>玉米、白3 </td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td></td>
<td>10101010-</td>
<td>1010101040000000000 </td>
<td>谷子</td>
<td>包括硬谷于</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>谷子、硬名</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>10 </td>
<td>101010105 </td>
<td>050000000000 </td>
<td>高粱</td>
<td>包括红粒高</td>
<td>10％</td>
<td>谷物</td>
<td>＜财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>高粱、红杉</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>11 </td>
<td>10101010 </td>
<td>60000000000<br>101010106000 </td>
<td>大麦</td>
<td>包括裸大多</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>大麦、裸</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>12 </td>
<td>10101010 </td>
<td>1010101070000000000 </td>
<td>燕麦</td>
<td>包括裸燕差</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>裸燕麦、B </td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>13 </td>
<td>10101010 </td>
<td>1010101080000000000 </td>
<td>黑麦</td>
<td></td>
<td>10%</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>黑麦</td>
<td>N </td>
<td>14 </td>
</tr><tr>
<td>14 </td>
<td>101010101 </td>
<td>1010101090000000000 </td>
<td>转费</td>
<td>包括甜荞</td>
<td>10％</td>
<td>谷物</td>
<td>《财政部</td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>荞麦、甜产</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>15 </td>
<td>10101019 </td>
<td>1010101990000000000 </td>
<td>其他谷物</td>
<td>包括糜子</td>
<td>10%</td>
<td>合物</td>
<td>《财政部 </td>
<td>1011606.0 </td>
<td></td>
<td></td>
<td></td>
<td>糜子、硬面</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>16 </td>
<td>1010102 </td>
<td>1010102000000000000 </td>
<td>鉴类</td>
<td>包括马铃签</td>
<td>、木墨、甘薯、</td>
<td>层类</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>10 </td>
</tr><tr>
<td>22 </td>
<td>10101020 </td>
<td>1010102010000000 </td>
<td>马铃薯</td>
<td>又称土豆、</td>
<td>10％</td>
<td>警类</td>
<td>《财政部</td>
<td>、1010504.0</td>
<td></td>
<td></td>
<td></td>
<td>马铃薯、</td>
<td></td>
<td>10 </td>
</tr><tr>
<td>18 </td>
<td>101010202 </td>
<td>1010102020000000000 </td>
<td>木墨</td>
<td>包括鲜木3 </td>
<td>10％</td>
<td>墨类</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>木墨、鲜才</td>
<td>N </td>
<td>10 </td>
</tr><tr>
<td>19 </td>
<td>10101020 </td>
<td>01010102030000000000 </td>
<td>雷日</td>
<td>又称红薯</td>
<td>10%</td>
<td>薯类</td>
<td>《财政部</td>
<td>、1010504.0</td>
<td></td>
<td></td>
<td></td>
<td>甘鉴、红薯</td>
<td>N </td>
<td>11 </td>
</tr><tr>
<td>20 </td>
<td>10101029 </td>
<td>1010102990000000000 </td>
<td>其他薯类</td>
<td>指其他未列</td>
<td>10％</td>
<td>警类</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>N </td>
<td>三</td>
</tr><tr>
<td>21 </td>
<td>1010103 </td>
<td>1010103000000000000 </td>
<td>油料作物</td>
<td>包括花生、</td>
<td>油菜籽、芝麻、</td>
<td>其油料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>江</td>
</tr><tr>
<td>22 </td>
<td>101010301 </td>
<td>010102010000000000<br>10101030100 </td>
<td>花生</td>
<td>包括带壳花</td>
<td>NOT </td>
<td>油料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>花生、带罗</td>
<td>N </td>
<td>/</td>
</tr><tr>
<td>23 </td>
<td>101010302 </td>
<td>1010103020000000000 </td>
<td>油菜籽</td>
<td>包括双低汇</td>
<td>10％</td>
<td>油料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>油菜籽、X </td>
<td>N </td>
<td></td>
</tr><tr>
<td>24 </td>
<td>101010300 </td>
<td>1010103030000000000 </td>
<td>芝麻</td>
<td>包括白芝用</td>
<td>10％</td>
<td>油料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>芝麻、黑芝</td>
<td>N </td>
<td>1 </td>
</tr><tr>
<td>25 </td>
<td>10101039 </td>
<td>1010103990000000000 </td>
<td>其他油料</td>
<td>其他含油于</td>
<td>10％</td>
<td>油料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>含油子仁、</td>
<td>N </td>
<td>ϊ</td>
</tr><tr>
<td>26 </td>
<td>1010104 </td>
<td>1010104000000000000 </td>
<td>干豆类</td>
<td>指脱美的干</td>
<td>豆；带英的鲜豆</td>
<td>牙豆</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td></td>
</tr><tr>
<td>27 </td>
<td>101010401 </td>
<td>1010104010000000000 </td>
<td>大豆</td>
<td>包括黄大E </td>
<td>10％</td>
<td>干豆</td>
<td>＜财政部</td>
<td>000101160 </td>
<td>6、000101</td>
<td>9905 </td>
<td></td>
<td>大豆、黄</td>
<td>力</td>
<td>1 </td>
</tr><tr>
<td>28 </td>
<td>10101040 </td>
<td>01010104020000000000 </td>
<td>绿豆</td>
<td>包括明绿E </td>
<td>10％</td>
<td>干豆</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>绿豆、明N </td>
<td></td>
<td>1<br>!</td>
</tr><tr>
<td>29 </td>
<td>10101049 </td>
<td>1010104990000000000 </td>
<td>其他杂豆</td>
<td>其他杂豆、</td>
<td>10％</td>
<td>千豆</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>小豆、干奶</td>
<td></td>
<td>12 </td>
</tr><tr>
<td>30 </td>
<td>1010105 </td>
<td>1010105000000000000 </td>
<td>棉花</td>
<td>不论是否转</td>
<td>10％</td>
<td>棉花</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>棉花、籽植</td>
<td></td>
<td>2 </td>
</tr><tr>
<td>31 </td>
<td>1010106 </td>
<td>1010106000000000000 </td>
<td>生麻</td>
<td>包括生亚A </td>
<td>10％</td>
<td>主麻</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>生麻、生I </td>
<td>N </td>
<td></td>
</tr><tr>
<td>32 </td>
<td>1010107 </td>
<td>1010107000000000000 </td>
<td>糖料</td>
<td>包括甘蔗、</td>
<td>甜菜、其他糖料</td>
<td>精料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>12 </td>
</tr><tr>
<td>33 </td>
<td>10101070 </td>
<td>1010107010000000000 </td>
<td>甘蔗</td>
<td></td>
<td>10%</td>
<td>精料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>甘蔗</td>
<td>N </td>
<td></td>
</tr><tr>
<td>34 </td>
<td>101010702 </td>
<td>1010107020000000000 </td>
<td>甜菜</td>
<td></td>
<td>10%</td>
<td>精料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>甜菜</td>
<td>N </td>
<td></td>
</tr><tr>
<td>35 </td>
<td>10101079 </td>
<td>1010107990000000000 </td>
<td>其他糖料</td>
<td></td>
<td>10%</td>
<td>精料</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>N </td>
<td></td>
</tr><tr>
<td>36 </td>
<td>1010108 </td>
<td>1010108000000000000 </td>
<td>未加工烟草</td>
<td>指未去模的</td>
<td>烟草，包括未去</td>
<td>在草</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>A </td>
<td>12 </td>
</tr><tr>
<td>37 </td>
<td>101010801 </td>
<td>1010108010000000000 </td>
<td>未去梗烤烟叶</td>
<td>指初烤未去</td>
<td>10％</td>
<td>烟草</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>未去梗烤炬</td>
<td>N </td>
<td></td>
</tr><tr>
<td>38 </td>
<td></td>
<td>0108990000000000 </td>
<td>其他未加工烟草</td>
<td>指其他未加</td>
<td>10％</td>
<td>烟草</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>N </td>
<td></td>
<td>1. </td>
</tr><tr>
<td>39 </td>
<td>1010109 </td>
<td>1010109000000000000 </td>
<td>饲料作物</td>
<td>包括茴蓿、</td>
<td>青饲料、饲料牧</td>
<td>饲料作物</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>Y </td>
<td>13 </td>
</tr><tr>
<td>40 </td>
<td>101010901 </td>
<td>1010109010000000000 </td>
<td>饲料牧草</td>
<td>包括茴覆于</td>
<td>10％</td>
<td>饲料作物</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>饲料牧草</td>
<td>、N</td>
<td>13 </td>
</tr><tr>
<td>41 </td>
<td>101010995 </td>
<td>1010109990000000000 </td>
<td>其他饲料作物</td>
<td>包沃青馆折</td>
<td>10％</td>
<td>恒料代物</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>首酸、竹</td>
<td>N </td>
<td></td>
</tr></table>

<!-- 18 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

##### 5.2.2.2优惠政策

商品明细中的免税类型、是否使用优惠政策、增值税特殊管理和税率等的取值可参考如下，最终以税局出具的商品和服务税收分类与编码文档为准。

<table border="1" ><tr>
<td>免税类型<br>freeTaxType </td>
<td>空</td>
<td>0 </td>
<td>1 </td>
<td>2 </td>
<td>3 </td>
</tr><tr>
<td>是否使用优惠政策<br>preferPolicyFlag </td>
<td>0 ：增值税特殊管理为空<br>1 ：增值税特殊管理不为空</td>
<td>1 </td>
<td>1 </td>
<td>1 </td>
<td>0 </td>
</tr><tr>
<td>增值税特殊管理<br>vatSpecial </td>
<td>空<br>超税负3 ％即征即退超税负8 ％即征即退超税负12 ％即征即退<br>简易征收<br>按3 ％简易征收<br>按5 ％简易征收<br>按5 ％简易征收减按1.5％计征<br>即征即退30 ％<br>即征即退50 ％<br>即征即退70 ％<br>即征即退100 ％<br>50 ％先征后退<br>100 ％先征后退<br>稀土产品</td>
<td>出口退税</td>
<td>免税</td>
<td>不征税</td>
<td>空</td>
</tr><tr>
<td>税率<br>taxRate </td>
<td>6<br>10<br>16 </td>
<td>0 </td>
<td>0 </td>
<td>0 </td>
<td>0 </td>
</tr></table>

##### *******特殊票种信息

特殊票种信息invoiceSpeciallnfo的格式是JSONArray，把每条信息作为一个JSONObject 加入JSONArray。单条信息每项参数以key-value方式存入JSONObject，不同特殊票种传入对应的信息。特殊票种信息格式参照（不动产租赁）：

]

{

"leaseHoldDateEnd": "2023-02-21",

＂leaseDetailAddress":＂新华街道迎宾大道179号＂，

"leaseAreaUnit":"㎡",

<!-- 19 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"leaseCrossSign": "N",

"leaseHoldDateStart": "2023-02-21",

"leasePropertyNo": "100********",

＂leaseAddress":＂广东省＆广州市＆花都区”，

" carNumbers ":""

}

]

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>leasePropertyNo </td>
<td>不动产租赁-房屋产权证书／不动产权证号码</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>leaseAddress </td>
<td>不动产租赁-不动产地址</td>
<td>String(1,120) </td>
<td>按照省、市、区／县三级传值，以＆符间隔，举例“北京市＆东城区、河北省＆石家庄市＆正定县”（不动产地 址和详细地址之和为120 ）</td>
<td>Y </td>
</tr><tr>
<td>leaseDetailAddr ess </td>
<td>不动产租赁-详细地址</td>
<td>String(1,120) </td>
<td>“北京市海淀区清华东路17<br>号”（不动 产地址和详细地址之和为120）</td>
<td>Y </td>
</tr><tr>
<td>leaseCrossSign </td>
<td>不动产租赁-跨地（市）标志</td>
<td>String(1,8) </td>
<td>Y：是；N：否</td>
<td>Y </td>
</tr><tr>
<td>leaseAreaUnit </td>
<td>不动产租赁-面积单位</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>leaseHoldDateSt art </td>
<td>不动产租赁-租赁起始日期</td>
<td>String(16) </td>
<td>格式：yyyy-MM-dd<br>3040502020200000000（车辆停放服务）商编时，格式为：yyyy-MM-dd HH:mm</td>
<td>Y </td>
</tr><tr>
<td>leaseHoldDateEn d </td>
<td>不动产租赁-租赁结束日期</td>
<td>String(16) </td>
<td>格式：yyyy-MM-dd<br>3040502020200000000（车辆停放服务）商编时，格式为：yyyy-MM-dd HH:mm</td>
<td>Y </td>
</tr><tr>
<td>carNumbers </td>
<td>不动产租赁-车牌号</td>
<td>String(10) </td>
<td>3040502020200000000（车辆停放服务）商编时可填写，其他商编填写无效</td>
<td>N </td>
</tr><tr>
<td>buildingLocalAd dress </td>
<td>建筑服务特定要素-建筑服务发生地</td>
<td>String(1,120) </td>
<td>按照省、市、区／县三级传值，以＆符间隔，举例“北京市＆东城区、河北省＆石家庄市＆正定县”（建筑服务发生地和详细地址之和为120 ）</td>
<td>Y </td>
</tr><tr>
<td>buildingDetailA ddress </td>
<td>建筑服务特定要素-建筑服务详细地址</td>
<td>String(1,120) </td>
<td>举例“北京市海淀区清华东路17 号”（建筑服务发生地和详细地址之和为120 ）</td>
<td>Y </td>
</tr><tr>
<td>buildingName </td>
<td>建筑服务特定要素-建筑项目名称</td>
<td>String(1,80) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>buildingLandTax No </td>
<td>建筑服务特定要素-土地增值税项目编号</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buildingCrossSi gn </td>
<td>建筑服务特定要素-跨地（市）标志</td>
<td>String(1,8) </td>
<td>Y：是；N：否</td>
<td>Y </td>
</tr><tr>
<td>transportDepart ure </td>
<td>货物运输特定要素-启运地</td>
<td>String(1,80) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>transportArrive </td>
<td>货物运输特定要素-到达地</td>
<td>String(1,80) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>transportToolTy pe </td>
<td>货物运输特定要素-运输工具种类</td>
<td>String(1,40) </td>
<td>铁路运输、公路运输、水路运输、航空运输、管道运输</td>
<td>Y </td>
</tr><tr>
<td>transportToolNu m </td>
<td>货物运输特定要素-运输工具牌号</td>
<td>String(1,40) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>transportGoodsN ame </td>
<td>货物运输特定要素-运输货物名称</td>
<td>String(1,80) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>propertyPropert yNo </td>
<td>不动产销售服务-房屋产权证书／不动产权证号码</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>propertyAddress </td>
<td>不动产销售服务-不动产地址</td>
<td>String(1,120) </td>
<td>按照省、市、区／县三级传值，以＆符 间隔，举例“北京市＆东城区、河北省＆石家庄市＆正定县”（不动产 地址和详细地址之和为120 ）</td>
<td>Y </td>
</tr><tr>
<td>propertyDetailA ddress </td>
<td>不动产销售服务-详细地址</td>
<td>String(1,120) </td>
<td>“北京市海淀区清华东路17<br>号”（不 动产地址和详细地址之和为120）</td>
<td>Y </td>
</tr><tr>
<td>propertyContrac tNo </td>
<td>不动产销售服务-不动产单元代码／网签合同备案编码</td>
<td>String(1,28) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>propertyLandTax No </td>
<td>不动产销售服务-土地增值税项目编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>propertyCrossSi gn </td>
<td>不动产销售服务-跨地（市）标志</td>
<td>String(1,8) </td>
<td>Y：是；N：否</td>
<td>Y </td>
</tr><tr>
<td>propertyAreaUni t </td>
<td>不动产销售服务-面积单位</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>propertyApprove dPrice </td>
<td>不动产销售服务-核定计税价格</td>
<td>BigDecimal(24, 6) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>propertyDealPri ce </td>
<td>不动产销售服务-实际成交含税金额</td>
<td>BigDecimal(24, 6) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>carriageName </td>
<td>出行人</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>carriageId </td>
<td>出行人证件类型</td>
<td>String(1,4) </td>
<td>出行人证件类型，101 ：组织机构代码证；<br>102 ：营业执照；103 ：税务登记证；199 ：其它单位证件；201 ：居民身份证；202 ：军官证；203：武警警官证；204：士兵证；205：<br>军队离退休干部证；206 ：残疾人证；207 ：<br>残疾军人证（1 -8 级）;208 ：外国护照；210 ：<br>港澳居民来往内地通行证；212 ：中华人民共和国往来港澳通行证；213 ：台湾居民来往大陆通行 证；214 ：大陆居民往来台湾通行证；215 ：外国人居留证；216 ：外交官证；217 ：使（领事）馆证；219 ：香港永久性居民身份证；218 ：海员证；220 ：台湾身份证；221 ：澳门特别行政区永久性居民身份证；<br>222 ：外国 人身份证件；224 ：就业失业登记证；225：退休证；226：离休证；227：中 国护照；228 ：城镇退役士兵自谋职业证；229 ：<br>随军家属身份证明；230 ：中国人民解放军军官专业证书；231 ：中国人民解放军义务兵退 出现役证；232 ：中国人民解放军士官退出现役证；233 ：外国人永久居 留身份证（外国人永久居留证）;234 ：就业创业证；235 ：香港特别行 政区护照；236 ：澳门特别行政区护照；237 ：中华人民共和国港澳居民身份证；238 ：中华人民共和国台湾居民身份证；239 ：《中华人民共和 国外国人工作许可证》（A 类）;240 ：《中华人民共和国外国人工作 许可证》（B 类）;241 ：《中华人民共和国外国人工作许可证》（C 类）；291 ：医学出生证明；299 ：其他个人证件；</td>
<td>Y </td>
</tr><tr>
<td>carriageIdNo </td>
<td>出行人证件号码</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>carriageDateYmd </td>
<td>出行日期</td>
<td>String(1,10) </td>
<td>yyyy-MM-dd </td>
<td>Y </td>
</tr><tr>
<td>carriageLeave </td>
<td>出发地 省市区</td>
<td>String(1,100) </td>
<td>按照省、市、区／县三级传值，以＆符号间隔，“北京市＆东城区、河北省＆石家庄市＆正定县”</td>
<td>Y </td>
</tr><tr>
<td>carriageLeaveAd dress </td>
<td>出发地 详细地址</td>
<td>String(1,100) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>carriageArrive </td>
<td>到达地 省市区</td>
<td>String(1,100) </td>
<td>按照省、市、区／县三级传值，以＆符号间隔，“北京市＆东城区、河北省＆石家庄市＆正定县”</td>
<td>Y </td>
</tr><tr>
<td>carriageArriveA ddress </td>
<td>到达地 详细地址</td>
<td>String(1,100) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>carriageVehicle Type </td>
<td>交通工具类型</td>
<td>String(2) </td>
<td>1 ：飞机；2 ：火车；3 ：长途汽车；4 ：公共交通；5 ：出租车；6 ：汽车；7 ：船舶；9 ：其他</td>
<td>Y </td>
</tr><tr>
<td>carriageVehicle Grade </td>
<td>交通工具等级</td>
<td>String(1,16) </td>
<td>仅当交通工具种类为“飞机、火车、船舶”时，必填；飞机：公务舱、头等舱、经济舱 火车：一等座、二等座、软席（软座、软卧）、硬席（硬座、硬卧）船舶：一等舱、二等舱、三等舱</td>
<td>N </td>
</tr><tr>
<td>originPlace </td>
<td>产地</td>
<td>String(1,10) </td>
<td>机动车发票，产地</td>
<td>N </td>
</tr><tr>
<td>vehicleType </td>
<td>车辆类型</td>
<td>String(1,40) </td>
<td>机动车发票，车辆类型（二手车发票车辆类型也用该字段）</td>
<td>Y </td>
</tr><tr>
<td>vehicleNo </td>
<td>车架号</td>
<td>String(1,23) </td>
<td>机动车发票，车架号（二手车发票车架号也用该字段）</td>
<td>Y </td>
</tr><tr>
<td>brandModel </td>
<td>厂牌型号</td>
<td>String(1,60) </td>
<td>机动车发票，厂牌型号（二手车发票厂牌型号也用该字段）</td>
<td>Y </td>
</tr><tr>
<td>certificateNo </td>
<td>合格证号</td>
<td>String(1,50) </td>
<td>机动车发票，合格证号（国产车必传）</td>
<td>N </td>
</tr><tr>
<td>importCertifica teNo </td>
<td>进口证明</td>
<td>String(1,16) </td>
<td>机动车发票，进口证明（进口车必传）</td>
<td>N </td>
</tr><tr>
<td>inspectionListN O </td>
<td>商检单号</td>
<td>String(1,32) </td>
<td>机动车发票，商检单号</td>
<td>N </td>
</tr><tr>
<td>tonnage </td>
<td>吨位</td>
<td>String(1,8) </td>
<td>机动车发票，吨位</td>
<td>N </td>
</tr><tr>
<td>passengerCapaci ty </td>
<td>限乘人数</td>
<td>String(1,12) </td>
<td>机动车发票，限乘人数</td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,80) </td>
<td>二手车发票，卖方名称</td>
<td>Y </td>
</tr><tr>
<td>sellerTaxNo </td>
<td>卖方税号</td>
<td>String(1,22) </td>
<td>二手车发票，卖方税号</td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,80) </td>
<td>二手车发票，卖方地址</td>
<td>Y </td>
</tr><tr>
<td>sellerPhone </td>
<td>卖方电话</td>
<td>String(1,20) </td>
<td>二手车发票，卖方电话</td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方银行</td>
<td></td>
<td>二手车发票，卖方银行</td>
<td>N </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td></td>
<td>二手车发票，卖方账号</td>
<td>N </td>
</tr><tr>
<td>licensePlate </td>
<td>车牌号码</td>
<td>String(1,20) </td>
<td>二手车发票，车牌号码</td>
<td>Y </td>
</tr><tr>
<td>registryNo </td>
<td>登记证号</td>
<td>String(1,20) </td>
<td>二手车发票，登记证号</td>
<td>Y </td>
</tr><tr>
<td>vehicleAdminist ration </td>
<td>车辆管理所名称</td>
<td>String(1,80) </td>
<td>二手车发票，车辆管理所名称</td>
<td>Y </td>
</tr><tr>
<td>reverseIssueMar k </td>
<td>反向开具标志</td>
<td>String(1) </td>
<td>二手车发票，反向开具标志，0 -正常开具，1 -反向开具</td>
<td>Y </td>
</tr></table>

<!-- 20 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 21 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 22 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

##### 5.2.2.4数电差额凭证明细信息

数电差额凭证明细信息 invoiceBalancelnfo的格式是JSONArray，把每条信息作为一个JSONObject加入JSONArray。单条信息每项参数以key-value方式存入JSONObject。凭证明细信息格式参照：

]

{

<!-- 23 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"balanceVoucherNo":"",

"balanceType": "01",

"balanceDeductAmount": 20,

"balanceNo": "1",

"balancelssueDate": "2024-06-04",

"balancelnvoiceNo":"",

"balanceTotalAmount": 20,

"balanceElectricNo": "24442000000236071682",

"balancelnvoiceCode": "",

＂balanceRemarks":＂备注＂

｝了

]

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>balanceNo </td>
<td>凭证明细行号</td>
<td>String(1,10) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>balanceType </td>
<td>凭证类型</td>
<td>String(2) </td>
<td>01  全电发票、02 增值税专用发票、03 增值税普通发票、04<br>营业税发票、05 财政票据、06<br>法院裁决书、07 契税完税凭证、08  其他发票类、09 其他扣除凭证</td>
<td>Y </td>
</tr><tr>
<td>balanceElectricN O </td>
<td>全电发票号码</td>
<td>String(1,20) </td>
<td>凭证类型为01 时必填</td>
<td>N </td>
</tr><tr>
<td>balanceInvoiceCo de </td>
<td>发票代码</td>
<td>String(1,12) </td>
<td>凭证类型为02 、03 、04 时必填</td>
<td>N </td>
</tr><tr>
<td>balanceInvoiceNo </td>
<td>发票号码</td>
<td>String(1,8) </td>
<td>凭证类型为02 、03 、04 时必填</td>
<td>N </td>
</tr><tr>
<td>balanceVoucherNo </td>
<td>凭证号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>balanceIssueDate </td>
<td>开具日期</td>
<td>String(10) </td>
<td>凭证类型为01、02、03、04时必填<br>格式：yyyy-MM-dd</td>
<td>N </td>
</tr><tr>
<td>balanceTotalAmou nt </td>
<td>凭证合计金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>balanceDeductAmo unt </td>
<td>本次扣除金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>balanceRemarks </td>
<td>备注</td>
<td>String(1,100) </td>
<td>当凭证类型为08 其他发票类、09  其他扣除凭证时，必填</td>
<td>N </td>
</tr></table>

#### 5.2.3 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>ISSUING：开具中</td>
<td>Y </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td>ISSUED：已开具<br>CLOSED：已关闭<br>SPLITED：已拆分</td>
<td></td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBrevityC ode </td>
<td>买方简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerGroupBre vityCode </td>
<td>买方集团简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>outTradeNo </td>
<td>三方自定义的订单号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notFaceRemark </td>
<td>非票面备注</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUr1 </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>ofdUr1 </td>
<td>OFD 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>ofdPreviewUrl </td>
<td>OFD 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>xmlUr1 </td>
<td>XML 文件地址</td>
<td>String(1,128 ) </td>
<td>只有数电（即全电）渠道会返回此链接</td>
<td>N </td>
</tr><tr>
<td>xmlPreviewUrl </td>
<td>XML 预览链接</td>
<td>String(1,128 ) </td>
<td>只有数电（即全电）渠道会返回此链接</td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的相同，但订单号以及金额等信息不同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 24 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 25 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 26 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

#### 6 异步开票

##### 6.1.1 接口说明

异步开票接口，在接收到用户开票信息后，后台进行订单校验，校验通过后直接返回开票申请成功（注意这里仅代表请求成功而不是开票成功）及基本响应结果（版式文件地址，待开票成功即可下载），开票成功结果会回调，或则商户通过查询接口获得开票结果。

##### 6.1.2 消息类型

**async.issue**

##### 6.1.3 请求

参考5.2.2节请求参数，与之相同。

**6.1.4 响应**

参考5.2.3节响应参数，与之相同。

#### 7 扫码开票

**7.1 场景说明**

1、扫码开票适用于出具交易小票给消费者的场景，在交易小票上打印开票二维码。

2、对接系统需要对接生成开票二维码接口。如果需要开票结果，则需要对接发票状态查询或开票结果通知【回调】接口，建议采用回调为主，查询为辅的方式。

可参考时序图如下：

<!-- 27 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 交易客户端 交易后台系统 发票平台 交易 交易请求 消费者 交易结果 生成开票二维码 请求 开票二维码 开票二维码 打印带开票二维码 的小票 扫码开票 开票请求异步处理 消费者 短信／邮件推送电 子发票 1）开票结果通知 每隔n秒 获取开票结果的两种方式： 1）开票结果通知 2）发票状态查询 2）查询发票状态 开票结果 -->
![](https://web-api.textin.com/ocr_image/external/54b8b0a105967162.jpg)

##### 7.2 生成开票二维码

###### 7.2.1 消息类型

**get.qrcode**

<!-- 28 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**7.2.2** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT<br>支持以下类型的发票：电子普通发票／电子专用发票／纸质普通发票／纸质增值税专用发票／数电普票／数电专票</td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd ，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>Y </td>
</tr><tr>
<td>amount </td>
<td>开票金额</td>
<td>Long </td>
<td>单位为分</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Long </td>
<td>单位为分<br>为空：普通征税<br>不为空：差额征税<br>扣除额必须小于开票金额</td>
<td>N </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,1500 0) </td>
<td>为空：使用平台配置的单个默认商品（注：对接系统最好自行上送，灵活性更高，不要依赖平台默认配置，如有多个商品，也必须通过接口上送）<br>不为空：单个商品或多个商品，参考5.2.2.1 节</td>
<td>N </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128) </td>
<td>可填写房间号或桌号，体现在票面备注栏</td>
<td>N </td>
</tr><tr>
<td>expireTime </td>
<td>过期时间</td>
<td>Integer </td>
<td>单位为分钟。表示生成二维码后N 分钟内不扫码，二维码自动过期。不设置，则表示不过期</td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地址，用于开票结果通知</td>
<td>String(1,256) </td>
<td>商户接收开票结果通知的地址，按开票结果通知接口开发</td>
<td>N </td>
</tr><tr>
<td>receiveMobile No </td>
<td>消费者接收二维码手机号</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>receiveEmail </td>
<td>消费者接收二维码邮箱</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeId </td>
<td>门店号</td>
<td>String(1,32) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>operateId </td>
<td>营运ID </td>
<td>String(1,18) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>vehicleNo </td>
<td>车牌号</td>
<td>String(1,10) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>outTradeNo </td>
<td>三方自定义的订单号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notFaceRemark </td>
<td>非票面备注</td>
<td>String(1,256) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>specialInvoic eFlag </td>
<td>特殊票种标识</td>
<td>String(2) </td>
<td>00 普通发票（默认）<br>全电发票支持：03  建筑服务发票，04 货物运输发票，05 不动产销售服务发票，06 不动产租赁服务发票，09 旅客运输发票，81 成品油发票，14 机动车，15 二手车</td>
<td>N </td>
</tr><tr>
<td>invoiceSpecia 1Info </td>
<td>特殊票种信息</td>
<td>String(1,1500 0) </td>
<td>Json 列表，全电发票特殊票种标志为03、04、05、06、09时必填，具体信息参考5 . 2 . 2 . 3 节</td>
<td>N </td>
</tr><tr>
<td>confirmIssue </td>
<td>全电成品油单价过低是否确认开具标识</td>
<td>String(2) </td>
<td>0 ：否，1 ：是，默认为0<br>（备注：专票单价过低强制开票会记入异常发票，红冲需要在电子税务局申请解除然后再红冲；普票单价过低开具成功发票可以正常红冲）</td>
<td>N </td>
</tr><tr>
<td>invoiceBalanc eInfo </td>
<td>数电发票差额扣除额凭证明细</td>
<td>JsonArray </td>
<td>数电开票，当deductionAmount 不为空时必填，最大支持100 行具体信息参考5 . 2 . 2 . 4 节</td>
<td>N </td>
</tr></table>

<!-- 29 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**7.2.3** **响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码编号</td>
<td>String(1,40) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>qrCode </td>
<td>二维码</td>
<td>String(1,256) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>shotQrCode </td>
<td>二维码短地址</td>
<td>String(1,256) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>auditQrCode </td>
<td>审核开票二维码</td>
<td>String(1,256) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>auditShortQrC ode </td>
<td>审核开票二维码短地址</td>
<td>String(1,256) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>expireDate </td>
<td>二维码过期时间</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>status </td>
<td>二维码状态</td>
<td>String(1,32) </td>
<td>PENDING：待开具CLOSED：已关闭<br>CANCELED：已撤销<br>ISSUING：开具中<br>ISSUED：已开具<br>REVERSING：红冲中<br>REVERSED：已红冲<br>SPLITED：已拆分</td>
<td>Y </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 30 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

#### 8 预制二维码开票

##### 8.1 场景说明

1、预制二维码开票也适用于出具交易小票给消费者的场景，在交易小票上打印开票二维码。但该方案允许对接系统按照二维码规范自行生成开票二维码，然后将待开发票数据通过接口上传到发票平台，消费者扫码即可开票。

2、对接系统需要对接二维码规范、待开数据上传接口。如果需要开票结果，则需要对接发票状态查询或开票结果通知【回调】接口，建议采用回调为主，查询为辅的方式。

可参考时序图如下：

<!-- 31 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 交易客户端 交易后台系统 发票平台 交易 交易请求 消费者 交易结果 根据规范打印带开 票二维码的小票 待开数据生成 待开数据上传 上传结果 扫码开票 开票请求异步处理 消费者 短信／邮件推送电 子发票 1）开票结果通知 每隔n秒 获取开票结果的两种方式： 2）发票状态查询 2）查询发票状态 开票结果 -->
![](https://web-api.textin.com/ocr_image/external/a3099cba59277dd8.jpg)

##### 8.2 二维码规范

###### 8.2.1 格式说明

开票二维码内容为HTTPS链接，格式如下：

https://mobl-test.chinaums.com/fapiao-portal/pre_issue.do?id=2018060454581f f6d4a94b828e9b9cfef4cebe90&checkCode=FF3C73AF&source=EASY_HOME_DIGITAL

其中mobl-test.chinaums.com为二维码域名，fapiao-portal代表发票服务平台上下文，pre_issue.do是开票场景动作，id为二维码唯一id,checkCode为校验码，source为来源系统。

<!-- 32 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**8.2.2 字段定义**

<table border="1" ><tr>
<td>id </td>
<td>checkCode </td>
<td>source </td>
</tr><tr>
<td>二维码唯一id ，要求格<br>式：8 位的订单日期<br>（merOrderDate)+32位的uuid ，保证全局唯一<br>注意：该二维码id 与待<br>开数据上传接口的<br>qrCodeld 保持一致，否则会匹配不到数据</td>
<td>二维码校验码，用于检验二维码的合法性，根据指定算法计算</td>
<td>来源系统，用于标识对接系统</td>
</tr></table>

###### 8.2.3 校验码算法

根据分配的key，对id+key拼接得到的字符串进行sha256运算，再将得到的字符串所有字符转换为大写（64位），去除前28和后28个字符，取中间8个字符，得到checkCode。

**8.2.4 参数**

<table border="1" ><tr>
<td>系统环境</td>
<td>域名</td>
<td>校验码 Key </td>
<td>source </td>
</tr><tr>
<td>测试</td>
<td>mobl-test.chinaums.co<br>m </td>
<td>43b80ac78ead4ac0a5f8d292d9 86e18e </td>
<td>由对接人员提供</td>
</tr><tr>
<td>生产</td>
<td>上线前提供</td>
<td>上线前提供</td>
<td>与测试环境一致</td>
</tr></table>

#### 9 销项接口

##### 9.1 查询发票状态

###### 9.1.1 接口说明

该接口用于查询发票状态和发票明细信息，对接系统可以通过该接口跟踪发票的状态，并且可以获取已开发票的票面信息，作为进一步的使用。

###### 9.1.2 消息类型

**query**

<!-- 33 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**9.1.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

**9.1.4** **响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>PENDING：待开具<br>CLOSED：已关闭<br>CANCELED：已撤销<br>ISSUING：开具中<br>ISSUED：已开具<br>REVERSING：红冲中<br>REVERSED：已红冲<br>INVALIDING：作废中<br>INVALIDED：已作废<br>SPLITED：已拆分</td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯-id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueQrCode </td>
<td>开票二维码</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 34 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 35 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.1.5 发票状态说明

<table border="1" ><tr>
<td>发票状态</td>
<td>状态名称</td>
<td>涉及发票类型</td>
<td>是否可进行开票</td>
<td>状态转换过程</td>
<td>状态过程说明</td>
</tr><tr>
<td>PENDING </td>
<td>待开具</td>
<td>电票／普票／专票</td>
<td>是</td>
<td>-&gt; PENDING </td>
<td>发票初始状态，生成开票二维码或待开数据上传后发票的状态</td>
</tr><tr>
<td>CLOSED </td>
<td>已关闭</td>
<td>电票／普票／专票</td>
<td>是</td>
<td>PENDING-&gt;<br>ISSUING-&gt;<br>CLOSED </td>
<td>发票开具失败后的状态</td>
</tr><tr>
<td>CANCELED </td>
<td>已撤销</td>
<td>电票／普票／专票</td>
<td>否</td>
<td>PENDING-&gt;<br>CANCELED<br>或<br>CLOSED-&gt;<br>CANCELED </td>
<td>待开或开票失败的发票可进行撤销，撤销后无法进行开票，可用于限制消费者进行开票</td>
</tr><tr>
<td>ISSUING </td>
<td>开具中</td>
<td>电票／普票／专票</td>
<td>否</td>
<td>PENDING-&gt;<br>ISSUING </td>
<td>开票处理中间状态</td>
</tr><tr>
<td>ISSUED </td>
<td>已开具</td>
<td>电票／普票／专票</td>
<td>是，幂等</td>
<td>PENDING-&gt;<br>ISSUING-&gt;<br>ISSUED </td>
<td>开票成功状态</td>
</tr><tr>
<td>REVERSING </td>
<td>红冲中</td>
<td>电票／普票／专票</td>
<td>否</td>
<td>ISSUED-&gt;<br>REVERSING </td>
<td>红冲处理中间状态</td>
</tr><tr>
<td>REVERSED </td>
<td>已红冲</td>
<td>电票／普票／专票</td>
<td>是，幂等</td>
<td>ISSUED-&gt;<br>REVERSING-&gt;<br>REVERSED </td>
<td>红冲成功状态</td>
</tr><tr>
<td>INVALIDING </td>
<td>作废中</td>
<td>普票／专票</td>
<td>否</td>
<td>ISSUED-&gt;<br>INVALIDING<br>或<br>REVERSED-&gt;<br>INVALIDING </td>
<td>作废处理中间状态</td>
</tr><tr>
<td>INVALIDED </td>
<td>已作废</td>
<td>普票／专票</td>
<td>是，幂等</td>
<td>ISSUED-&gt;<br>INVALIDING-&gt;<br>INVALIDED<br>或<br>REVERSED-&gt;<br>INVALIDING-&gt;<br>INVALIDED </td>
<td>作废成功状态</td>
</tr><tr>
<td>SPLITED </td>
<td>已拆分</td>
<td>电票／普票／</td>
<td>否</td>
<td>无</td>
<td>已拆分的订单母订单状态</td>
</tr></table>

<!-- 36 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

状态转换过程图：

<!-- 37 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 撤销恢复请求 撤销请求 已撤销 CANCELED 撤销请求 开具失败 已关闭 CLOSED 待开具 PENDING 开具请求 开具中 ISSUING 重新发起开票 红冲请求 红冲中 红冲成功 已红冲 REVERSING REVERSED 红冲失败 开具成功 已开具 ISSUED 作废失败 作废请求 作废中 作废成功 已作废 INVALIDING INVALIDED -->
![](https://web-api.textin.com/ocr_image/external/1921de78dbf59933.jpg)

##### 9.2 开票结果通知【回调】

###### 9.2.1 接口说明

1、该接口由对接系统开发，并提供接口地址（通过开票接口上送［notifyUrl])，由发票平台在开票完成后进行调用，如果在调用过程出现网络不通、网络超时等异常，那么会重试3次，之后停止调用。

2、该接口需要支持HTTP/HTTPS+POST通讯，数据格式为application/json;charset=UTF-8。

3、该接口将推送发票状态（稳定状态）和发票明细信息，对接系统可以根据实际需要使用其中的参数。

4、对接系统可根据需要对回调报文进行验签（sign字段），以验证报文的合法性和完整性。

5、由于发票平台正式环境网络限制，非80和443端口默认不通，因此尽量使用80或443端口，如只能使用其他端口，请提前告知对接人员提交网络变更，变更周期一般为一周。

###### 9.2.2请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>ISSUED：已开具REVERSED：已红冲<br>INVALIDED：已作废<br>CLOSED：已关闭<br>CANCELED：已撤销<br>SPLITED：已拆分</td>
<td>Y </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256) </td>
<td>开票报错说明</td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b8 28e9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,1024) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>source </td>
<td>来源系统</td>
<td>String(1,32) </td>
<td>同msgSrc </td>
<td>Y </td>
</tr><tr>
<td>sign </td>
<td>签名值</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr><tr>
<td>mergedInvoice S </td>
<td>被合并的订单信息</td>
<td>JsonArray 格式的字符串</td>
<td>合并之后的订单才有，按需要取</td>
<td></td>
</tr></table>

<!-- 38 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 39 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 40 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.2.3 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>错误码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功，其他表示失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>错误信息</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr></table>

##### 9.3 待开数据上传

###### 9.3.1 接口说明

该接口可用于预制二维码开票、审核开票、公众号开票等场景，通过该接口上送待开发票数据。

###### 9.3.2 消息类型

##### upload.invoice

###### 9.3.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT<br>支持以下类型的发票：电子普通发票／电子专用发票／纸质普通发票／纸质增值税专用发票／数电普票／数电专票</td>
<td>Y </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>预制二维码开票必填<br>审核开票、公众号开票非必填<br>要求格式：8 位的订单日期（merOrderDate)+32位的uuid，保证全局唯一<br>例如：<br>2018060454581ff6d4a94b828e9b9 cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd ，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，<br>以避免跟其他系统冲突，也避免重复开票）</td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBrevityC ode </td>
<td>买方简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerGroupBre vityCode </td>
<td>买方集团简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>amount </td>
<td>开票金额</td>
<td>Long </td>
<td>单位为分</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Long </td>
<td>单位为分<br>为空：普通征税<br>不为空：差额征税<br>扣除额必须小于开票金额</td>
<td>N </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,1500 0) </td>
<td>为空：使用平台配置的单个默认商品（注：对接系统最好自行上送，灵活性更高，不要依赖平台默认配置，如有多个商品，也必须通过接口上送）<br>不为空：单个商品或多个商品，参考5.2.2.1 节</td>
<td>N </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128) </td>
<td>可填写房间号或桌号，体现在票面备注栏</td>
<td>N </td>
</tr><tr>
<td>expireTime </td>
<td>过期时间</td>
<td>Integer </td>
<td>单位为分钟。表示生成二维码后N 分钟内不扫码，二维码自动过期。不设置，则表示不过期</td>
<td>N </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEMail </td>
<td>消费者邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUr1 </td>
<td>商户通知地址，用于开票结果<br>通知</td>
<td>String(1,256) </td>
<td>接收开票结果的地址，按开票结果通知接口开发</td>
<td>N </td>
</tr><tr>
<td>storeId </td>
<td>门店号</td>
<td>String(1,32) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>operateId </td>
<td>营运ID </td>
<td>String(1,18) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>vehicleNo </td>
<td>车牌号</td>
<td>String(1,10) </td>
<td>深圳出租车开票请求时必填</td>
<td>N </td>
</tr><tr>
<td>outTradeNo </td>
<td>三方自定义的订单号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notFaceRemark </td>
<td>非票面备注</td>
<td>String(1,256) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>specialInvoic eFlag </td>
<td>特殊票种标识</td>
<td>String(2) </td>
<td>00 普通发票（默认）<br>全电发票支持：03  建筑服务发票，04 货物运输发票，05 不动产销售服务发票，06 不动产租赁服务发票，09 旅客运输发票，81 成品油发票，14 机动车，15 二手车</td>
<td>N </td>
</tr><tr>
<td>invoiceSpecia 1Info </td>
<td>特殊票种信息</td>
<td>String(1,1500 0) </td>
<td>Json 列表，全电发票特殊票种标志为03、04、05、06、09时必填，具体信息参考5 . 2 . 2 . 3 节</td>
<td>N </td>
</tr><tr>
<td>confirmIssue </td>
<td>全电成品油单价过低是否确认开具标识</td>
<td>String(2) </td>
<td>0 ：否，1 ：是，默认为0<br>（备注：专票单价过低强制开票会记入异常发票，红冲需要在电子税务局申请解除然后再红冲；普票单价过低开具成功发票可以正常红冲）</td>
<td>N </td>
</tr><tr>
<td>invoiceBalanc eInfo </td>
<td>数电发票差额扣除额凭证明细</td>
<td>JsonArray </td>
<td>数电开票，当deductionAmount 不为空时必填，最大支持100 行具体信息参考5 . 2 . 2 . 4 节</td>
<td>N </td>
</tr></table>

<!-- 41 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 42 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.3.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>错误码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功，其他表示失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>错误信息</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 43 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

##### 9.4 待开数据批量上传

###### 9.4.1 接口说明

该接口可用于预制二维码开票、审核开票、公众号开票等场景，通过该接口上送待开发票数据，每次不能超过50条数据。

###### 9.4.2 消息类型

##### batch.upload.invoice

**9.4.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoices </td>
<td>发票数组，json数组格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

单个发票信息如下所示：

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT<br>支持三种类型的发票：电子普通发票／纸质普通发票／纸质增值税专用发票</td>
<td>Y </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>预制二维码开票必填<br>审核开票、公众号开票非必填<br>要求格式：8 位的订单日期（merOrderDate)+32位的uuid，保证全局唯一<br>例如：<br>2018060454581ff6d4a94b828e9b9 cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd ，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易</td>
<td>Y </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td>必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td></td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBrevityC ode </td>
<td>买方简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerGroupBre vityCode </td>
<td>买方集团简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>amount </td>
<td>开票金额</td>
<td>Long </td>
<td>单位为分</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Long </td>
<td>单位为分<br>为空：普通征税<br>不为空：差额征税<br>扣除额必须小于开票金额</td>
<td>N </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,1500 0) </td>
<td>为空：使用平台配置的单个默认商品（注：对接系统最好自行上送，灵活性更高，不要依赖平台默认配置，如有多个商品，也必须通过接口上送）<br>不为空：单个商品或多个商品，参考5.2.2.1 节</td>
<td>N </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128) </td>
<td>可填写房间号或桌号，体现在票面备注栏</td>
<td>N </td>
</tr><tr>
<td>expireTime </td>
<td>过期时间</td>
<td>Integer </td>
<td>单位为分钟。表示生成二维码后N 分钟内不扫码，二维码自动过期。不设置，则表示不过期</td>
<td>N </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEMail </td>
<td>消费者邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地址，用于开票结果通知</td>
<td>String(1,256) </td>
<td>接收开票结果的地址，按开票结果通知接口开发</td>
<td>N </td>
</tr><tr>
<td>storeId </td>
<td>门店号</td>
<td>String(1,32) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td>为空则使用平台配置的默认值</td>
<td>N </td>
</tr><tr>
<td>outTradeNo </td>
<td>三方自定义的订单号</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notFaceRemark </td>
<td>非票面备注</td>
<td>String(1,256) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>specialInvoic eFlag </td>
<td>特殊票种标识</td>
<td>String(2) </td>
<td>00 普通发票（默认）<br>全电发票支持：03  建筑服务发票，04 货物运输发票，05 不动产销售服务发票，06 不动产租赁服务发票，09 旅客运输发票，81 成品油发票，14 机动车，15 二手车</td>
<td>N </td>
</tr><tr>
<td>invoiceSpecia 1Info </td>
<td>特殊票种信息</td>
<td>String(1,1500 0) </td>
<td>Json 列表，全电发票特殊票种标志为03、04、05、06、09时必填，具体信息参考5 . 2 . 2 . 3 节</td>
<td>N </td>
</tr><tr>
<td>confirmIssue </td>
<td>全电成品油单价过低是否确认开具标识</td>
<td>String(2) </td>
<td>0 ：否，1 ：是，默认为0<br>（备注：专票单价过低强制开票会记入异常发票，红冲需要在电子税务局申请解除然后再红冲；普票单价过低开具成功发票可以正常红冲）</td>
<td>N </td>
</tr><tr>
<td>invoiceBalanc eInfo </td>
<td>数电发票差额扣除额凭证明细</td>
<td>JsonArray </td>
<td>数电开票，当deductionAmount 不为空时必填，最大支持100 行具体信息参考5 . 2 . 2 . 4 节</td>
<td>N </td>
</tr></table>

<!-- 44 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 45 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.4.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>错误码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功，其他表示失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>错误信息</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoices </td>
<td>发票数组，json数组格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

单个节点信息如下：

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>错误码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功，其他表示失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>错误信息</td>
<td>String(1,128) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，原交易日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>原交易订单号）</td>
<td>Y </td>
</tr></table>

<!-- **46** -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

##### 9.5 红冲发票

###### 9.5.1 接口说明

在发生交易退货，或者误开发票时可以进行发票红冲，其中电子普通发票可以红冲，纸质普通发票和纸质专用发票可以红冲，红冲将开具一张负数发票（红字发票），用以抵消原正数发票（蓝字发票）。

###### 9.5.2 消息类型

##### reverse

**9.5.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>redNotificati onNo </td>
<td>红字信息表编号</td>
<td>String(1,32) </td>
<td>专票红冲必传<br>全电发票传红字确认单编号</td>
<td>N </td>
</tr></table>

###### 9.5.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>ISSUED：已开具<br>REVERSING：红冲中<br>REVERSED：已红冲<br>SPLITED：已拆分</td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 47 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 48 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 49 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.6 退货发票

###### 9.6.1 接口说明

在发生交易退货，无需进行查询现有订单状态，直接发送撤销命令，发票平台后端自行判断订单状态，如果没有开票就直接撤销发票订单，如果已经开票就直接红冲。

###### 9.6.2 消息类型

##### return

**9.6.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

**9.6.4** **响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>ISSUED：已开具<br>REVERSING：红冲中<br>REVERSED：已红冲<br>PENDING：待开具<br>CLOSED：已关闭<br>CANCELED：已撤销<br>SPLITED：已拆分</td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 50 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 51 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 52 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

###### 9.7 作废发票

###### 9.7.1 接口说明

在发生交易退货，或者误开发票时可以进行发票作废，其中电子普通发票只能红冲，不能作废（电子发票具有可复制性），纸质普通发票和纸票专用发票为当月发票时可以进行作废（未超过销售方开票当月；销售方未抄税并且未记账；购买方未认证或者认证失败），跨月发票只能进行红冲。

###### 9.7.2 消息类型

## invalid

### 9.7.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>invalidPerson </td>
<td>作废人</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr></table>

### 9.7.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>ISSUED：已开具<br>INVALIDING：作废中<br>INVALIDED：已作废</td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td></td>
<td>微信插卡</td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 53 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 54 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 55 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 9.8 撤销发票

#### 9.8.1 接口说明

该接口用于扫码开票场景，可以通过该接口对待开发票数据进行撤销，撤销之后消费者将无法进行扫码开票。

#### 9.8.2 消息类型

## cancel

**9.8.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

### 9.8.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>PENDING：待开具<br>CLOSED：已关闭<br>CANCELED：已撤销</td>
<td>Y </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td>SPLITED：已拆分</td>
<td></td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 56 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 57 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 58 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 9.9 撤销恢复发票

#### 9.9.1 接口说明

该接口用于扫码开票场景，可以通过该接口对已撤销发票数据进行撤销恢复，恢复成待开发票数据之后，消费者可以进行扫码开票。

#### 9.9.2 消息类型

## cancel. resume

**9.9.3** **请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

**9.9.4** **响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>status </td>
<td>发票状态</td>
<td>String(1,32) </td>
<td>PENDING：待开具<br>CANCELED：已撤销</td>
<td>Y </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td>SPLITED：已拆分</td>
<td></td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String(1,32) </td>
<td>纸质发票：PAPER<br>电子发票：ELECTRONIC</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String(1,32) </td>
<td>普通发票：PLAIN<br>增值税专用发票：VAT </td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>cipherCode </td>
<td>密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eNo </td>
<td>红票的发票号码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票的发票代码</td>
<td>String(1,20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCheckC ode </td>
<td>红票的校验码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseCipher Code </td>
<td>红票的密码区</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红冲日期</td>
<td>String(18) </td>
<td>格式yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>deviceNo </td>
<td>机器编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCodeId </td>
<td>二维码唯一id </td>
<td>String(40) </td>
<td>2018060454581ff6d4a94b828e 9b9cfef4cebe90 </td>
<td>Y </td>
</tr><tr>
<td>storeId </td>
<td>门店编号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>storeName </td>
<td>门店名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantName </td>
<td>银商商户名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>买方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>买方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>买方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephon e </td>
<td>买方电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBank </td>
<td>买方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>买方银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>卖方名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>卖方纳税人识别号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAddress </td>
<td>卖方地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTelphon e </td>
<td>卖方电话</td>
<td>String(1,16) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerBank </td>
<td>卖方开户行</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerAccount </td>
<td>卖方账号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>checker </td>
<td>复核人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>drawer </td>
<td>开票人</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxMethod </td>
<td>征税方式</td>
<td>String(1,32) </td>
<td>NORMAL：普通征税<br>DEDUCTION：差额征税</td>
<td>Y </td>
</tr><tr>
<td>deductionAmou nt </td>
<td>扣除额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPriceInc ludingTax </td>
<td>含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税总金额</td>
<td>Double </td>
<td>单位为元</td>
<td>Y </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String(1,102 4) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEmail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyUrl </td>
<td>商户通知地<br>址，用于开票结果通知</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyMerEmai 1 </td>
<td>商户邮箱，用于邮件通知电票</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>pdfPreviewUrl </td>
<td>PDF 预览链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>开票结果信息</td>
<td>String(1,256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 59 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 60 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 61 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 9.10打印发票

#### 9.10.1 接口说明

该接口用于打印纸质发票，但该接口只是发起打印指令，实际打印还须开票员在打印端进行确认。另外在此之前请开票员在打印机放置空白发票，并核对好票号，以免打错发票。

#### 9.10.2 消息类型

# print

**9.10.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

**9.10.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

<!-- 62 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.11推送发票

### 9.11.1 接口说明

该接口用于向消费者手机或电子邮箱推送发票，只有已开具发票才能推送。

### 9.11.2 消息类型

# notify

**9.11.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate 为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>notifyMobileN O </td>
<td>消费者手机<br>号，用于短信通知电票</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>notifyEMail </td>
<td>消费者邮箱，<br>用于邮件通知电票</td>
<td>String(1,64) </td>
<td>推送手机号和邮箱，必填一项，前端自行控制</td>
<td>N </td>
</tr></table>

## 9.11.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

<!-- 63 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.12下载版式文件

### 9.12.1 接口说明

该接口用于下载电子发票版式文件，只有已开具发票才能下载。

### 9.12.2 消息类型

# pickup

**9.12.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>reversing </td>
<td>是否是红票</td>
<td>Boolean </td>
<td>蓝票false（默认），红票true </td>
<td>N </td>
</tr><tr>
<td>needImg </td>
<td>是否需要版式文件的图片</td>
<td>Boolean </td>
<td>版式文件转成图片比较耗时，按需请求</td>
<td>N </td>
</tr></table>

**9.12.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>pdf </td>
<td>版式文件，PDF 格式（用于报销）</td>
<td>String(1,409 6) </td>
<td>base64 编码</td>
<td>Y </td>
</tr><tr>
<td>pdfImg </td>
<td>版式文件转成的图片，png 格式（仅可用于页面展示，不能用来报销）</td>
<td>String(1,409 6) </td>
<td>base64 编码，请求needImg 为true 时才返回该值</td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>版式文件下载地址（可做成页面上的下载或保存按钮）</td>
<td>String(1,256 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>ofd </td>
<td>版式文件，OFD 格式（用于报销）</td>
<td>String(1,409 6) </td>
<td>base64 编码</td>
<td>Y </td>
</tr><tr>
<td>ofdImg </td>
<td>版式文件转成的图片，ofd 格式（仅可用于页面展示，不能用来报销）</td>
<td>String(1,409 6) </td>
<td>base64 编码，请求needImg 为true 时才返回该值</td>
<td>N </td>
</tr><tr>
<td>ofdUr1 </td>
<td>版式文件下载地址（可做成页面上的下载或保存按钮）</td>
<td>String(1,256 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>xml </td>
<td>版式文件，XML 格式（用于报销）</td>
<td>String(1,409 6) </td>
<td>base64 编码</td>
<td>Y </td>
</tr><tr>
<td>xmlUr1 </td>
<td>版式文件下载地址（可做成页面上的下载或保存按钮）</td>
<td>String(1,256 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>subList </td>
<td>子订单信息</td>
<td>JsonArray 格<br>式的字符串</td>
<td>状态为已拆分的母订单才会有子订单的信息。信息结构和母订单的返回相同，可按需要取</td>
<td>N </td>
</tr></table>

<!-- 64 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 9.13批量下载版式文件

## 9.13.1 消息类型

# batch.pickup

## 9.13.2 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoices </td>
<td>发票数组，json数组格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 65 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<table border="1" ><tr>
<td colspan="5">invoices 的属性如下</td>
</tr><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>reversing </td>
<td>是否是红票</td>
<td>Boolean </td>
<td>蓝票false，红票true </td>
<td>Y </td>
</tr></table>

## 9.13.3 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoices </td>
<td>发票数<br>组，json数组<br>格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

invoices的属性如下

<table border="1" ><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，建议使用原交易支付日期</td>
<td>N </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>N </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reversing </td>
<td>是否是红票</td>
<td>Boolean </td>
<td>蓝票false，红票true </td>
<td>N </td>
</tr><tr>
<td>pdf </td>
<td>版式文件数据</td>
<td>String(1,409 6) </td>
<td>base64 编码</td>
<td>N </td>
</tr><tr>
<td>pdfUr1 </td>
<td>PDF 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>ofd </td>
<td>版式文件数据</td>
<td>String(1,409 6) </td>
<td>base64 编码</td>
<td>N </td>
</tr><tr>
<td>ofdUr1 </td>
<td>OFD 下载链接</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>errCode </td>
<td>错误代码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功</td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>错误描述</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 66 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 9.14抬头模糊查询

## 9.14.1 接口说明

该接口用于模糊匹配企业信息，方便消费者在开票时输入发票抬头信息。注意接口返回数据均为推荐数据，不能保证百分百的准确率。

## 9.14.2 消息类型

# query.fuzzy.title

**9.14.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>name </td>
<td>公司名称关键字</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxCode </td>
<td>公司税号关键字</td>
<td>String(1,32) </td>
<td>根据名称或税号关键字查询，二选一</td>
<td>N </td>
</tr></table>

**9.14.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>titleList </td>
<td>抬头列表</td>
<td>Array </td>
<td>内容如下</td>
<td>Y </td>
</tr><tr>
<td>name </td>
<td>公司名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>taxCode </td>
<td>公司税号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>address </td>
<td>公司地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>telephone </td>
<td>公司电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>bank </td>
<td>公司开户银行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>account </td>
<td>公司开户银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 67 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 9.15抬头上传

## 9.15.1 接口说明

该接口用于抬头上传，方便消费者可以通过接口进行集团抬头信息的更新，包含客户的集团码。

## 9.15.2 消息类型

# code.title.upload

**9.15.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>type </td>
<td>抬头类型</td>
<td>String(1,16) </td>
<td>"PERSON","COMPANY" </td>
<td>Y </td>
</tr><tr>
<td>titleName </td>
<td>抬头名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>taxCode </td>
<td>公司税号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>telephone </td>
<td>公司电话</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>bank </td>
<td>公司开户行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>account </td>
<td>公司银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>address </td>
<td>公司地址</td>
<td>String(1,128<br>) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>mobileNo </td>
<td>用户手机号</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>email </td>
<td>用户邮箱</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户编号</td>
<td>String(1,15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(1,8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>brevityCode </td>
<td>商品简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>groupBrevityC ode </td>
<td>集团简码</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr></table>

## 9.15.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

<!-- 68 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 9.16微信抬头识别

## 9.16.1 接口说明

该接口用于解析微信发票助手中的抬头二维码，返回解析后的抬头信息。

## 9.16.2 消息类型

**scan.weixin.title**

**9.16.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>qrCode </td>
<td>微信抬头二维码内容</td>
<td>String(1,102 4) </td>
<td>例：<br>http://w.url.cn/s/AtyrsZ3</td>
<td>Y </td>
</tr></table>

**9.16.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>name </td>
<td>抬头名称</td>
<td>String(1,128 ) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>type </td>
<td>抬头类型</td>
<td>String(1,64) </td>
<td>PERSON：个人<br>COMPANY：公司</td>
<td>Y </td>
</tr><tr>
<td>taxCode </td>
<td>公司税号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>address </td>
<td>公司地址</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>telephone </td>
<td>公司电话</td>
<td>String(1,16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>bank </td>
<td>公司开户银行</td>
<td>String(1,64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>account </td>
<td>公司开户银行账号</td>
<td>String(1,32) </td>
<td></td>
<td>N </td>
</tr></table>

# 9.17获取微信插卡授权链接

## 9.17.1 接口说明

该接口可以返回微信插卡授权链接，对接方可以将该链接做成插卡按钮，为消费者提供插卡功能，支持微信H5页面、微信小程序、普通H5页面。

<!-- 69 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

注意：该接口只是提供便捷的微信插卡功能，获取授权链接和授权后的插卡操作均有发票平台完成。当然对接方可以直接根据微信公众平台官方对接方案，但开发量相对较大。

## 9.17.2 消息类型

# weixin.card

**9.17.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>source </td>
<td>开票来源</td>
<td>String(1,32) </td>
<td>web：微信h5 开票；wxa：小程序开发票；wap ：普通网页开票</td>
<td>Y </td>
</tr></table>

**9.17.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>authUr1 </td>
<td>授权链接地址</td>
<td>String(1,256 ) </td>
<td>插入卡包授权链接地址</td>
<td>N </td>
</tr><tr>
<td>appid </td>
<td>source 为wxa 时才有</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr></table>

# 9.18 自动插入微信卡包

## 9.18.1 接口说明

该接口适用于首先由对接方根据微信官方接口获取插卡授权链接，在插卡授权完成后再调用该接口完成发票插卡操作。

<!-- 70 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.18.2 消息类型

**weixin.card.auto.insert**

**9.18.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd<br>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr><tr>
<td>merWxAppId </td>
<td>商户微信<br>AppId，用于微信插卡</td>
<td>String(1,32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merWxOrderId </td>
<td>商户微信<br>OrderId，用于微信插卡</td>
<td>String(1,64) </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.18.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

# 9.19 心跳检测

## 9.19.1 接口说明

该接口用于检测发票平台接口的连通性（非长连接的心跳检测），在调用发票平台的任意接口之前可以先调用本接口，如果接口正常，继续调用其他接口，如果不正常，停止调用其他接口。对接方可以根据需要使用本接口。

<!-- 71 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.19.2 消息类型

# heartbeat

## 9.19.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

## 9.19.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr></table>

# 9.20商户自定义商品信息查询

## 9.20.1 接口说明

该接口用于同步商户在发票平台维护的商品信息，便于商品信息的集中维护、多方同步使用。

## 9.20.2 消息类型

**goodsCode.query**

## 9.20.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 72 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.20.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>goodsCodes </td>
<td>商品信息数<br>组，json数组格式</td>
<td>List&lt;<br>GoodsCode<br>&lt;</td>
<td></td>
<td>Y </td>
</tr><tr>
<td>shareGoodsCod es </td>
<td>共享商品信息数组，json数<br>组格式</td>
<td>List&lt;<br>GoodsCode<br>&gt;</td>
<td></td>
<td>Y </td>
</tr></table>

GoodsCode的属性如下

<table border="1" ><tr>
<td>code </td>
<td>商品编码</td>
<td>String(21) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>goodsName </td>
<td>商品或者服务名称</td>
<td>String(128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>descr </td>
<td>说明</td>
<td>String(200 0) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vatRate </td>
<td>增值税税率</td>
<td>String(16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vatRateManage </td>
<td>增值税特殊管理</td>
<td>String(128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vatPolicy </td>
<td>增值税政策依据</td>
<td>String(256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vatSpecContent </td>
<td>增值税特殊内容代码</td>
<td>String(200 0) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>consumTaxManage </td>
<td>消费税管理</td>
<td>String(128 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>consumTaxPolicy </td>
<td>消费税政策依据</td>
<td>String(256 ) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>consumTaxSpecCon tent </td>
<td>消费税特殊内容代码</td>
<td>String(200 0) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>isSum </td>
<td>是否汇总项 是／否</td>
<td>String(2) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>tjjbm </td>
<td>统计局编码</td>
<td>String(100 0) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>hgjcksppm </td>
<td>海关进出口商品品目</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>brevityCode </td>
<td>商品简码</td>
<td>String(32) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>model </td>
<td>规格</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>unit </td>
<td>单位</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>unitPrice </td>
<td>单价</td>
<td>Double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>freeTaxType </td>
<td>免税类型</td>
<td>String(1) </td>
<td>空：正常非零税率0 ：<br>出口退税1 ：免税2 ：</td>
<td>N </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td>不征税3 ：普通零税率</td>
<td></td>
</tr><tr>
<td>preferPolicyFlag </td>
<td>是否使用优惠政策</td>
<td>String(1) </td>
<td>0：否1：是</td>
<td>N </td>
</tr></table>

<!-- 73 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 9.21商户开通状态查询

## 9.21.1 接口说明

该接口用于查询商户（商户终端）的开通状态，如状态为关闭，将无法使用开票功能。

## 9.21.2 消息类型

**mer.openStatus.query**

## 9.21.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.21.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>openStatus </td>
<td>开通状态</td>
<td>String </td>
<td>OPENED：已开通<br>NOT_OPEN：未开通</td>
<td></td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

# 9.22商户库存信息查询

## 9.22.1 接口说明

该接口用于查询商户的税盘库存信息。

<!-- 74 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.22.2 消息类型

# inventory.query

**9.22.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.22.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>results </td>
<td>库存信息</td>
<td>Json 数组格式的String </td>
<td>List&lt;ResultsInfo&gt;</td>
<td>Y </td>
</tr></table>

单个ResultsInfo如下

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>companyCode </td>
<td>公司代码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>companyName </td>
<td>公司名称</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>companyTaxNo </td>
<td>公司税号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String </td>
<td>s 纸质专票c 纸质普票ce 电子普票</td>
<td>N </td>
</tr><tr>
<td>startInvoiceN O </td>
<td>发票起始号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>endInvoiceNo </td>
<td>发票终止号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>nextInvoiceNo </td>
<td>下一个发票号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>nextInvoiceCo de </td>
<td>下一个发票代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>leftInvoiceNu<br>m </td>
<td>当前卷剩余发票数</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>applyInvoiceN um </td>
<td>当前卷申请发票数</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>applyDate </td>
<td>申请日期</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>applyUser </td>
<td>申请人</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>applyInvoiceT otalNum </td>
<td>申请发票总份数</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>onlineInvoice TotalNum </td>
<td>在线开具发票总数</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>offlineInvoic eTotalNum </td>
<td>离线开具发票总份数</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxDeviceMach ineNo </td>
<td>开票机号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>userId </td>
<td>开票账号（mi 号）</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 75 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.23商户用户名、密码查询状态

### 9.23.1 消息类型

# terminalQuary

**9.23.2 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>LoginName </td>
<td>登录名</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>Password </td>
<td>密码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.23.3 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantld </td>
<td>商户号</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>terminalld </td>
<td>终端号</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>organization </td>
<td>开票组织</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>taxCode </td>
<td>纳税人识别号</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>address </td>
<td>地址</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>telephone </td>
<td>电话</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>bank </td>
<td>开户行</td>
<td>String </td>
<td></td>
<td></td>
</tr><tr>
<td>account </td>
<td>账号</td>
<td>String </td>
<td></td>
<td></td>
</tr></table>

<!-- 76 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.24更新三方系统合并的子订单状态

### 9.24.1 消息类型

# batch.updateMergedStat

## 9.24.2 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoices </td>
<td>发票数组，json数组格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

invoices的属性如下

<table border="1" ><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，建议使用原交易支付日期</td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td>注意：<br>merchantId+terminalId+<br>merOrderId+merOrderDate为标识同一订单，请与开票接口上送的保持一致，否则会查询不到发票记录</td>
<td>Y </td>
</tr></table>

## 9.24.3 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoices </td>
<td>发票数<br>组，json 数组<br>格式</td>
<td></td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 77 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

invoices的属性如下

<table border="1" ><tr>
<td>merOrderDate </td>
<td>开票订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd，建议使用原交易支付日期</td>
<td>N </td>
</tr><tr>
<td>merOrderId </td>
<td>开票订单号</td>
<td>String(1,32) </td>
<td>建议使用原交易订单号（每笔交易必须保证唯一，需按照规则生成，以避免跟其他系统冲突，也避免重复开票）</td>
<td>N </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>status </td>
<td>状态</td>
<td>String </td>
<td>MERGEDANDISSUED 表示已由第三方自行合并开具</td>
<td></td>
</tr><tr>
<td>errCode </td>
<td>错误代码</td>
<td>String(1,20) </td>
<td>SUCCESS 表示成功</td>
<td>N </td>
</tr><tr>
<td>errMsg </td>
<td>错误描述</td>
<td>String(1,128 ) </td>
<td></td>
<td>N </td>
</tr></table>

## 9.25红字信息表申请

### 9.25.1 接口说明

该接口用于已开具的专票申请购方或销方的红字信息表。

### 9.25.2 消息类型

# reverse.applyRedNo

## 9.25.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>issueQRCodeId </td>
<td>发票二维码编号</td>
<td>String(64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>applyReason </td>
<td>申请理由</td>
<td>String(1) </td>
<td>参数为：0、1、2：<br>购方申请：<br>0 ：已抵扣<br>1 ：未抵扣<br>销方申请：<br>2 ：销方申请</td>
<td>Y </td>
</tr><tr>
<td>applyWay </td>
<td>申请方式</td>
<td>String(1) </td>
<td>0 ：全部冲红<br>1 ：部分冲红</td>
<td>Y </td>
</tr><tr>
<td>taxRateFlag </td>
<td>征税方式</td>
<td>String(1) </td>
<td>0 ：普通征税<br>1 ：减按计征<br>2 ：差额征税</td>
<td>N </td>
</tr><tr>
<td>autoFlag </td>
<td>自动标识</td>
<td>String(1) </td>
<td>0 ：手动审批、手动上传；<br>1 ：手动审批、自动上传；2 ：自动审批、自动上传；</td>
<td>N </td>
</tr></table>

<!-- 78 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.25.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.26红字信息表查询

### 9.26.1 接口说明

该接口用于查询已申请的红字信息表。

### 9.26.2 消息类型

**redInfo.query**

**9.26.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>applySsn </td>
<td>申请流水号</td>
<td>String(40) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxPayerCode </td>
<td>纳税人识别号</td>
<td>String(20) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>redInfoNo </td>
<td>红字信息表编号</td>
<td>String(16) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>fullBackInfo </td>
<td>返回全信息</td>
<td>String(8) </td>
<td>0 ：返回信息表主体信息<br>1 ：返回主体与明细信息</td>
<td>N </td>
</tr></table>

<!-- 79 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 9.26.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>redInfoResult S </td>
<td>查询结果</td>
<td>Json 数组格式的 String </td>
<td>List&lt;RedInfoResult&gt;</td>
<td>Y </td>
</tr></table>

单个RedInfoResult 如下

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>applySsn </td>
<td>申请流水号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>applyNo </td>
<td>申请单号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>venderTaxCode </td>
<td>销货单位识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>venderName </td>
<td>销货单位名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>purchaserTaxC ode </td>
<td>购货单位识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerName </td>
<td>购货单位名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxRateFlag </td>
<td>征税方式</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>processStatus </td>
<td>处理状态</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>errorInfo </td>
<td>错误信息</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalPrice </td>
<td>合计金额</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>合计税额</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>redInfoNo </td>
<td>信息表编号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>oriInvoiceCod e </td>
<td>原发票代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>oriInvoiceNo </td>
<td>原发票号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 80 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.27红字信息表撤销

### 9.27.1 接口说明

该接口用于撤销红字信息表。

### 9.27.2 消息类型

# reverse.cancelRedNo

**9.27.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>issueQRCodeId </td>
<td>发票二维码编号</td>
<td>String(64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(64) </td>
<td></td>
<td>N </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String(20) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>termNo </td>
<td>税控服务器编号</td>
<td>String(12) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>redNotificati onNo </td>
<td>红字信息表编号</td>
<td>String(16) </td>
<td></td>
<td>Y </td>
</tr></table>

## 9.27.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 81 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.28全电红字确认表申请

### 9.28.1 接口说明

该接口用于已开具的全电增值税专用发票申请红字确认单。

### 9.28.2 消息类型

# reverse.confirmedInfoApply

**9.28.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr><tr>
<td>applyReason </td>
<td>冲红原因</td>
<td>String(2) </td>
<td>01 ：开票有误<br>02 ：销货退回<br>03 ：服务中止<br>04 ：销售折让</td>
<td>Y </td>
</tr><tr>
<td>entryIdentity </td>
<td>录入方身份</td>
<td>String(2) </td>
<td>01：销方<br>02：购方</td>
<td>Y </td>
</tr></table>

## 9.28.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 82 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 9.29全电红字确认表查询

### 9.29.1 接口说明

该接口用于查询全电增值税专用发票已申请的红字确认单信息。

### 9.29.2 消息类型

# reverse.confirmedInfoQuery

**9.29.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderId </td>
<td>商户订单号</td>
<td>String(64) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merOrderDate </td>
<td>商户订单日期</td>
<td>String(8) </td>
<td>格式yyyyMMdd </td>
<td>Y </td>
</tr></table>

## 9.29.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>invoiceMateri al </td>
<td>发票材质</td>
<td>String </td>
<td>ELECTRONIC：电子发票<br>PAPER：纸质发票</td>
<td>Y </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String </td>
<td>PLAIN：普通发票<br>VAT ：专用发票</td>
<td>Y </td>
</tr><tr>
<td>redConfirmUui d </td>
<td>红字确认信息UUID </td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>applyReason </td>
<td>冲红原因</td>
<td>String </td>
<td>01 ：开票有误<br>02 ：销货退回<br>03 ：服务中止<br>04 ：销售折让</td>
<td>Y </td>
</tr><tr>
<td>reverseState </td>
<td>红冲状态</td>
<td>String </td>
<td>N ：未红冲<br>Y ：已红冲</td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>购买方名称</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>购买方纳税人识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>redConfirmNo </td>
<td>红字发票信息确认单号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>confirmState </td>
<td>确认单状态代码</td>
<td>String </td>
<td>01 无需确认<br>02  销方录入待购方确认<br>03  购方录入待销方确认<br>04  购销双方已确认<br>05  作废（销方录入购方否认）06  作废（购方录入销方否认）07 作废（超72 小时未确认）<br>08  作废（发起方已撤销）<br>09  作废（确认后撤销）</td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>蓝字发票代码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>totalPrice </td>
<td>蓝票合计金额</td>
<td>double </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>totalTax </td>
<td>蓝票合计税额</td>
<td>double </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>issueDate </td>
<td>蓝票开票日期</td>
<td>Date </td>
<td>日期格式<br>yyyy-MM-dd HH:mm:ss</td>
<td>Y </td>
</tr><tr>
<td>sellerName </td>
<td>销售方名称</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>销售方纳税人识别号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>entryIdentity </td>
<td>录入放身份</td>
<td>String </td>
<td>01：销方<br>02：购方</td>
<td>Y </td>
</tr><tr>
<td>entryDate </td>
<td>录入日期</td>
<td>Date </td>
<td>日期格式<br>yyyy-MM-dd HH:mm:ss</td>
<td>Y </td>
</tr><tr>
<td>confirmDate </td>
<td>确认日期</td>
<td>Date </td>
<td>日期格式<br>yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseInvoic eCode </td>
<td>红票发票号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseDate </td>
<td>红票开票日期</td>
<td>Date </td>
<td>日期格式<br>yyyy-MM-dd HH:mm:ss</td>
<td>N </td>
</tr><tr>
<td>reverseTotalP rice </td>
<td>红票冲销金额</td>
<td>double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>reverseTotalT ax </td>
<td>红票冲销税额</td>
<td>double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>goodsList </td>
<td>商品明细</td>
<td>List </td>
<td>参考5 . 2 . 2 . 1 商品明细</td>
<td>Y </td>
</tr></table>

<!-- 83 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- **84** -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 10进项接口

## 10.1 发票查验

### 10.1.1 消息类型

**inspect**

**10.1.2 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String </td>
<td>10 ：电子普通发票<br>04 ：纸质普通发票<br>01 ：增值税专用发票09 ：数电专票<br>90 ：数电普票<br>03 ：机动车销售统一发票<br>15 ：二手车销售统一发票<br>注意：机动车明细看响应vehicleData 字段，<br>二手车明细看响应usedCarData 字段</td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String </td>
<td>09 , 90 数电可为空</td>
<td>N </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>merchantId </td>
<td>银商商户编号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>issueDate </td>
<td>开票日期</td>
<td>String </td>
<td>格式yyyy-MM-dd </td>
<td>Y </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String </td>
<td>校验码后6 位，09,90<br>数电可为空，专票可为空，普票（04,10）不为空</td>
<td>N </td>
</tr><tr>
<td>totalPrice </td>
<td>金额</td>
<td>String </td>
<td>不含税金额<br>09 , 90 数电发票传价税合计</td>
<td>Y </td>
</tr></table>

<!-- 85 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**10.1.3 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>inspectState </td>
<td>发票查验状态</td>
<td>String </td>
<td>FAILED：失败<br>INSPECTED：查验成功</td>
<td>Y </td>
</tr><tr>
<td>invalidStatus </td>
<td>作废标志</td>
<td>String </td>
<td>Y ：已作废N ：未作废1 ：全额红冲2 ：部分红冲</td>
<td>N </td>
</tr><tr>
<td>localFlag </td>
<td>发票数据本地标志</td>
<td>String </td>
<td>1 ：本地<br>0 ：非本地</td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>buyerName </td>
<td>购方名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>购方税号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddress </td>
<td>购方地址</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTelephone </td>
<td>购方电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAccount </td>
<td>购方银行账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>销方名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>销方税号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerAddress </td>
<td>销方地址</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerTelephon e </td>
<td>销方电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerAccount </td>
<td>销方银行账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>remark </td>
<td>备注</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalPrice </td>
<td>不含税金额</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalTax </td>
<td>税额</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalPriceIncl udingTax </td>
<td>价税合计</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>goodsDetail </td>
<td>商品明细</td>
<td>String </td>
<td>Json 列表</td>
<td>N </td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String(4) </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>vehicleData </td>
<td>机动车明细</td>
<td>String </td>
<td>Json 字段如下</td>
<td>N </td>
</tr><tr>
<td>usedCarData </td>
<td>二手车明细</td>
<td>String </td>
<td>Json 字段如下</td>
<td>N </td>
</tr></table>

vehicleData节点字段信息如下：

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>originPlace </td>
<td>产地</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vehicleNo </td>
<td>车架号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vehicleType </td>
<td>车辆类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>brandModel </td>
<td>厂牌型号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>engineNo </td>
<td>发动机号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>certificateNo </td>
<td>合格证号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>importCertific ateNo </td>
<td>进口证明书号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>inspectionList No </td>
<td>商检单号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>idCardNo </td>
<td>身份证号码／组织机构代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>paymentVoucher No </td>
<td>完税凭证号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>passengersLimi ted </td>
<td>限乘人数</td>
<td>Integer </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxRate </td>
<td>增值税税率</td>
<td>Double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxAuthorityNo </td>
<td>主管税务机关代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxAuthorityNa me </td>
<td>主管税务机关名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>tonnage </td>
<td>吨位</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 86 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

usedCarData节点字段信息如下：

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>totalPrice </td>
<td>车价合计</td>
<td>Double </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vehicleType </td>
<td>车辆类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vehicleNo </td>
<td>车辆识别代号／车架号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>brandModel </td>
<td>厂牌型号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>carNo </td>
<td>车牌照号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>registrationNo </td>
<td>登记证号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>usedCarPhone </td>
<td>二手车市场电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>usedCarAddress </td>
<td>二手车市场地址</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>usedCarName </td>
<td>二手车市场名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>usedCarTaxNo </td>
<td>二手车市场纳税人识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>usedCarBank </td>
<td>二手车市场开户银行及账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>auctionPhone </td>
<td>经营，拍卖单位电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>auctionAddress </td>
<td>经营，拍卖单位地址</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>auctionName </td>
<td>经营，拍卖单位名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>auctionTaxNo </td>
<td>经营，拍卖单位纳税人识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>auctionBank </td>
<td>经营，拍卖单位开户银行及账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>vehiclePlaceNa me </td>
<td>转入地车辆车管所名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 87 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 10.2发票OCR上传接口

### 10.2.1 接口说明

该接口是发票识别功能第一步，发票上传得到对应的key之后再调用发票OCR识别接口进行发票识别。

### 10.2.2 消息类型

**ocr.object.put**

### 10.2.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>fileContent </td>
<td>文件内容：图片base64 编码</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>expireTime </td>
<td>秒，不传默认过期时间为1<br>月</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 88 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 10.2.4 响应

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>objectKey </td>
<td>对象存储key </td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

## 10.3发票OCR识别接口

### 10.3.1 接口说明

该接口是发票识别功能第二步，先使用发票OCR上传接口得到对应的key之后再调用该接口进行识别。

### 10.3.2 消息类型

**ocr.dcisp**

**10.3.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>fileId </td>
<td>发票上传接口返回的key </td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

**10.3.4 响应**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>invoiceInfoList </td>
<td>识别的发票信息列表</td>
<td>Array </td>
<td>内容如下</td>
<td>Y </td>
</tr><tr>
<td>invoiceCode </td>
<td>发票代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceNo </td>
<td>发票号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceDate </td>
<td>开票日期<br>yyyyMMdd </td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>checkCode </td>
<td>校验码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>amountTax </td>
<td>价税合计</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr><tr>
<td>amountInWords </td>
<td>大写金额</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerName </td>
<td>购方名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerTaxCode </td>
<td>购方识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerAddrTel </td>
<td>购方地址电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerName </td>
<td>购方名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>buyerBankAccount </td>
<td>购方开户行及账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerName </td>
<td>销方名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerTaxCode </td>
<td>销方识别号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerAddrTel </td>
<td>销方地址电话</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>sellerBankAccoun t </td>
<td>销方开户行及账号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalAmount </td>
<td>备注</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceCiphertex t </td>
<td>发票密文</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>printInvoiceCode </td>
<td>打印发票代码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>printInvoiceNo </td>
<td>打印发票号码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>qrCode </td>
<td>二维码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>machineCode </td>
<td>机器编号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>oriCheckSum </td>
<td>原始校验码</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>payee </td>
<td>收款人</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceLocation </td>
<td>发票所属地</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>fileType </td>
<td>文件类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>consumType </td>
<td>消费类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>agentFlag </td>
<td>是否代开</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>product0ilFlag </td>
<td>成品油标志</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceName </td>
<td>发票名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceType </td>
<td>发票类型</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>invoiceTemplateT ype </td>
<td>发票联次</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>detailList </td>
<td>识别的商品信息</td>
<td>Array </td>
<td>内容如下</td>
<td>N </td>
</tr></table>

<!-- 89 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

识别的商品信息detailList

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>index </td>
<td>序号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>name </td>
<td>名称</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>model </td>
<td>规格型号</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>unit </td>
<td>单位</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>quantity </td>
<td>数量</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>unitPrice </td>
<td>单价</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>totalAmount </td>
<td>金额</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>taxRate </td>
<td>税率</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>tax </td>
<td>税额</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

<!-- 90 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 11数电认证接口

## 11.1数电登录认证获取短信验证码

### 11.1.1 接口说明

数电登录认证需要短信验证码的地区，先调用该接口获取短信验证码。

需要在云平台-＞机构管理-＞数电连接配置，新增开票员再调用。

### 11.1.2 消息类型

## digital.elec.getSmsCode

**11.1.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>loginName </td>
<td>电局账号</td>
<td>String(32) </td>
<td></td>
<td>Y </td>
</tr></table>

### 11.1.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

<!-- 91 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

### 11.2数电登录认证

#### 11.2.1 接口说明

数电登录认证操作。

需要在云平台-＞机构管理-＞数电连接配置，新增开票员再调用。

#### 11.2.2 消息类型

## digital.elec.userLogin

### 11.2.3 请求

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>loginName </td>
<td>电局账号</td>
<td>String(32) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>smsCode </td>
<td>短信验证码</td>
<td>String(8) </td>
<td>需要短信验证码的地区，先调用8 . 30 获取手机验证码</td>
<td>N </td>
</tr></table>

### 11.2.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr></table>

### 11.3获取数电身份认证二维码

**11.3.1 接口说明**

获取数电身份认证二维码，然后用电局APP进行扫脸认证。

需要在云平台-＞机构管理-＞数电连接配置，新增开票员再调用。

<!-- 92 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

#### 11.3.2 消息类型

## digital.get.authQrCode

**11.3.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>loginName </td>
<td>电局账号</td>
<td>String(32) </td>
<td></td>
<td>Y </td>
</tr></table>

### 11.3.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>qrCode </td>
<td>二维码数据</td>
<td>String </td>
<td></td>
<td>N </td>
</tr><tr>
<td>isConfirmFace </td>
<td>是否需要确认扫脸</td>
<td>String </td>
<td>默认为false ，当此字段返回为true 时候，需要调用11 . 4<br>接口，进行全电确认是否刷脸</td>
<td></td>
</tr><tr>
<td>authId </td>
<td>认证ID </td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

## 11.4全电确认是否刷脸

**11.4.1 接口说明**

获取数电身份认证二维码之后，该请求（即11.3获取数电身份认证二维码）的返回中，isConfirmFace如果为：是，则需要调用此接口，来进行全电确认是否刷脸的调用。

**11.4.2 消息类型**

**digital.confirm.faceSwiping**

<!-- 93 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

**11.4.3 请求**

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>merchantId </td>
<td>银商商户号</td>
<td>String(15) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>terminalId </td>
<td>银商终端号</td>
<td>String(8) </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>authId </td>
<td>认证ID </td>
<td>String(32) </td>
<td></td>
<td>Y </td>
</tr></table>

### 11.4.4 响应

除了公共响应参数外，还有以下信息

<table border="1" ><tr>
<td>参数</td>
<td>名称</td>
<td>类型</td>
<td>参数说明</td>
<td>必填</td>
</tr><tr>
<td>resultCode </td>
<td>返回码</td>
<td>String </td>
<td>SUCCESS：成功<br>其他：失败</td>
<td>Y </td>
</tr><tr>
<td>resultMsg </td>
<td>返回信息</td>
<td>String </td>
<td></td>
<td>Y </td>
</tr><tr>
<td>qrCode </td>
<td>二维码数据</td>
<td>String </td>
<td></td>
<td>N </td>
</tr></table>

# 12示例报文

## 12.1开具发票

### 12.1.1 请求

｝了

"buyerTelephone": "***********",

"msgType": "issue",

"merOrderDate": "********",

"sign":

"3E9DF180375E7B0A1807A75B08C80C2A455A89CB26B790994FFC262F6B8DA7E0",

"msgld": "fa447578-0c68-308a-dd31-1c0d006586af",

"buyerTaxCode": "914419006681800001",

"remark":"",

"terminalld": "********",

"srcReserve": "",

＂buyerAddress":＂中国＂，

"notifyEMail": "<EMAIL>",

<!-- 94 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"deductionAmount": "",

"merchantld": "***************",

"invoiceType": "PLAIN",

"merOrderld": "TEST********201731637743",

＂buyerBank":＂工商银行＂，

"amount": "17800",

"requestTimestamp": "********201731",

"msgSrc": "EXTERNAL_TEST",

"notifyMobileNo":"",

"invoiceMaterial": "ELECTRONIC",

"storeld":"",

＂buyerName":＂测试公司＂，

"buyerAccount": "6212264100011335373",

"notifyUrl":"" ,

"goodsDetail":"[{\"index|":|"1\",|"attribute|":|"0\",|"discountIndex\":\"\",\"name\":\"餐 饮 服 务",ricelncludingTax\"':\"178.0\",\"quantit ]"

}

### 12.1.2 响应

｝了

"buyerAccount": "6212264100011335373",

＂buyerAddress":＂中国＂，

＂buyerBank":＂工商银行＂，

＂buyerName":＂测试公司＂，

"buyerTaxCode": "914419006681800001",

"buyerTelephone": "***********",

"checkCode":"",

＂checker":＂李四＂，

"cipherCode":"",

"createTime": "2020-01-07 20:17:42",

"deductionAmount":0,

"deviceNo":"",

＂drawer":＂王五＂，

＂errMsg":＂开票申请成功！"，

"extend1":""

"extend2":"",

"goodsDetail":

"[{\"attribute\":"0","freeTaxType|":"","index|":1,|"model":"","name|":|餐饮服务\",\"preferPolicyFlag\":\"0\",\"price\":167.*************,\"pricelncludingTax\":178.0,\"quantit y\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\" 餐 饮 服 务

<!-- 95 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

\",\"sn\":\"3070401000000000000\",\"tax\":10.***************,\"taxRate\":6,\"unit\":\"\",\"unitPrice\":167.*************,\"unitPricelncludingTax\":178.0,\"vatSpecial\":\"\"}]",

"invalidDate": null,

"invalidInvoiceCode":"",

"invalidInvoiceNo": "",

"invoiceCode":"",

"invoiceMaterial": "ELECTRONIC",

"invoiceNo": "",

"invoiceType": "PLAIN",

"issueDate":null,

"merOrderDate": "********",

"merOrderld": "TEST********201731637743",

"merWxAppld":"",

"merWxOrderld": "",

"merchantld": "***************",

＂merchantName":＂测试开票终端”，

"msgld": "fa447578-0c68-308a-dd31-1c0d006586af",

"msgSrc": "EXTERNAL_TEST",

"msgType": "issue",

"notifyEmail": "<EMAIL>",

"notifyMerEmail": "",

"notifyMobileNo": "",

"notifyUrl": "https://mobl-test.chinaums.com/fapiao-portal/test/notify.do",

＂payee":＂张三＂，

"pdf":"",

"pdfPreviewUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"pdfUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QBxWL4",

"qrCode": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"qrCodeld": "********4a0d8e39cf1b48ff8648dd5c8f562593",

＂remark":＂测试备注-开票订单号：TEST********201731637743"，

"responseTimestamp": "2020-01-07 20:17:42",

"resultCode": "SUCCESS",

＂resultMsg":＂开票申请成功！"，

"reverseCheckCode":"",

"reverseCipherCode": "",

"reverseDate": null,

"reverselnvoiceCode":"",

"reverselnvoiceNo":"",

"sellerAccount": "****************",

＂sellerAddress":＂武汉＂，

＂sellerBank":＂中国银行＂，

＂sellerName":＂测试开票商户＂，

"sellerTaxCode": "****************",

"sellerTelephone": "***********",

<!-- 96 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"srcReserve":"",

"status":"ISSUING",

"storeld":"3fca1775",

"storeName": "",

"taxMethod":"NORMAL",

"terminalld": "********",

"totalPrice": 167.*************,

"totalPricelncludingTax":178,

"totalTax": 10.***************,

"updateTime": "2020-01-07 20:17:42",

'sign":

"4034D041E4E02CA5540682D5655C5B1D01700AE13DBAA67CE593E053872D47CB"

}

## 12.2生成开票二维码

### 12.2.1 请求

｝了

"amount": "17800",

"msgType": "get.qrcode",

"merOrderDate":"********",

"receiveMobileNo":"",

"requestTimestamp": "********202634",

"msgSrc": "EXTERNAL_TEST",

"sign":

"AB05F864F4089CF86D6FB5DA9C310D5C7F174F2C70E1D11E3484684038EDB039",

"msgld": "1dcbfd56-18cf-bafc-364a-628b56d3d122",

"receiveEmail": "",

"remark":"",

"terminalld": "********",

"invoiceMaterial": "ELECTRONIC",

"storeld":"",

"srcReserve": "",

"deductionAmount": "",

"expireTime":"",

"merchantld": "***************",

$$"invoiceType":"PLAIN",$$

"notifyUrl":"",

"goodsDetail":"[|"index|":|"1|",|"atribute\":\"0\",\"discountIndex|":"","name|":|"餐 饮 服 务",ricelncludingTax\":\"178.0\",\"quantit ]",

<!-- 97 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"merOrderld": "TEST********202634371459"

1}

### 12.2.2 响应

{

"expireDate": "2020-01-22 20:26:40",

"msgld": "1dcbfd56-18cf-bafc-364a-628b56d3d122",

"msgSrc": "EXTERNAL_TEST",

"msgType": "get.qrcode",

"qrCode":

"https://mobl-test.chinaums.com/fapiao-portal/pos_issue.do?id=********3dd9c1352dc9490ab 04ecb25a5482a58&checkCode=594A4D54",

"qrCodeld": "********3dd9c1352dc9490ab04ecb25a5482a58",

"responseTimestamp": "2020-01-07 20:26:40",

"resultCode": "SUCCESS",

＂resultMsg":＂生成开票二维码成功！”，

"shortQrCode": "https://mobl-test.chinaums.com/fapiao-portal/d/0Q2Vtkn",

"srcReserve": "",

"status": "PENDING",

"sign":

"6A278C752E416DF2122BC8A9F0C25BE01EE7D540823460542837EED72AABD105"

}

## 12.3待开数据上传

### 12.3.1 请求

｝了

"qrCodeld": "********c4108640f9dd9937a1eebf84f7dab23c",

"amount": "17800",

"msgType": "upload.invoice",

"merOrderDate": "********",

"requestTimestamp": "********203354",

"msgSrc": "EXTERNAL_TEST",

"sign":

"F571010B9A85F88D04DBFF48BEE8765CB4B5F3FE6FF78337E04B6BB8D452D59D",

"msgld": "d06b4692-de2f-8f00-8b24-10b8cd709ab4",

"drawer":"",

＂remark":＂备注＂，

"terminalld": "********",

"checker":"",

<!-- 98 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"invoiceMaterial": "ELECTRONIC",

"storeld":"",

"srcReserve": "",

"payee":"",

"deductionAmount": "",

"expireTime": "",

"merchantld": "8980000********",

"invoiceType": "PLAIN",

"notifyUrl":"",

"storeName":"",

"goodsDetail": "[{\"index\":\"1\",\"attribute\""O","discountlndex|""","name"餐 饮 服 务",'"6",'",''pricelncludingTax"::"178.0",''quantit ]",

"merOrderld":"TEST********203354856642 "

}

### 12.3.2 响应

｝了

"merOrderDate": "********",

"merOrderld": "TEST********203354856642",

"merchantld": "8980000********",

"msgld": "d06b4692-de2f-8f00-8b24-10b8cd709ab4",

"msgSrc": "EXTERNAL_TEST",

"msgType": "upload.invoice",

"qrCodeld": "********c4108640f9dd9937a1eebf84f7dab23c",

"responseTimestamp": "2020-01-07 20:33:59",

"resultCode": "SUCCESS",

＂resultMsg":＂待开数据上传成功＂，

"srcReserve": "",

"status": "PENDING",

"terminalld": "********",

"sign":

"4B08978AB2FFA17AB5F26371BE713E433CDEF06B81917F53628BCD97B9A45765"

}

<!-- 99 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 12.4查询发票状态

### 12.4.1 请求

{

"srcReserve": "",

"msgType":"query",

"merOrderDate": "********",

"requestTimestamp": "********202933",

"merchantld": "***************",

"msgSrc": "EXTERNAL_TEST",

"sign":

"C933E0A7EB81A6E7D7C087F46488F814B9E38E4F2B4D1168A1E08CA9CC63EA6D",

"msgld": "a920240e-cb64-8252-4a6b-5bc03a219c9a",

"terminalld": "********",

"merOrderld": "TEST********201731637743"

}

### 12.4.2 响应

｝了

"buyerAccount": "6212264100011335373",

＂buyerAddress":＂中国”，

＂buyerBank":＂工商银行＂，

＂buyerName":＂测试公司＂，

"buyerTaxCode": "914419006681800001",

"buyerTelephone": "***********",

"checkCode":"79809457063786317219",

＂checker":＂李四”，

"cipherCode":

"&lt;645++067++2954&gt;795&gt;80035/&gt;1/96+&gt;17&lt;3&gt;&gt;00*/385&gt;&lt;8&gt;6*89+72913-689&gt;&gt;61111-329+-&gt;90&lt;65-/8896+989+" ,

"createTime": "2020-01-07 20:17:42",

"deductionAmount":0,

"deviceNo":"",

"drawer":＂王五＂，

＂errMsg":＂开票成功＂，

$$"extend1":""$$

$$"extend2":"",$$

"goodsDetail":

"["attribute|"::"0","freeTaxType|"::"","index|":1,,"model|"::"","name|"::" 餐饮服务\",\"preferPolicyFlag\":\"0\",\"price\":167.*************,\"pricelncludingTax\":178.0,\"quantit

<!-- 100 -->

