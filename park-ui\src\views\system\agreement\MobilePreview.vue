<template>
  <!-- 手机框架预览组件 -->
  <div class="phone-frame">
    <!-- 手机端协议内容 -->
    <div class="mobile-agreement-content">
      <!-- 手机状态栏 -->
      <div class="mobile-status-bar">
        <span class="status-time"></span>
        <div class="status-right">
          <span class="status-signal">●●●●</span>
          <span class="status-wifi">📶</span>
          <span class="status-battery">99% 🔋</span>
        </div>
      </div>

      <!-- 导航栏 -->
      <div class="mobile-nav-bar">
        <div class="nav-left">
          <i class="nav-back">←</i>
        </div>
        <div class="nav-center">
          <span class="nav-title">协议详情</span>
        </div>
        <div class="nav-right">
          <i class="nav-more">⋯</i>
          <i class="nav-share">⚪</i>
        </div>
      </div>

      <!-- 协议内容区域 -->
      <div class="mobile-content-area">
        <!-- 协议正文内容 -->
        <div class="agreement-text" v-html="formatAgreementContent(agreementContent)"></div>
      </div>

      <!-- 底部安全区域 -->
      <div class="mobile-safe-area"></div>
    </div>
  </div>
</template>

<script setup name="MobilePreview">
const props = defineProps({
  agreementContent: {
    type: String,
    default: ''
  }
})

/** 格式化协议内容 */
function formatAgreementContent(content) {
  if (!content) return '';

  // 保留格式的清理
  let formatted = content
    // 移除完全空的p标签
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    // 移除只包含&nbsp;的p标签
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // 给p标签添加适当的样式，保持段落间距
    .replace(/<p([^>]*)>/gi, '<p$1 style="margin: 8px 0; line-height: 1.6;">')
    // 保留文本对齐样式
    .replace(/text-align:\s*center/gi, 'text-align: center !important')
    .replace(/text-align:\s*left/gi, 'text-align: left !important')
    .replace(/text-align:\s*right/gi, 'text-align: right !important')
    // 减少过多的&nbsp;但保留一些空格
    .replace(/&nbsp;{4,}/g, '&nbsp;&nbsp;&nbsp;')
    // 保留强调标签
    .replace(/<strong([^>]*)>/gi, '<strong$1 style="font-weight: bold;">')
    // 清理过多的连续空白，但不要太激进
    .replace(/\s{3,}/g, ' ');

  return formatted;
}
</script>

<style scoped>
/* 手机框架 */
.phone-frame {
  width: 100%;
  height: 780px;
  background: #ffffff;
  border-radius: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  border: 6px solid #1a1a1a;
  margin: 0;
}

/* 现代手机内容样式 */
.mobile-agreement-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  border-radius: 19px;
}

/* 底部安全区域 */
.mobile-safe-area {
  height: 15px;
  background: #ffffff;
  flex-shrink: 0;
}

/* 手机状态栏样式 */
.mobile-status-bar {
  height: 20px;
  background: #000;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  font-size: 10px;
  font-weight: 600;
}

.status-time {
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 3px;
}

.status-signal, 
.status-wifi, 
.status-battery {
  font-size: 8px;
}

/* 导航栏样式 */
.mobile-nav-bar {
  height: 36px;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.nav-left, 
.nav-right {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 50px;
}

.nav-right {
  justify-content: flex-end;
}

.nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}

.nav-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-back, 
.nav-more, 
.nav-share {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域样式 */
.mobile-content-area {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  height: calc(100% - 56px);
}

/* 协议正文样式 */
.agreement-text {
  padding: 12px;
  line-height: 1.6;
  font-size: 12px;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 预览内容格式化 */
.agreement-text :deep(p) {
  margin: 8px 0 !important;
  line-height: 1.6 !important;
}

.agreement-text :deep(strong) {
  font-weight: bold !important;
  font-size: 13px !important;
}

.agreement-text :deep(h1),
.agreement-text :deep(h2),
.agreement-text :deep(h3) {
  margin: 12px 0 8px 0;
  font-weight: bold;
  font-size: 14px;
}

/* 滚动条样式 */
.mobile-content-area::-webkit-scrollbar {
  width: 4px;
}

.mobile-content-area::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-content-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.mobile-content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .phone-frame {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .phone-frame {
    height: 400px;
  }
}
</style>
