<template>
  <view class="open-invoice">
    <view class="cell">
      <view class="cell-title">发票详情</view>
      <view class="content">
        <view class="content_item" @tap="showPopup = true">
          <view class="title">发票类型</view>
          <view class="word">
            {{
              invoiceInfo.id
                ? invoiceInfo.invoiceType === 1
                  ? '专票'
                  : invoiceInfo.titleType === 1
                  ? '公司'
                  : '个人'
                : '请选择抬头'
            }}
          </view>
          <up-icon name="arrow-right" color="#BDBDBD" size="16"></up-icon>
        </view>
        
        <view class="content_item">
          <view class="title">发票抬头</view>
          <view class="word">{{ invoiceInfo.invoiceTitleContent || '' }}</view>
        </view>
        
        <view class="content_item">
          <view class="title">总金额</view>
          <view class="word">
            <text class="money">{{ money || '0' }}</text> 元
          </view>
        </view>
        
        <template v-if="invoiceInfo.titleType === 1 || invoiceInfo.invoiceType === 1">
          <view class="content_item">
            <view class="title">单位税号</view>
            <view class="word">{{ invoiceInfo.unitDutyParagraph || '' }}</view>
          </view>
          <view class="content_item">
            <view class="title">注册地址</view>
            <view class="word">{{ invoiceInfo.registerAddress || '' }}</view>
          </view>
          <view class="content_item">
            <view class="title">注册电话</view>
            <view class="word">{{ invoiceInfo.registerPhone || '' }}</view>
          </view>
          <view class="content_item">
            <view class="title">开户银行</view>
            <view class="word">{{ invoiceInfo.depositBank || '' }}</view>
          </view>
          <view class="content_item">
            <view class="title">银行账户</view>
            <view class="word">{{ invoiceInfo.bankAccount || '' }}</view>
          </view>
        </template>
      </view>
      
      <view class="cell-title">接收方式</view>
      <view class="email">
        <view class="content_item">
          <view class="title">电子邮箱</view>
          <view class="email-input">
            <up-input
              v-model="email"
              border="none"
              placeholder="请输入您的电子邮箱"
              clearable
              fontSize="28rpx"
              color="#616161"
              :placeholderStyle="{ color: '#616161' }"
            ></up-input>
          </view>
        </view>
        <view class="desc">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>
      </view>
      
      <view class="btn" @tap="handleAddInvoice" :class="{ disabled: isLoading }">
        <up-loading-icon v-if="isLoading" color="#ffffff" size="20"></up-loading-icon>
        <text v-if="!isLoading">{{ isResume ? '发票重开' : '提交开票' }}</text>
      </view>
    </view>

    <view class="tips_word">
      <view class="tips_word_title">注意事项：</view>
      <view class="tips_word_desc">
        1、开票完成，请前往【我的】-【开票管理】查看对应结果。邮件请耐心等待3-10分钟。
      </view>
      <view class="tips_word_desc">
        2、仅本软件支付订单能开具发票，其他请前往对应软件或联系场地运营单位开具发票。
      </view>
      <view class="tips_word_desc">
        3、若发票信息错误，请【申请换开】，同一订单发票仅支持重开一次。
      </view>
    </view>

    <!-- 发票抬头选择弹窗 -->
    <up-popup
      :show="showPopup"
      :safeAreaInsetBottom="false"
      :round="20"
      closeOnClickOverlay
      @close="showPopup = false"
    >
      <view class="popup-cell">
        <view class="popup-title">发票类型</view>
        <view class="popup-content">
          <view
            class="invoice-item"
            v-for="item in invoiceTitleList"
            :key="item.id"
            @tap="chooseInvoice(item)"
          >
            <view class="item-header">
              <template v-if="item.invoiceType === 1">
                <up-icon name="file-text" color="#4BA1FC" size="16"></up-icon>
                <text class="header-text">专用发票抬头</text>
              </template>
              <template v-else>
                <template v-if="item.titleType === 1">
                  <up-icon name="home" color="#FF9500" size="16"></up-icon>
                  <text class="header-text">公司 · 普通发票抬头</text>
                </template>
                <template v-else>
                  <up-icon name="account" color="#34C759" size="16"></up-icon>
                  <text class="header-text">个人 · 普通发票抬头</text>
                </template>
              </template>
            </view>
            
            <view class="item-title">
              {{ item.invoiceTitleContent || '-' }}
            </view>
            
            <view class="item-bottom">
              <view class="item-desc">
                {{ item.unitDutyParagraph || '' }}
              </view>
              <view class="item-edit" @tap.stop="handleEdit(item)">
                <up-icon name="edit-pen" color="#9e9e9e" size="14"></up-icon>
                <text>编辑</text>
              </view>
            </view>
          </view>
        </view>

        <view class="add-btn" @tap="addInvoiceTitle">
          <up-icon name="plus" color="#ffffff" size="16"></up-icon>
          <text>添加发票抬头</text>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup>
import { ref, reactive} from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getInvoiceTitleList, postSaveInvoiceRecord, postResumeInvoice } from '@/api/invoice'

// 响应式数据
const showPopup = ref(false)
const invoiceTitleList = ref([])
const invoiceInfo = reactive({})
// 订单金额
const money = ref(null)
// 订单号
const functionId = ref(null)
// 功能类型
const functionType = ref(null)
// 电子邮箱
const email = ref('')
const isLoading = ref(false)
// 是否重开
const isResume = ref(false)
// 发票id
const invoiceId = ref('')

onShow(() => {
  fetchInvoiceTitleList()
})

onLoad((options) => {
  console.log(options)
  money.value = options.money
  functionId.value = options.functionId
  functionType.value = options.functionType
  
  if (options.invoiceId) {
    invoiceId.value = options.invoiceId
  }
  
  if (options.isResume) {
    isResume.value = true
  } else {
    isResume.value = false
  }
})

// 提交开票
const handleAddInvoice = async () => {
  if (!functionId.value) {
    uni.showToast({
      title: '订单id不存在~',
      icon: 'none'
    })
    return
  }
  
  if (!functionType.value) {
    uni.showToast({
      title: '功能类型不存在~',
      icon: 'none'
    })
    return
  }
  
  if (!invoiceInfo.invoiceTitleContent) {
    uni.showToast({
      title: '请选择抬头~',
      icon: 'none'
    })
    return
  }
  
  if (!email.value) {
    uni.showToast({
      title: '电子邮箱必填~',
      icon: 'none'
    })
    return
  }

  const params = {
    invoiceType: invoiceInfo.invoiceType,
    functionType: functionType.value,
    functionId: functionId.value,
    invoiceTitleContent: invoiceInfo.invoiceTitleContent,
    unitDutyParagraph: invoiceInfo.unitDutyParagraph,
    registerAddress: invoiceInfo.registerAddress,
    registerPhone: invoiceInfo.registerPhone,
    depositBank: invoiceInfo.depositBank,
    bankAccount: invoiceInfo.bankAccount,
    notifyEmail: email.value,
    titleId: invoiceInfo.id,
    id: invoiceId.value
  }
  console.log(params)

  isLoading.value = true

  try {
    const apiCall = isResume.value ? postResumeInvoice : postSaveInvoiceRecord
    const res = await apiCall(params)
    
    uni.showModal({
      content: res.msg,
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack()
        }
      }
    })
  } catch (error) {
    console.error('开票失败:', error)
    uni.showToast({
      title: '开票失败，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 获取发票抬头列表
const fetchInvoiceTitleList = async () => {
  try {
    const res = await getInvoiceTitleList()
    invoiceTitleList.value = res.data || []
  } catch (error) {
    console.error('获取发票抬头列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

// 选择发票抬头
const chooseInvoice = (item) => {
  showPopup.value = false
  Object.assign(invoiceInfo, item)
}

// 编辑发票抬头
const handleEdit = (item) => {
  const obj = JSON.stringify(item)
  uni.navigateTo({
    url: '/pages/invoice/addInvoiceTitle?isEdit=true&obj=' + encodeURIComponent(obj)
  })
}

// 添加发票抬头
const addInvoiceTitle = () => {
  uni.navigateTo({
    url: '/pages/invoice/addInvoiceTitle?isEdit=false'
  })
}
</script>

<style lang="scss" scoped>
.open-invoice {
  min-height: 100vh;
  background: #f8f9fa;

  .cell {
    padding: 32rpx;

    .cell-title {
      font-size: 32rpx;
      font-weight:600;
      color: #000000;
      margin-bottom: 16rpx;
    }

    .content {
      padding: 32rpx;
      background: #ffffff;
      border-radius: 20rpx;
      margin-bottom: 32rpx;

      .content_item {
        display: flex;
        align-items: center;
        position: relative;
        border-bottom: 1rpx solid rgba(189, 189, 189, 0.2);
        padding: 26rpx 0;

        &:last-child {
          border-bottom: none;
        }

        .title {
          font-size: 28rpx;
          color: #212121;
          font-weight: 500;
          margin-right: 74rpx;
          min-width: 112rpx;
          word-break: keep-all;
        }

        .word {
          flex: 1;
          font-size: 28rpx;
          color: #616161;
          line-height: 1.5;

          .money {
            font-weight: 600;
            color: #246bfd;
            margin-right: 8rpx;
          }
        }
      }
    }

    .email {
      padding: 32rpx;
      background: #ffffff;
      border-radius: 20rpx;
      margin-bottom: 56rpx;

      .content_item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .title {
          font-size: 28rpx;
          color: #212121;
          font-weight: 500;
          margin-right: 74rpx;
          min-width: 112rpx;
          word-break: keep-all;
        }

        .email-input {
          flex: 1;
        }
      }

      .desc {
        font-size: 24rpx;
        color: #f5820e;
        margin-top: 35rpx;
      }
    }

    .btn {
      width: 100%;
      height: 88rpx;
      background: #246bfd;
      border-radius: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #ffffff;

      &.disabled {
        opacity: 0.6;
      }

      text {
        margin-left: 8rpx;
      }
    }
  }

  .tips_word {
    padding: 0 42rpx 60rpx;

    .tips_word_title {
      font-weight: 700;
      font-size: 36rpx;
      color: #2a304a;
      margin-bottom: 20rpx;
    }

    .tips_word_desc {
      font-weight: 400;
      font-size: 28rpx;
      color: #a0a7c2;
      line-height: 1.4;
      margin-bottom: 16rpx;
    }
  }
}

.popup-cell {
  padding: 32rpx;
  background: #ebeef6;
  border-radius: 20rpx;
  max-height: 80vh;

  .popup-title {
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    border-left: 4rpx solid #246bfd;
    padding-left: 12rpx;
    margin-bottom: 32rpx;
  }

  .popup-content {
    max-height: 600rpx;
    overflow-y: scroll;

    .invoice-item {
      padding: 32rpx;
      background: #ffffff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;

      .item-header {
        display: flex;
        align-items: center;
        padding-bottom: 20rpx;
        border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

        .header-text {
          font-size: 24rpx;
          font-weight: bold;
          color: #616161;
          margin-left: 8rpx;
        }
      }

      .item-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #212121;
        margin: 20rpx 0 8rpx;
      }

      .item-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-desc {
          font-size: 24rpx;
          color: #9e9e9e;
          flex: 1;
        }

        .item-edit {
          display: flex;
          align-items: center;
          font-size: 28rpx;
          color: #9e9e9e;

          text {
            margin-left: 6rpx;
          }
        }
      }
    }
  }

  .add-btn {
    height: 88rpx;
    background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 20rpx;

    text {
      margin-left: 8rpx;
    }
  }
}
</style> 