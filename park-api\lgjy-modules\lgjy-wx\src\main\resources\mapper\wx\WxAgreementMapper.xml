<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxAgreementMapper">
    
    <resultMap type="WxAgreement" id="WxAgreementResult">
        <result property="id"    column="id"    />
        <result property="agreementType"    column="agreement_type"    />
        <result property="agreementTitle"    column="agreement_title"    />
        <result property="agreementContent"    column="agreement_content"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWxAgreementVo">
        select id, agreement_type, agreement_title, agreement_content, delete_flag, create_time, update_time from sys_agreement
    </sql>

    <select id="selectAgreementByType" parameterType="Integer" resultMap="WxAgreementResult">
        <include refid="selectWxAgreementVo"/>
        where agreement_type = #{agreementType} and delete_flag = 0
        order by create_time desc
        limit 1
    </select>
</mapper>
