<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

y\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\" 餐 饮 服 务

\",\"sn\":\"3070401000000000000\",\"tax\":10.***************,\"taxRate\":6,\"unit\":\"\",\"

unitPrice\":167.*************,\"unitPricelncludingTax\":178.0,\"vatSpecial\":\"\"}]",

"invalidDate":null,

"invalidInvoiceCode": "",

"invalidInvoiceNo": "",

"invoiceCode": "0010720170",

"invoiceMaterial": "ELECTRONIC",

"invoiceNo": "72017440",

"invoiceType": "PLAIN",

"issueDate": "2020-01-07 20:17:58",

"merOrderDate": "********",

"merOrderld": "TEST********201731637743",

"merWxAppld":"",

"merWxOrderld": "",

"merchantld": "***************",

＂merchantName":＂测试开票终端”，

"msgld": "a920240e-cb64-8252-4a6b-5bc03a219c9a",

"msgSrc": "EXTERNAL_TEST",

"msgType": "query",

"notifyEmail": "<EMAIL>",

"notifyMerEmail":"",

"notifyMobileNo": "",

"notifyUrl": "https://mobl-test.chinaums.com/fapiao-portal/test/notify.do",

＂payee":＂张三＂，

"pdf":"",

"pdfPreviewUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"pdfUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QBxWL4",

"qrCode": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"qrCodeld": "********4a0d8e39cf1b48ff8648dd5c8f562593",

＂remark":＂测试备注-开票订单号：TEST********201731637743"，

"responseTimestamp": "2020-01-07 20:29:55",

"resultCode": "SUCCESS",

＂resultMsg":＂发票状态［ISSUED］下，操作［QUERY］幂等＂，

"reverseCheckCode":"",

"reverseCipherCode":"",

"reverseDate": null,

"reverselnvoiceCode": "",

"reverselnvoiceNo":"",

"sellerAccount": "****************",

＂sellerAddress":＂武汉＂，

＂sellerBank":＂中国银行＂，

＂sellerName":＂测试开票商户＂，

"sellerTaxCode": "****************",

<!-- 101 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"sellerTelephone": "***********",

"srcReserve": "",

"status": "ISSUED",

"storeld": "3fca1775",

"storeName":"",

"taxMethod": "NORMAL",

"terminalld": "********",

"totalPrice": 167.9245283,

"totalPricelncludingTax": 178,

"totalTax": 10.0754717,

"updateTime": "2020-01-07 20:17:59",

"sign":

"354CFC818BCCDE9184F72EB5002968F6F55D9FB136938A0ED5DE45250923C1B1"

}

# 12.5开票结果通知【回调】

## 12.5.1 请求

{

"buyerAccount": "6212264100011335373",

＂buyerAddress":＂中国＂，

＂buyerBank":＂工商银行＂，

＂buyerName":＂测试公司＂，

"buyerTaxCode": "914419006681800001",

"buyerTelephone": "***********",

"checkCode": "79809457063786317219",

＂checker":＂李四＂，

"cipherCode":

"&lt;645+++067++2954&gt;795&gt;80035/&gt;1/96+&gt;17&lt;38gt;8gt;00*/385&gt;8dt;88gt;6*89+72913-689&gt;&gt;61111-329+-&gt;90&lt;65-/8896+989+",

"createTime": *************,

"deductionAmount": 0,

"deviceNo":"",

＂drawer":＂王五＂，

"errMsg":：＂开票成功＂，

"goodsDetail":

"[{\"unitPrice\":167.*************,\"vatSpecial\":\"\",\"quantity\":1.0,\"showQuantityAndUnit Price|":"1","freeTaxType":\"\",\"index\":1,\"tax\":10.***************,\"unitPricelncludingT ax|":178.0,|"taxRate|":6,|"unit|":|"|",|"simpleName|":|" \",\"price\":167.*************,\"name\":\" 餐 饮 服 务餐 饮 服 务\",\"model\":\"\",\"attribute\":\"0\",\"sn\":\"3070401000000000000\",\"preferPolicyFlag\":\"0\",pricelncludingTax\":178.0}]",

<!-- 102 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"invalidDate": null,

"invalidInvoiceCode":"",

"invalidInvoiceNo": "",

"invoiceCode": "0010720170",

"invoiceMaterial": "ELECTRONIC",

"invoiceNo": "72017440",

"invoiceType": "PLAIN",

"issueDate": "2020-01-07 20:17:58",

"merOrderDate": "********",

"merOrderld": "TEST********201731637743",

"merWxAppld":"",

"merWxOrderld": "",

"merchantlId": "***************",

＂merchantName":＂测试开票终端＂，

"notifyEmail": "<EMAIL>",

"notifyMerEmail": "",

"notifyMobileNo": "",

"notifyUrl": "https://mobl-test.chinaums.com/fapiao-portal/test/notify.do",

＂payee":＂张三＂，

"pdfPreviewUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"pdfUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QDQ5fW",

"qrCode":"",

"qrCodeld": "********4a0d8e39cf1b48ff8648dd5c8f562593",

＂remark":＂测试备注-开票订单号：TEST********201731637743"，

"reverseCheckCode": "79809457063786317219",

"reverseCipherCode":

"&lt;645+-+067++2954&gt;795&gt;80035/&gt;1/96+&gt;17&lt;3&gt;&gt;00*/385&gt;&lt;8&gt;6*89+72913-689&gt;&gt;61111-329+-&gt;90&lt;65-/8896+989+",

"reverseDate": "2020-01-07 20:32:04",

"reverselnvoiceCode": "**********",

"reverselnvoiceNo": "********",

"sellerAccount": "****************",

＂sellerAddress":＂武汉＂，

＂sellerBank":＂中国银行＂，

＂sellerName":＂测试开票商户＂，

"sellerTaxCode": "****************",

"sellerTelephone": "***********",

"status": "REVERSED",

"storeld": "3fca1775",

"storeName": "",

"taxMethod": "NORMAL",

"terminalld": "********",

"totalPrice": 167.9245283,

"totalPricelncludingTax": 178,

<!-- 103 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"totalTax": 10.0754717,

"updateTime": *************

}

## 12.5.2 响应

{

"resultCode":"SUCCESS",

＂resultMsg":＂通知处理成功！”，

}

# 12.6红冲发票

## 12.6.1 请求

**｝了**

"srcReserve":"",

"msgType": "reverse",

"merOrderDate": "********",

"requestTimestamp": "********203143",

"merchantld": "***************",

"msgSrc": "EXTERNAL_TEST",

"sign":

"5A8AC198F5716AE6DD07C934A293EE635B2944FABCE4DBBF21300588B8CC8C7B",

"msgld": "331e2cea-50e5-ac01-daa8-5e967400106f",

"terminalld": "********",

"merOrderld": "TEST********201731637743"

｝了

## 12.6.2 响应

**{**

"buyerAccount": "6212264100011335373",

＂buyerAddress":＂中国＂，

＂buyerBank":＂工商银行＂，

＂buyerName":＂测试公司＂，

"buyerTaxCode": "914419006681800001",

"buyerTelephone": "***********",

"checkCode": "79809457063786317219",

＂checker":＂李四＂，

"cipherCode":

<!-- 104 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"&lt;645++067++2954&gt;795&gt;80035/&gt;1/96+&gt;17&lt;3&gt;&gt;00*/385&gt;&lt;8&gt;6*89+72913-689&gt;&gt;61111-329+&gt;90&lt;65-/8896+989+",

"createTime": "2020-01-07 20:17:42",

"deductionAmount":,

"deviceNo":"",

"drawer":＂王五＂，

"errMsg":＂红冲申请成功！"，

$$"extend1":""$$

"extend2": "",

"goodsDetail":

"[{"attribute|"::"0)","freeTaxType|"::"","index|":1,"model|"::"","name|"::" 餐饮服务\",\"preferPolicyFlag\":\"0\",\"price\":167.*************,\"pricelncludingTax\":178.0,\"quantit y\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\" 餐 饮 服 务,"tax|":10.***************,"taxRate|":6,"unit|"::1"unit|":10.***************,|"taxRate|":6,|"unit|":::"",|"\",\"tax\":10.***************,\"taxRate\":6,\"unit\":\"\",\"unitPrice\":167.*************,\"unitPricelncludingTax\":178.0,\"vatSpecial\":\"\"}]",

"invalidDate": null,

"invalidInvoiceCode": "",

"invalidInvoiceNo":"",

"invoiceCode": "0010720170",

"invoiceMaterial": "ELECTRONIC",

"invoiceNo": "72017440",

"invoiceType": "PLAIN",

"issueDate": "2020-01-07 20:17:58",

"merOrderDate": "********",

"merOrderld": "TEST********201731637743",

"merWxAppld": "",

"merWxOrderld": "",

"merchantld": "***************",

＂merchantName":＂测试开票终端”，

"msgld": "331e2cea-50e5-ac01-daa8-5e967400106f",

"msgSrc": "EXTERNAL_TEST",

"msgType":"reverse",

"notifyEmail": "<EMAIL>",

"notifyMerEmail":"",

"notifyMobileNo":"",

"notifyUrl": "https://mobl-test.chinaums.com/fapiao-portal/test/notify.do",

＂payee":＂张三＂，

"pdf":"",

"pdfPreviewUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"pdfUrl": "https://mobl-test.chinaums.com/fapiao-portal/d/0QDQ5fW",

"qrCode": "https://mobl-test.chinaums.com/fapiao-portal/d/0QRHm1U",

"qrCodeld": "********4a0d8e39cf1b48ff8648dd5c8f562593",

＂remark":＂测试备注-开票订单号：TEST********201731637743"，

"responseTimestamp": "2020-01-07 20:31:49",

<!-- 105 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"resultCode": "SUCCESS",

＂resultMsg":＂红冲申请成功！”，

"reverseCheckCode":"",

"reverseCipherCode": "",

"reverseDate": null,

"reverselnvoiceCode": "",

"reverselnvoiceNo":"",

"sellerAccount": "****************",

＂sellerAddress":＂武汉＂，

＂sellerBank":＂中国银行＂，

＂sellerName":＂测试开票商户＂，

"sellerTaxCode": "****************",

"sellerTelephone": "***********",

"srcReserve": "",

"status": "REVERSING",

"storeld": "3fca1775",

"storeName": "",

"taxMethod": "NORMAL",

"terminalld": "********",

"totalPrice": 167.9245283,

"totalPricelncludingTax": 178,

"totalTax": 10.0754717,

"updateTime": "2020-01-07 20:31:49",

"sign":

"A3AE797A19BB1FD88FB869815679C48E2B124F2FC34C60B49216F7C95FA6CF1C"

}

# 12.7退货发票

## 12.7.1 请求

{

"requestTimestamp":"********102409",

"msgSrc":"EXTERNAL_TEST",

"sign":"8F1B51C884151829C638C473F25DE6C7B3ADDDCCD820ECOFEA58917A8B0E7AEA",

"msgld":"09976e1b-79e7-de9f-da3c-66b9d4555bfa",

"srcReserve":"",

"msgType":"return",

"merchantld":"***************",

"terminalld":"********",

"merOrderld":"TEST********102409695206",

"merOrderDate":"********"

｝了

<!-- 106 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

## 12.7.2 响应

**｝了**

"msgld":"09976e1b-79e7-de9f-da3c-66b9d4555bfa",

"msgSrc": "EXTERNAL_TEST",

$$"msgType":"return",$$

"responseTimestamp": "2024-04-16 17:42:28",

"resultCode":"BADTATUS",,

＂resultMsg":＂发票当前状态为：CANCELED，不能退货！"，

"srcReserve":"",

$$"subList":[],$$

$$"sign":$$

"BF5A8DA035540F63C1A15D858AF852AC25C9EBD5010FA195595C0C8EA67DC038"

}

# 12.8作废发票

## 12.8.1 请求

｝了

"srcReserve":"",

"msgType":"invalid",

"merOrderDate": "********",

"requestTimestamp": "********205627",

"merchantld": "***************",

"msgSrc": "EXTERNAL_TEST",

"sign":

"AE27ACCC3F30C84A8C42E1CCC3A976D7********************************",

"msgld": "61d617bc-d5a4-3f40-1ce6-f1b290bddcbf",

"terminalld": "********",

＂invalidPerson":＂作废人＂，

"merOrderld": "********08593059263907"

}

## 12.8.2 响应

｝了

"buyerAccount":"",

"buyerAddress":"",

<!-- 107 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"buyerBank":"",

＂buyerName":＂广东爱啡餐饮管理有限公司＂，

"buyerTaxCode":"",

"buyerTelephone":"",

"checkCode": "79809457063786317219",

＂checker":＂李四＂，

"cipherCode":

"&lt;645++067++2954&gt;795&gt;80035/&gt;1/96+&gt;17&lt;3&gt;&gt;00*/385&gt;&lt;8&gt;6*89+72913-689&gt;&gt;61111-329+-&gt;90&lt;65-/8896+989+",

"createTime":"2020-01-0708:59:45",,

"deductionAmount":0,

"deviceNo":"",,

"drawer":＂王五＂，

"errMsg":：＂作废申请成功！”，

"extend1":"",

$$"extend2":"",$$

"goodsDetail":

"[{\"attribute":"o\","freeTaxType\":"\",\"index|":1,|"model\"::"\",name 管理费(201805)\",\"preferPolicyFlag\":\"0\",\"price\":87.*************,\"pricelncludingTax\":93.18,\"quantity\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\＂企业管理服务\",\"sn\":\"3040801010000000000\",\"tax\":5.**************,\"taxRate\":6,\"unit\":\"\",\"uni tPrice\":87.*************,\"unitPricelncludingTax\":93.18,\"vatSpecial\":\"\"},{\"attribute\":\"0\",\"freeTaxType\":\"\",\"index\":2,\"model\":\"\",\"name\":\＂综合管理费(201807)\",\"preferPolicyFlag\":\"0\",\"price\":594.3396226415094,\"pricelncludingTax\":630.0,＼"quantity\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\＂企业管理服务\",\"sn\":\"3040801010000000000\",\"tax\":35.660377358490564,\"taxRate\":6,\"unit\":\"\",\"unitPrice\":594.3396226415094,\"unitPricelncludingTax\":630.0,\"vatSpecial\":\"\"},{\"attribute\＂:\"0\",\"freeTaxType\":\"\",\"index\":3,\"model\":\"\",\"name\":\＂综合管理费(201808)\",\"preferPolicyFlag\":\"0\",\"price\":155.66037735849056,\"priceIncludingTax\":165.0,＼"quantity\":1.0,\"showQuantityAndUnitPrice\":\"1\",\"simpleName\":\＂企业管理服务\",\"sn\":\"3040801010000000000\",\"tax\":9.339622641509434,,"taxRate|":6,|"unit|":|"",|"u nitPrice\":155.66037735849056,\"unitPricelncludingTax\":165.0,\"vatSpecial\":\"\"}]",

"invalidDate":null,

"invalidInvoiceCode":"",

"invalidInvoiceNo": "",

"invoiceCode": "0010708590",

"invoiceMaterial": "PAPER",

"invoiceNo": "70859470",

"invoiceType": "PLAIN",

"issueDate": "2020-01-07 09:00:00",

"merOrderDate": "********" ,

"merOrderld": "********08593059263907",

"merWxAppld":"",

"merWxOrderld":"",

<!-- 108 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"merchantld": "***************",

＂merchantName":＂测试开票终端＂，

"msgld": "61d617bc-d5a4-3f40-1ce6-f1b290bddcbf",

"msgSrc": "EXTERNAL_TEST",

"msgType": "invalid",

"notifyEmail": "<EMAIL>",

"notifyMerEmail": "",

"notifyMobileNo":"",

"notifyUrl":"",

＂payee":＂张三＂，

"pdf":"",

"pdfPreviewUrl":"",

"pdfUrl":"",

"qrCode":"",

"qrCodeld": "********71aadb3ab5a443818895e6898c995b29",

＂remark":＂测试备注-开票订单号：********08593059263907"，

"responseTimestamp": "2020-01-07 20:56:37",

"resultCode": "SUCCESS",

＂resultMsg":＂作废申请成功！"，

"reverseCheckCode":"",

"reverseCipherCode":"",

"reverseDate": null,

"reverselnvoiceCode": "",

"reverselnvoiceNo":"",

"sellerAccount": "****************",

＂sellerAddress":＂武汉＂，

＂sellerBank":＂中国银行＂，

＂sellerName":＂测试开票商户＂，

"sellerTaxCode": "****************",

"sellerTelephone": "***********",

"srcReserve": "",

"status": "INVALIDED",

"storeld": "3fca1775",

"storeName":"",

"taxMethod": "NORMAL",

"terminalld": "********",

"totalPrice": 837.********,

"totalPricelncludingTax": 888.18,

"totalTax": 50.********,

"updateTime": "2020-01-07 20:56:37",

"sign":

"6B42F0CA990FA12132C6DCAFC77A384B9E0439F4A1123AAECA651DE441C0CF64"

}

<!-- 109 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 12.9打印发票

## 12.9.1 请求

{

"srcReserve": "",

"msgType":"print",

"merOrderDate": "********",

"requestTimestamp": "********205915",

"merchantld": "***************",

"msgSrc": "EXTERNAL_TEST",

"sign":

"B8BB21D35BA5A1CEE5A500ACFD44DF6389FF02C67A6A02201D4A87ADBAA718E7",

"msgld": "b80b4ec1-bfb7-2a7e-5514-70ee025e90f6",

"terminalld": "********",

"merOrderld": "TEST20191219080837288167"

}

## 12.9.2 响应

｝了

"msgld": "b80b4ec1-bfb7-2a7e-5514-70ee025e90f6",

"msgSrc": "EXTERNAL_TEST",

"msgType": "print",

"responseTimestamp": "2020-01-07 20:59:51",

"resultCode": "SUCCESS",

＂resultMsg":＂发票打印申请成功！”，

"srcReserve": "",

$$"sign":$$

"F7E14330A636496052A90CBD48E5AFECAF0A246045C1368D880AA6980B3E870D"

}

# 12.10 下载版式文件

## 12.10.1请求

｝了

"srcReserve":"",

$$"msgType":"pickup",$$

"merOrderDate": "********",

<!-- 110 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"requestTimestamp": "********203531",

"merchantld": "***************",

"msgSrc": "EXTERNAL_TEST",

"sign":

"C466B2A895222951C4041EC4A3FB6DE2AE91AA5ADFDD692017FF589DC9949D22",

"msgld": "8184ed55-9755-a2d3-8670-7418fe4b48cf",

"terminalld": "********",

"merOrderld": "TEST********201731637743"

}

## 12.10.2响应

｝了

"msgld":"8184ed55-9755-a2d3-8670-7418fe4b48cf",

"msgSrc":"EXTERNAL_TEST",

"msgType":"pickup",

"pdf":"JVBERi0xLjQKJeLjz9MKMiAwlG9iago8PC9MZW5ndGggMTAvRmlsdGVyLOZsYXRIR GVjb2RIPj5. 11

"pdflmg":"",

"pdfUrl":"https://mobl-test.chinaums.com/fapiao-portal/d/0QBxWL4",

"responseTimestamp":"2020-01-07 20:35:48",

"resultCode":"SUCCESS",

＂resultMsg":＂下载电子发票版式文件成功！"，

"srcReserve":"",

"sign":"9B9345C0C214C0FD98DDC74D5EC0C0B3ACFB005D63D9D10ECE85174FF55123

$$ED"$$

}

# 12.11 推送发票

## 12.11.1请求

1了

"srcReserve":"",

"notifyEMail": "<EMAIL>",

"msgType":"notify",

"merOrderDate": "********",

"requestTimestamp": "********204325",

"merchantlId": "***************",

"msgSrc": "EXTERNAL_TEST",

$$"sign":$$

"7F2A8069A671E64750CA99171330962DB488820D0E43A67F5456185752411E47",

<!-- 111 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"msgld": "f4982bd5-7bad-f1a1-6910-a9b4e2188607",

"terminalld": "********",

"notifyMobileNo":"",

"merOrderld": "TEST********201731637743"

}

## 12.11.2响应

｝了

"msgld": "f4982bd5-7bad-f1a1-6910-a9b4e2188607",

"msgSrc": "EXTERNAL_TEST",

"msgType": "notify",

"responseTimestamp": "2020-01-07 20:44:20",

"resultCode": "SUCCESS",

＂resultMsg":＂开票结果消费者通知成功＂，

"srcReserve": "",

"sign":

"270B6237EBFA49927D53D4063135A81F106919049947B6001810A56ED508DA0C"

}

# 12.12 抬头模糊查询

## 12.12.1请求

**｝了**

"srcReserve": "",

"msgType": "query.fuzzy.title",

"requestTimestamp": "********204012",

"msgSrc": "EXTERNAL_TEST",

＂name":＂银联商务”，

"sign":

"6C7D9919CDB3A66DD898688FE2435C9A57799B310D6F534677B88265336761C8",

"msgld": "0e16272a-2d5e-2076-4916-ae3cdc45e929",

"taxCode":""

}

## 12.12.2响应

｝了

"msgld": "0e16272a-2d5e-2076-4916-ae3cdc45e929",

"msgSrc": "EXTERNAL_TEST",

<!-- 112 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"msgType": "query.fuzzy.title",

"responseTimestamp": "2020-01-07 20:40:17",

"resultCode": "SUCCESS",

＂resultMsg":＂抬头查询成功＂，

"srcReserve":"",

"titleList":[

{

"account":"",

"address":"",

"bank":"",

"name":"✓"东银联商务有限公司＂，

"taxCode": "91440101708308561T",

"telephone":""

},

{

"account": "1001258209300058338",

＂address":＂中国（上海）自由贸易试验区郭守敬路351号1号楼518室＂，

＂bank":＂工行上海市分行不夜城支行＂，

＂name":＂上海银联商务有限公司＂，

"taxCode":"913100007472577280",

"telephone": "********"

},

{1

"account": **"7251110182600173718",**

＂address":＂西安市碑林区长安北路111号华尔国际大厦18层＂，

＂bank":＂中信银行西安分行营业部＂，

"name":＂银联商务股份有限公司陕西分公司＂，

"taxCode":"916101037428368196"

"telephone": "029-********"

},

{1

"account":"",

"address":"",

"bank":"",

＂name":＂腾冲联银商务有限公司＂，

"taxCode": "915305223466232131",

"telephone":""

},

{1

"account": "1001281219006743953",

＂address":＂上海市张江高科技园区张衡路********号＂，

＂bank":＂工商银行上海浦东开发区支行＂，

＂name":＂银联商务股份有限公司＂，

"taxCode": "91310000734572833M",

<!-- 113 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"telephone":"021-********"

},

{

"account": "*************",

"address":＂北京市海淀区车公庄西路乙19号B座10层＂，

"bank":" 上海浦东发展银行股份有限公司北京分行营业部＂，

"name":＂北京银联商务有限公司＂，

"taxCode": "91110108740446294L",

"telephone":"********"

},

{

"account": "",

＂address":＂宁夏银川银联商务有限公司＂，

$$"bank":"",$$

＂name":＂银联商务有限公司＂，

"taxCode": "640121992101242216",

"telephone":11

},

｝了

"account":"",

$$"address":"",$$

$$"bank":"",$$

＂name":＂北京银联商务有限公司工会委员会＂，

"taxCode": **"811100000854776408",**

"telephone": 11

｝了

],

"sign":

"6145791157B4B700001963BF6EAF8C6B3C5973E32F913FB93A2038A0AC071E41"

}

# 12.13 发票查验

## 12.13.1请求

{

"msgType":"inspect",

"requestTimestamp": "2023-11-23 17:14:47",

"totalPrice": "76106.19",

"msgSrc": "PUBLIC_TEST",

$$"sign":$$

"D5D5A2174D63E27694AF42FED6FB8B1C42E5DED5A0A39DD5CE9B0A9C1D0DF6F6",

"msgld": "a2583931401944ddabf4c74497e2941e",

<!-- 114 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"terminalld": "********",

"invoiceCode": "************",

＂srcReserve":＂发票查验＂，

"merchantld": "***************",

"invoiceType": "03",

"invoiceNo": "********",

"issueDate": "2023-11-11"

}

## 12.13.2响应

{

"buyerAccount":"",

"buyerAddress":"",

＂buyerName":＂叶＊*"，

"buyerTaxCode":"4414**********2512",

"buyerTelephone":"",

"checkCode":"",

"deviceNo": "************",

"goodsDetailI":[],

"inspectState": "INSPECTED",

"invalidStatus": "N",

"invoiceCode": "************",

"invoiceNo": "********",

"invoiceType": "03",

"issueDate": "Sat Nov 11 00:00:00 CST 2023",

"msgld": "a2583931401944ddabf4c74497e2941e",

"msgSrc": "PUBLIC_TEST",

"msgType": "inspect",

"remark":"",

"responseTimestamp": "2023-11-23 17:14:16",

"resultCode": "SUCCESS",

＂resultMsg":＂发票查验成功＂，

"sellerAccount":"",

"sellerAddress":"",

＂sellerName":＂惠州市悦景新能源汽车有限公司＂，

"sellerTaxCode": "91441303MA56FE4K0B",

"sellerTelephone":"",

＂srcReserve":＂发票查验＂，

"subList":[],

"totalPrice": "76106.19",

"totalPricelncludingTax": "86000.0",

"totalTax": "9893.81",

<!-- 115 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"sign":

"C6550AFE00D795031D088DCDF1AF95B8269827CE0899E55B8E2A54FAB243BD12"

1}

# 12.14 数电登陆获取短信验证码

## 12.14.1请求

｝了

＂srcReserve":＂数电登陆获取短信验证码＂，

"msgType": "digital.elec.getSmsCode",

"requestTimestamp": "2024-08-06 17:01:27",

"merchantld": "***************",

"msgSrc": "PUBLIC_TEST",

"loginName": "135****8979",

"sign":

"6495C4F475FFB7BDA9CD3ECB9CFF45AFA346C6392BCAF2693462D3D4EA7A14E4",

"msgld": "68bf50d853ca4dc2a211590add4d65a4",

"terminalld": "********"

}

## 12.14.2响应

｝了

"msgld":"68bf50d853ca4dc2a211590add4d65a4",

"msgSrc": "PUBLIC_TEST",

"msgType": "digital.elec.getSmsCode",

"responseTimestamp": "2024-08-06 17:01:19",

"resultCode": "SUCCESS",

＂resultMsg":＂成功＂，

＂srcReserve":＂数电登陆获取短信验证码＂，

"sign":

"673D05F0AD33ABBEA1E305949848F102F74C3F2D7738FF656744119DA0EAE019"

1}

<!-- 116 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 12.15 数电登陆认证

## 12.15.1请求

{

＂srcReserve":＂数电登陆认证”，

"msgType": "digital.elec.userLogin",

"requestTimestamp": "2024-08-06 17:01:27",

"merchantld": "***************",

"msgSrc": "PUBLIC_TEST",

"loginName": "135****8979",

"smsCode": "123456",

"sign":

"6495C4F475FFB7BDA9CD3ECB9CFF45AFA346C6392BCAF2693462D3D4EA7A14E4",

"msgld": "68bf50d853ca4dc2a211590add4d65a4",

"terminalld": "********"

}

## 12.15.2响应

{

"msgld": "68bf50d853ca4dc2a211590add4d65a4",

"msgSrc": "PUBLIC_TEST",

"msgType": "digital.elec.userLogin",

"responseTimestamp": "2024-08-06 17:01:19",

"resultCode": "SUCCESS",

＂resultMsg":＂数电登录认证成功＂，

＂srcReserve":＂数电登陆认证＂，

"sign":

"673D05F0AD33ABBEA1E305949848F102F74C3F2D7738FF656744119DA0EAE019"

}

# 12.16 获取数电身份认证二维码

## 12.16.1请求

｝了

＂srcReserve":＂获取数电身份认证二维码＂，

"msgType": "digital.get.authQrCode",

<!-- 117 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"requestTimestamp": "2024-08-06 17:09:12",

"merchantld": "***************",

"msgSrc": "PUBLIC_TEST",

"loginName": "135****8979",

"sign":

"A2091956CE31622EF0AEB323B52FDDD95AEE81607C99F53C2AC9640AEAA329D1",

"msgld": "fd5b846e47964b709d3f3a65e96e59a9",

"terminalld": "********"

}

## 12.16.2响应

｝了

"msgld": "fd5b846e47964b709d3f3a65e96e59a9",

"msgSrc": "PUBLIC_TEST",

"msgType": "digital.get.authQrCode",

"qrCode":

"data:image/jpg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQ NDAsLDBkSEw8UHRofHh0aHBwgJC4nlClslxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQw LDBgNDRgylRwhMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjlyMjL/wAA RCAEsASwDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAA（省略图片 Base64数据．.....)"，

"responseTimestamp": "2024-08-06 17:09:00",

"resultCode": "SUCCESS",

＂resultMsg":＂获取全电身份认证二维码成功＂，

＂srcReserve":＂获取数电身份认证二维码”，

＂taxRealName":＂张三＂，

"sign":

"CBF8473F26DDC54069E2D3FA05AFF5390309DA6FE2AC0F69E265E60B55A47D71"

}

# 12.17 全电确认是否刷脸

## 12.17.1请求

｝了

"merchantld": "***************",

"terminalld": "********",

"authld":"1254858478",

"msgld": "2309049561704ef5a9d67fa29049e886",

"msgType": "digital.confirm.faceSwiping",

"msgSrc": "TEST",

<!-- 118 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

"requestTimestamp": "20250108102427",

"sign":

"7E33A9332EF0B17373D7DDA68BFA47085956046EE3570AFD4AE18577C9781B7F"

}

## 12.17.2响应

1了

"authld":"",

"msgld": "2309049561704ef5a9d67fa29049e886",

"msgSrc": "TEST",

"msgType": "digital.confirm.faceSwiping",

"qrCode":"",

"responseTimestamp": "2025-01-08 16:18:42",

"resultCode": "BAD_STATUS",

＂resultMsg":＂服务商不支持本操作＂，

"srcReserve":"",

"subList":[],

"sign": "5CDEDDDEFE38285C74B07B5020A7DC88"

｝了

**13错误码说明**

<table border="1" ><tr>
<td>错误码</td>
<td>错误描述</td>
<td>原因和应对措施</td>
<td>系统失败</td>
</tr><tr>
<td>SUCCESS </td>
<td>成功</td>
<td>无。</td>
<td></td>
</tr><tr>
<td>INTERNAL_ERROR </td>
<td>内部错误</td>
<td>系统错误，请联系技术支持。</td>
<td>是</td>
</tr><tr>
<td>BAD_REQUEST</td>
<td>请求报文有错</td>
<td>报文格式有错误，请对照文档检查报文格式。</td>
<td>是</td>
</tr><tr>
<td>NO_SERVICE</td>
<td>没有能处理请求<br>msgtype 的服务</td>
<td>msgType 错误，请检查文档，msgType是否拼写正确。</td>
<td>是</td>
</tr><tr>
<td>TIMEOUT </td>
<td>处理超时</td>
<td>处理超时，很可能是微信和支付宝的网络请求没应答，建议重试或者撤销交易。</td>
<td>是</td>
</tr><tr>
<td>NO_MERCHANT</td>
<td>找不到商户信息</td>
<td>对应的merchantId+terminalId不正确</td>
<td>否</td>
</tr><tr>
<td>NO_INVOICE</td>
<td>找不到发票信息</td>
<td>对应的mid+merOrderId不正确，无法找到原交易，请检查merOrderId 是否跟原交易一致。<br>或者qrCodeId 不正确。</td>
<td>否</td>
</tr><tr>
<td>DUP_INVOICE</td>
<td>重复开票</td>
<td>商户对同一笔交易<br>（merOrderDate+merOrderId)，重复发起生成开票二维码请求</td>
<td>否</td>
</tr><tr>
<td>DUP_SCAN</td>
<td>重复扫码</td>
<td>对同一个二维码，并发地扫码开票</td>
<td>否</td>
</tr><tr>
<td>API_ERROR</td>
<td>第三方开票接口失<br>败</td>
<td>瑞宏等第三方发票服务商的调用接口出<br>错</td>
<td>否</td>
</tr><tr>
<td>NET_ERROR</td>
<td>跟支付包通讯出问<br>题，包括请求发送异常，报文应答不是<br>200 ，请求被取消，<br>应答超时等。</td>
<td>通讯问题，联系运行检查网络情况。</td>
<td>是</td>
</tr><tr>
<td>ABNORMAL_REQUEST_TIME</td>
<td>请求时间异常</td>
<td>请求终端或者平台的系统时间不正常，请检查系统时间。</td>
<td>是</td>
</tr><tr>
<td>BAD_SIGN</td>
<td>签名错误</td>
<td>报文签名错误，请联系技术指导签名算<br>法。</td>
<td>是</td>
</tr></table>

<!-- 119 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

# 14常见错误

<table border="1" ><tr>
<td>错误描述</td>
<td>原因和应对措施</td>
</tr><tr>
<td>开票提交失败，发票渠道异常：调用［发票开具接口］失败，原因：开票终端标识［xxxxxx ］不存在！</td>
<td>1 、商户注册时配置的开票点代码与百望云添加的开票终端标识不一致<br>2 、百望云未添加开票终端</td>
</tr><tr>
<td>百望通讯出错，验证失败！</td>
<td>3 、核对接入代码</td>
</tr><tr>
<td>报文验签失败</td>
<td>1 、确认使用的签名密钥是否正确<br>2 、确认待签名的报文格式是否正确<br>3 、确认签名算法是否正确<br>4 、尽量使用提供的签名工具类</td>
</tr><tr>
<td>抬头提交失败，此税控设备编号尚未与客户端绑定</td>
<td>1 、商户注册时配置的税号和税控盘号与开票助手注册填写的税号和税控盘号不一致<br>2 、开票助手未成功启动<br>3 、该商户在开票助手未成功登陆</td>
</tr><tr>
<td>发票渠道异常：验签失败，请确认</td>
<td>1、用友appid 和ca 证书不匹配</td>
</tr><tr>
<td>t message 0.6 [*2028-09-28 09:24:58.651 0008 [1-5-thred-1376]3000904300]c.c.f.p.b.trasport.Beangrasport-百讯http://i.baag.com/pi/service bapt<br>Jer.Tang.NTTPoisterEcetion ul1<br>at con.beriang.behvegclsad.client.Srienga/dPKliet.sedtto(aieon Sorce)<br>at can.beisag.heivengclad.client.BeiealdPiet.rc(ininon Sorce) </td>
<td>1 、开票点代码不正确</td>
</tr><tr>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
</tr><tr>
<td></td>
<td></td>
</tr></table>

<!-- 120 -->

<!-- 银联商务 Chinaums -->

<!-- 银联商务发票服务平台标准接口文档 -->

<!-- 121 -->

