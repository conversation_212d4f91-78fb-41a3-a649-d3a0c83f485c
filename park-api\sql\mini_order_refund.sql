-- ----------------------------
-- Table structure for mini_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `mini_order_refund`;
CREATE TABLE `mini_order_refund` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trade_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单号',
  `refund_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '退款原因',
  `refund_type` tinyint NOT NULL COMMENT '退款类型（1-临停订单，2-VIP会员）',
  `original_amount` decimal(10,2) NOT NULL COMMENT '原订单金额',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `create_by` bigint NOT NULL COMMENT '创建者',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_trade_id` (`trade_id`),
  KEY `idx_refund_type` (`refund_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单退款记录表';
