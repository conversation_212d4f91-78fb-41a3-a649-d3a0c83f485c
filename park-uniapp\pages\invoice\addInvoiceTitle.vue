<template>
  <view class="invoice-form-container">
    <!-- 表单区域 -->
    <view class="form-content">
      <!-- 发票类型 -->
      <view class="form-row">
        <view class="form-label">发票类型</view>
        <view class="form-options">
          <view class="option-item" :class="{ active: formData.invoiceType === 0 }" @tap="setInvoiceType(0)">
            <text class="option-text">普通发票</text>
          </view>
          <view class="option-item" :class="{ active: formData.invoiceType === 1 }" @tap="setInvoiceType(1)">
            <text class="option-text">专用发票</text>
          </view>
        </view>
      </view>

      <!-- 抬头类型（仅普通发票显示） -->
      <view class="form-row" v-if="formData.invoiceType === 0">
        <view class="form-label">抬头类型</view>
        <view class="form-options">
          <view class="option-item" :class="{ active: formData.titleType === 0 }" @tap="setTitleType(0)">
            <text class="option-text">个人</text>
          </view>
          <view class="option-item" :class="{ active: formData.titleType === 1 }" @tap="setTitleType(1)">
            <text class="option-text">单位</text>
          </view>
        </view>
      </view>

      <!-- 发票抬头 -->
      <view class="form-row">
        <view class="form-label">发票抬头</view>
        <view class="form-input">
          <input v-model="formData.invoiceTitleContent" :placeholder="(formData.invoiceType === 1 || formData.titleType === 1) ? '请输入发票抬头' : '请输入发票抬头'" class="input-field" />
        </view>
      </view>

      <!-- 单位信息（专用发票或普通发票单位类型时显示） -->
      <template v-if="formData.invoiceType === 1 || formData.titleType === 1">
        <view class="form-row">
          <view class="form-label">单位税号</view>
          <view class="form-input">
            <input v-model="formData.unitDutyParagraph" placeholder="请输入单位税号" class="input-field" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-label">注册地址</view>
          <view class="form-input">
            <input v-model="formData.registerAddress" placeholder="请输入注册地址" class="input-field" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-label">注册电话</view>
          <view class="form-input">
            <input v-model="formData.registerPhone" placeholder="请输入注册电话" class="input-field" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-label">开户银行</view>
          <view class="form-input">
            <input v-model="formData.depositBank" placeholder="请输入开户银行" class="input-field" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-label">银行账户</view>
          <view class="form-input">
            <input v-model="formData.bankAccount" placeholder="请输入银行账户" class="input-field" />
          </view>
        </view>
      </template>

      <!-- 保存按钮 -->
      <view class="save-button" @tap="handleSubmit">
        <text class="save-text">{{ isEdit ? "保存" : "添加" }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { addInvoiceTitle, editInvoiceTitle } from "@/api/invoice";

const isEdit = ref(false);

// 表单数据，设置默认值
const formData = reactive({
  id: null,
  invoiceType: 0,  // 默认普通发票
  titleType: 0,    // 默认个人
  invoiceTitleContent: "",
  unitDutyParagraph: "",
  registerAddress: "",
  registerPhone: "",
  depositBank: "",
  bankAccount: "",
});

// 临时存储单位信息，避免切换类型时数据丢失
const tempUnitData = reactive({
  unitDutyParagraph: "",
  registerAddress: "",
  registerPhone: "",
  depositBank: "",
  bankAccount: "",
});

onLoad((options) => {
  if (options.isEdit === "true") {
    isEdit.value = true;
    if (options.obj) {
      const obj = JSON.parse(decodeURIComponent(options.obj));
      Object.assign(formData, obj);
      // 专用发票自动设置为单位类型
      if (formData.invoiceType === 1) {
        formData.titleType = 1;
      }
      // 编辑时保存单位数据到临时存储
      if (formData.titleType === 1 || formData.invoiceType === 1) {
        Object.assign(tempUnitData, {
          unitDutyParagraph: formData.unitDutyParagraph,
          registerAddress: formData.registerAddress,
          registerPhone: formData.registerPhone,
          depositBank: formData.depositBank,
          bankAccount: formData.bankAccount,
        });
      }
    }
  }
});

// 设置发票类型
const setInvoiceType = (type) => {
  // 在切换前保存当前的单位信息
  if (formData.titleType === 1 || formData.invoiceType === 1) {
    Object.assign(tempUnitData, {
      unitDutyParagraph: formData.unitDutyParagraph,
      registerAddress: formData.registerAddress,
      registerPhone: formData.registerPhone,
      depositBank: formData.depositBank,
      bankAccount: formData.bankAccount,
    });
  }

  formData.invoiceType = type;
  if (type === 1) {
    // 专用发票自动设置为单位类型
    formData.titleType = 1;
    // 恢复单位信息
    Object.assign(formData, tempUnitData);
  } else {
    // 普通发票重置为个人类型，清空单位信息
    formData.titleType = 0;
    formData.unitDutyParagraph = "";
    formData.registerAddress = "";
    formData.registerPhone = "";
    formData.depositBank = "";
    formData.bankAccount = "";
  }
};

// 设置抬头类型
const setTitleType = (type) => {
  // 在切换前保存当前的单位信息
  if (formData.titleType === 1) {
    Object.assign(tempUnitData, {
      unitDutyParagraph: formData.unitDutyParagraph,
      registerAddress: formData.registerAddress,
      registerPhone: formData.registerPhone,
      depositBank: formData.depositBank,
      bankAccount: formData.bankAccount,
    });
  }

  formData.titleType = type;
  // 切换到个人时清空单位相关信息
  if (type === 0) {
    formData.unitDutyParagraph = "";
    formData.registerAddress = "";
    formData.registerPhone = "";
    formData.depositBank = "";
    formData.bankAccount = "";
  } else {
    // 切换到单位时恢复之前保存的信息
    Object.assign(formData, tempUnitData);
  }
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!formData.invoiceTitleContent.trim()) {
    uni.showToast({
      title: (formData.invoiceType === 1 || formData.titleType === 1) ? "请输入单位名称" : "请输入发票抬头",
      icon: "none",
    });
    return;
  }

  // 专用发票或单位类型需要单位税号
  if ((formData.invoiceType === 1 || formData.titleType === 1) && !formData.unitDutyParagraph.trim()) {
    uni.showToast({
      title: "请输入单位税号",
      icon: "none",
    });
    return;
  }

  uni.showLoading({
    title: isEdit.value ? "保存中..." : "添加中...",
    mask: true
  });

  try {
    const apiCall = isEdit.value ? editInvoiceTitle : addInvoiceTitle;
    const res = await apiCall(formData);
    
    uni.hideLoading();
    
    if (res.code === 200) {
      uni.showToast({
        title: isEdit.value ? "保存成功" : "添加成功",
        icon: "success",
        duration: 1500
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      // 显示后端返回的具体错误信息，不返回页面
      uni.showToast({
        title: res.msg || (isEdit.value ? "保存失败" : "添加失败"),
        icon: "none",
        duration: 3000
      });
    }
  } catch (error) {
    console.error("操作失败:", error);
    uni.hideLoading();
    
    // 显示捕获到的异常信息
    const errorMsg = error.msg || error.message || (isEdit.value ? "保存失败，请重试" : "添加失败，请重试");
    uni.showToast({
      title: errorMsg,
      icon: "none",
      duration: 3000
    });
  }
};
</script>

<style lang="scss" scoped>
.invoice-form-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
}

.form-content {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .form-label {
    font-size: 30rpx;
    color: #1f2937;
    min-width: 160rpx;
    flex-shrink: 0;
  }

  .form-options {
    display: flex;
    gap: 16rpx;
    flex: 1;
    margin-left: 20rpx;

    .option-item {
      flex: 1;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid #e5e7eb;
      border-radius: 12rpx;
      background: #ffffff;
      transition: all 0.3s ease;

      &.active {
        border-color: #4BA1FC;
        background: #f0f9ff;
        
        .option-text {
          color: #4BA1FC;
          font-weight: 500;
        }
      }

      &:active {
        transform: scale(0.98);
      }

      .option-text {
        font-size: 26rpx;
        color: #6b7280;
        transition: all 0.3s ease;
      }
    }
  }

  .form-input {
    flex: 1;
    margin-left: 20rpx;

    .input-field {
      width: 100%;
      height: 50rpx;
      padding: 0 8rpx 5rpx 8rpx;
      border: none;
      border-bottom: 2rpx solid #e5e7eb;
      background: transparent;
      font-size: 28rpx;
      color: #1f2937;
      transition: all 0.3s ease;

      &:focus {
        border-bottom-color: #4BA1FC;
        outline: none;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }
  }
}

.save-button {
  margin-top: 40rpx;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  border-radius: 16rpx;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(75, 161, 252, 0.25);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1rpx);
    box-shadow: 0 2rpx 8rpx rgba(75, 161, 252, 0.2);
  }

  .save-text {
    font-size: 30rpx;
    font-weight: 500;
    color: #ffffff;
    letter-spacing: 1rpx;
  }
}

// 美化单位信息部分的过渡效果
.form-row {
  transition: all 0.4s ease;
  
  &[data-v-enter-active],
  &[data-v-leave-active] {
    transition: all 0.4s ease;
  }
  
  &[data-v-enter-from],
  &[data-v-leave-to] {
    opacity: 0;
    transform: translateY(-20rpx);
  }
}
</style>
