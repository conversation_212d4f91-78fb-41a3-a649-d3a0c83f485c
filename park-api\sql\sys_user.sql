/*
 Navicat MySQL Data Transfer

 Source Server         : 远程测试数据库
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : 127.0.0.1:3306
 Source Schema         : parknew

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 01/08/2025 15:33:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `delete_flag` tinyint NULL DEFAULT 0,
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 100, 'admin', '超级管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$mA5pzdZ7zAsIeBsOaeR/VOV2SBmOXbVQJ1acL7npXmaTd3B4Jh9SC', '0', 0, '***************', '2025-08-01 09:37:04', '2025-07-27 13:23:45', 1, '2025-06-04 11:01:40', 1, '2025-08-01 09:37:04', '管理员');
INSERT INTO `sys_user` VALUES (2, 100, 'ry', '管理员', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', 2, '127.0.0.1', '2025-06-04 11:01:40', '2025-06-04 11:01:40', 1, '2025-06-04 11:01:40', 1, '2025-07-30 09:39:48', '测试员');
INSERT INTO `sys_user` VALUES (3, 100, 'zhangsan', '李四', '00', '', '15270369865', '2', '', '$2a$10$VsQxeuT2lQXMuKlpaqHKDuPZUSglW31kp6Le6TMhHxBWvyaR/18im', '0', 2, '***************', '2025-07-29 15:59:35', '2025-07-29 16:07:20', NULL, '2025-07-29 15:49:21', NULL, '2025-07-30 09:39:46', NULL);
INSERT INTO `sys_user` VALUES (4, 110, 'Lgjyadmin1', '1号管理员', '00', '', '', '0', '', '$2a$10$bNw0t8Huwt/L43xsDn6ft.24IcX3FkEJFiP2VuSPvW7aQL3FxoeE2', '0', 0, '************', '2025-07-30 10:26:50', NULL, NULL, '2025-07-30 10:20:01', NULL, '2025-07-30 10:26:50', '1号测试管理员');
INSERT INTO `sys_user` VALUES (5, 110, 'Lgjyadmin2', '2号管理员', '00', '', '', '0', '', '$2a$10$TvEOUMJCDFbnZmlyrr6Bt.UkzQ6xRthhMnKS210FOJkJMC598G/dC', '0', 0, '', NULL, NULL, NULL, '2025-07-30 10:20:43', NULL, '2025-07-30 10:20:43', NULL);
INSERT INTO `sys_user` VALUES (6, 110, 'Lgjyadmin3', '3号管理员', '00', '', '', '0', '', '$2a$10$ZQBbCe95medB0ZZVwXObSetMkxMtL6sa.ohjRYJQiG16puhuejr7S', '0', 0, '************', '2025-07-30 11:05:22', NULL, NULL, '2025-07-30 10:21:21', NULL, '2025-07-30 11:05:22', NULL);
INSERT INTO `sys_user` VALUES (7, 110, 'Lgjyadmin4', '4号管理员', '00', '', '', '0', '', '$2a$10$zRjjPTj1PczOM6vWa3dHhO0Udps3yZ0DZ3BpV4eqin1jDPvd.tPD6', '0', 0, '************', '2025-07-30 10:46:38', NULL, NULL, '2025-07-30 10:25:55', NULL, '2025-07-30 10:46:38', NULL);
INSERT INTO `sys_user` VALUES (8, NULL, 'Lgjyadmin5', '4号管理员', '00', '', '', '0', '', '$2a$10$ZiWlSdmlJTa2EHT9.upxn.D2ssRAlptSLCGX/mKR7JPJtbqA9HPm.', '0', 0, '************', '2025-07-30 10:37:06', NULL, NULL, '2025-07-30 10:26:18', NULL, '2025-07-30 10:37:05', NULL);

SET FOREIGN_KEY_CHECKS = 1;
