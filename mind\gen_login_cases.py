import pandas as pd

# 所有测试用例数据
data = [
    ["TC_001", "新用户手机号验证码注册登录", "1.网络正常\n2.用户未注册\n3.已同意用户协议", "1.输入手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "手机号：13800138000\n验证码：123456", "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.用户信息存储成功", "P0"],
    ["TC_002", "老用户手机号验证码登录", "1.网络正常\n2.用户已注册\n3.已同意用户协议", "1.输入已注册手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "手机号：13800138001\n验证码：123456", "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.更新用户openid", "P0"],
    ["TC_003", "老用户openid已存在且匹配", "1.网络正常\n2.用户已注册\n3.openid已绑定\n4.已同意用户协议", "1.输入已注册手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "手机号：13800138002\n验证码：123456", "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.不更新openid", "P0"],
    ["TC_004", "手机号格式错误", "1.网络正常", "1.输入错误格式手机号\n2.点击获取验证码", "手机号：1380013800", "显示\"手机号格式错误\"提示", "P1"],
    ["TC_005", "手机号为空", "1.网络正常", "1.不输入手机号\n2.点击获取验证码", "手机号：空", "显示\"请输入手机号\"提示", "P1"],
    ["TC_006", "验证码错误", "1.网络正常\n2.已获取验证码\n3.已同意用户协议", "1.输入正确手机号\n2.输入错误验证码\n3.点击登录", "手机号：13800138000\n验证码：000000", "显示\"验证码错误\"提示", "P1"],
    ["TC_007", "验证码为空", "1.网络正常\n2.已输入手机号\n3.已同意用户协议", "1.输入正确手机号\n2.不输入验证码\n3.点击登录", "手机号：13800138000\n验证码：空", "显示\"请输入正确的手机号或者验证码\"提示", "P1"],
    ["TC_008", "验证码过期", "1.网络正常\n2.验证码已过期（5分钟后）\n3.已同意用户协议", "1.输入正确手机号\n2.输入过期验证码\n3.点击登录", "手机号：13800138000\n验证码：123456（过期）", "显示\"验证码未发送或已过期\"提示", "P1"],
    ["TC_009", "未同意用户协议", "1.网络正常\n2.已获取验证码", "1.输入正确手机号\n2.输入正确验证码\n3.未勾选用户协议\n4.点击登录", "手机号：13800138000\n验证码：123456", "显示\"请阅读并同意用户协议和隐私政策\"提示", "P1"],
    ["TC_010", "频繁获取验证码", "1.网络正常\n2.已获取一次验证码", "1.输入手机号\n2.连续点击获取验证码", "手机号：13800138000", "显示\"禁止频繁获取验证码\"提示，按钮倒计时60秒", "P1"],
    ["TC_011", "网络异常", "1.网络断开", "1.输入手机号\n2.点击获取验证码", "手机号：13800138000", "显示网络错误提示", "P2"],
    ["TC_012", "获取微信code失败", "1.网络正常\n2.微信授权失败\n3.已同意用户协议", "1.输入正确手机号和验证码\n2.点击登录", "手机号：13800138000\n验证码：123456", "显示\"获取微信code失败\"提示", "P2"],
    ["TC_013", "用户被封禁", "1.网络正常\n2.用户状态为封禁（status=1）\n3.已同意用户协议", "1.输入被封禁用户手机号\n2.获取验证码\n3.输入验证码\n4.点击登录", "手机号：13800138003\n验证码：123456", "显示用户被封禁相关提示", "P1"],
    ["TC_014", "新用户微信一键登录注册", "1.网络正常\n2.微信授权正常\n3.用户未注册\n4.已同意用户协议", "1.勾选用户协议\n2.点击微信登录按钮\n3.授权获取手机号", "微信授权手机号：13900139000", "1.获取手机号成功\n2.自动注册新用户\n3.登录成功\n4.跳转到首页", "P0"],
    ["TC_015", "老用户微信一键登录", "1.网络正常\n2.微信授权正常\n3.用户已注册\n4.已同意用户协议", "1.勾选用户协议\n2.点击微信登录按钮\n3.授权获取手机号", "微信授权手机号：13900139001", "1.获取手机号成功\n2.登录成功\n3.更新用户openid\n4.跳转到首页", "P0"],
    ["TC_016", "未同意用户协议点击微信登录", "1.网络正常\n2.未勾选用户协议", "1.不勾选用户协议\n2.点击微信登录按钮", "无", "显示\"请阅读并同意用户协议和隐私政策\"提示", "P1"],
    ["TC_017", "微信授权失败", "1.网络正常\n2.已同意用户协议", "1.勾选用户协议\n2.点击微信登录按钮\n3.拒绝微信授权", "无", "显示\"获取微信授权失败\"提示", "P1"],
    ["TC_018", "获取手机号失败", "1.网络正常\n2.已同意用户协议\n3.微信授权成功", "1.勾选用户协议\n2.点击微信登录按钮\n3.拒绝手机号授权", "无", "显示\"获取手机号失败\"提示", "P1"],
    ["TC_019", "微信登录网络异常", "1.网络异常\n2.已同意用户协议", "1.勾选用户协议\n2.点击微信登录按钮", "无", "显示\"登录失败，请重试\"提示", "P2"],
    ["TC_020", "页面加载正常", "1.小程序启动正常", "1.进入登录页面", "无", "1.页面布局正常\n2.所有元素显示正常\n3.返回按钮显示正常", "P0"],
    ["TC_021", "手机号输入框限制", "1.进入登录页面", "1.在手机号输入框输入超长字符", "手机号：138001380001234", "输入框最多显示11位数字", "P1"],
    ["TC_022", "验证码输入框限制", "1.进入登录页面", "1.在验证码输入框输入超长字符", "验证码：1234567890", "输入框最多显示6位字符", "P1"],
    ["TC_023", "获取验证码按钮倒计时", "1.进入登录页面\n2.已输入手机号", "1.点击获取验证码按钮", "手机号：13800138000", "按钮显示倒计时60秒，期间不可点击", "P1"],
    ["TC_024", "用户协议勾选状态", "1.进入登录页面", "1.点击用户协议勾选框", "无", "勾选状态正确切换，微信登录按钮状态相应变化", "P1"],
    ["TC_025", "用户协议链接跳转", "1.进入登录页面", "1.点击\"用户协议\"链接", "无", "跳转到用户协议页面", "P2"],
    ["TC_026", "隐私政策链接跳转", "1.进入登录页面", "1.点击\"隐私政策\"链接", "无", "跳转到隐私政策页面", "P2"],
    ["TC_027", "返回按钮功能", "1.从其他页面进入登录页面", "1.点击返回按钮", "无", "返回上一页面或首页", "P1"],
    ["TC_028", "登录加载状态", "1.网络正常\n2.已输入正确信息\n3.已同意用户协议", "1.点击登录按钮", "手机号：13800138000\n验证码：123456", "显示\"登录中...\"加载提示，按钮不可重复点击", "P1"],
    ["TC_029", "验证码发送加载状态", "1.网络正常\n2.已输入手机号", "1.点击获取验证码按钮", "手机号：13800138000", "显示发送中状态，按钮暂时不可点击", "P2"],
    ["TC_030", "登录成功后token存储", "1.登录成功", "1.检查本地存储", "无", "token和wxUser信息正确存储到本地", "P0"],
    ["TC_031", "登录失败后存储清理", "1.登录失败", "1.检查本地存储", "无", "不存储任何登录信息", "P1"],
    ["TC_032", "401错误后存储清理", "1.token过期或无效", "1.发起需要认证的请求", "无", "自动清除本地token和用户信息", "P1"],
    ["TC_033", "手机号最短长度", "1.进入登录页面", "1.输入10位数字\n2.点击获取验证码", "手机号：1380013800", "显示手机号格式错误提示", "P2"],
    ["TC_034", "手机号最长长度", "1.进入登录页面", "1.输入12位数字\n2.点击获取验证码", "手机号：138001380001", "只接受前11位，或显示格式错误", "P2"],
    ["TC_035", "验证码最短长度", "1.已获取验证码", "1.输入5位验证码\n2.点击登录", "验证码：12345", "显示验证码格式错误提示", "P2"],
    ["TC_036", "验证码最长长度", "1.已获取验证码", "1.输入7位验证码\n2.点击登录", "验证码：1234567", "只接受前6位，或显示格式错误", "P2"],
    ["TC_037", "特殊字符输入", "1.进入登录页面", "1.在手机号框输入特殊字符", "手机号：138-0013-8000", "过滤特殊字符或显示格式错误", "P2"],
    ["TC_038", "验证码发送响应时间", "1.网络正常", "1.输入手机号\n2.点击获取验证码\n3.记录响应时间", "手机号：13800138000", "响应时间 < 3秒", "P2"],
    ["TC_039", "登录响应时间", "1.网络正常\n2.已获取验证码", "1.输入正确信息\n2.点击登录\n3.记录响应时间", "手机号：13800138000\n验证码：123456", "响应时间 < 5秒", "P2"],
    ["TC_040", "微信登录响应时间", "1.网络正常\n2.微信授权正常", "1.点击微信登录\n2.完成授权\n3.记录响应时间", "无", "响应时间 < 5秒", "P2"],
    ["TC_041", "iOS设备登录", "1.iOS设备\n2.微信最新版本", "1.执行完整登录流程", "标准测试数据", "功能正常，界面适配良好", "P1"],
    ["TC_042", "Android设备登录", "1.Android设备\n2.微信最新版本", "1.执行完整登录流程", "标准测试数据", "功能正常，界面适配良好", "P1"],
    ["TC_043", "不同屏幕尺寸适配", "1.不同尺寸设备", "1.检查页面布局\n2.执行登录流程", "标准测试数据", "界面元素正常显示，功能正常", "P2"],
    ["TC_044", "敏感信息存储安全", "1.登录成功", "1.检查本地存储内容", "无", "密码等敏感信息不明文存储", "P0"],
    ["TC_045", "token有效期验证", "1.登录成功\n2.等待token过期", "1.使用过期token请求接口", "无", "返回401错误，自动清除本地信息", "P1"],
    ["TC_046", "重放攻击防护", "1.已获取验证码", "1.多次使用同一验证码登录", "验证码：123456", "第二次使用时提示验证码无效", "P1"],
    ["TC_047", "登录后页面跳转", "1.登录成功", "1.检查页面跳转", "无", "正确跳转到首页tab", "P0"],
    ["TC_048", "登录状态保持", "1.登录成功\n2.关闭小程序\n3.重新打开", "1.检查登录状态", "无", "保持登录状态，不需要重新登录", "P0"],
    ["TC_049", "退出登录功能", "1.已登录状态", "1.执行退出登录操作", "无", "清除本地存储，返回登录页面", "P1"]
]

# 创建 DataFrame 并导出 Excel
df = pd.DataFrame(data, columns=["测试用例ID", "测试场景", "前置条件", "测试步骤", "输入数据", "预期结果", "优先级"])
df.to_excel("小程序登录测试用例.xlsx", index=False)
print("✅ Excel 文件已生成：小程序登录测试用例.xlsx")