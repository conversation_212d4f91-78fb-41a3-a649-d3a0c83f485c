import { getStorageSync } from './utils'
import RequestManager from '@/utils/requestManager.js'
import { URL } from '@/config/index.js'

// 获取小程序环境信息
const accountInfo = wx.getAccountInfoSync()
const envVersion = accountInfo.miniProgram.envVersion

// 请求管理实例
const manager = new RequestManager()

/**
 * 设置请求头
 * @param {string} type - 请求类型（json/form）
 * @returns {Object} 请求头
 */
const setHeaders = (type) => {
  const header = {
    Authorization: 'WxBearer ' + getStorageSync('token') || '',
  }
  if (type === 'form') {
    header['content-type'] = 'application/x-www-form-urlencoded'
  }
  return header
}

/**
 * 处理未授权（401）情况
 */
const handleUnauthorized = () => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('wxUser')
}

/**
 * 显示错误提示
 * @param {string} message - 错误信息
 */
const showErrorToast = (message) => {
  uni.showToast({
    title: message || '网络连接失败，请稍后重试',
    icon: 'none',
    duration: 2000,
  })
}

/**
 * 基础请求方法
 * @param {string} url - 请求地址
 * @param {string} method - 请求方法
 * @param {Object} data - 请求数据
 * @param {string} type - 请求类型（json/form）
 * @param {boolean} loading - 是否显示加载状态
 * @returns {Promise} 请求结果
 */
const baseRequest = async (url, method, data = {}, type = 'json', loading = true) => {
  // 生成请求 ID，防止重复请求
  const requestId = manager.generateId(method, url, data)
  if (!requestId) {
    showErrorToast('请勿重复请求')
    return Promise.reject('重复请求')
  }

  try {
    // 发起请求
    const result = await new Promise((resolve, reject) => {
      uni.request({
        url: URL + url,
        method: method || 'get',
        header: setHeaders(type),
        timeout: method === 'get' ? 10000 : 30000, 
        data: data,
        success: (res) => {
          const response = res.data
          if (response.code === 200) {
            resolve(response)
          } else if (response.code === 401) {
            handleUnauthorized()
            reject(response)
          } else {
            showErrorToast(response.msg)
            reject(response)
          }
        },
        fail: (err) => {
          showErrorToast(err.errMsg)
          reject(err)
        },
        complete: () => {
          manager.deleteById(requestId)
        },
      })
    })
    return result
  } catch (error) {
    throw error
  }
}

/**
 * 请求方法封装
 */
const request = {}
;['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {
    request[method] = (api, data, type, loading) => baseRequest(api, method, data, type, loading)
  })

export default request
