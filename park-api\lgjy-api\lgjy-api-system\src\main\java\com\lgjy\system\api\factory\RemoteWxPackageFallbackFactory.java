package com.lgjy.system.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.RemoteWxPackageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 会员套餐服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteWxPackageFallbackFactory implements FallbackFactory<RemoteWxPackageService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteWxPackageFallbackFactory.class);

    @Override
    public RemoteWxPackageService create(Throwable throwable) {
        log.error("会员套餐服务调用失败:{}", throwable.getMessage());
        return new RemoteWxPackageService() {
            @Override
            public R<JSONObject> refundPackageOrder(String tradeId, BigDecimal refundAmount, String refundReason, String source) {
                return R.fail("会员套餐退款失败:" + throwable.getMessage());
            }
        };
    }
}
