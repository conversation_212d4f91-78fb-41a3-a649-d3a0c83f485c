/**
 * 车牌号工具函数
 * 统一处理车牌号的显示样式和类型判断
 */

/**
 * 判断是否为新能源车牌
 * @param {string} plateNo 车牌号
 * @returns {boolean} 是否为新能源车牌
 */
export function isNewEnergyPlate(plateNo) {
  if (!plateNo) return false;
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8;
}

/**
 * 获取车牌号标签类型（Element Plus Tag组件使用）
 * @param {string} plateNo 车牌号
 * @returns {string} 标签类型
 */
export function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

/**
 * 获取车牌号CSS类名（用于统一样式）
 * @param {string} plateNo 车牌号
 * @returns {string} CSS类名
 */
export function getPlateNoClass(plateNo) {
  if (!plateNo) return 'plate-tag';
  // 返回统一的CSS类名
  const baseClass = 'plate-tag';
  const typeClass = plateNo.length === 8 ? 'plate-new-energy' : 'plate-normal';
  return `${baseClass} ${typeClass}`;
}

/**
 * 获取车牌号颜色（兼容旧版本，建议使用CSS类名）
 * @param {string} plateNo 车牌号
 * @returns {string} 颜色值
 * @deprecated 建议使用getPlateNoClass()和CSS类名
 */
export function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#e8f5e8' : '#e3f2fd';
}

/**
 * 获取车牌类型数值
 * @param {string} plateNo 车牌号
 * @returns {number} 车牌类型 1-普通车牌 2-新能源车牌
 */
export function getPlateType(plateNo) {
  if (!plateNo) return 1;
  // 8位为新能源车牌(2)，7位为普通车牌(1)
  return plateNo.length === 8 ? 2 : 1;
}

/**
 * 获取车牌类型文字描述
 * @param {string} plateNo 车牌号
 * @returns {string} 车牌类型描述
 */
export function getPlateTypeText(plateNo) {
  if (!plateNo) return '未知';
  return plateNo.length === 8 ? '新能源车牌' : '普通车牌';
}

/**
 * 验证车牌号格式
 * @param {string} plateNo 车牌号
 * @returns {boolean} 是否为有效车牌号
 */
export function validatePlateNo(plateNo) {
  if (!plateNo) return false;
  
  // 普通车牌：7位，格式如：京A12345
  const normalPlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{5}$/;
  
  // 新能源车牌：8位，格式如：京AD12345 或 京A12345D
  const newEnergyPlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z]([A-HJ-NP-Z0-9]{5}[DF]|[DF][A-HJ-NP-Z0-9]{5})$/;
  
  if (plateNo.length === 7) {
    return normalPlateRegex.test(plateNo);
  } else if (plateNo.length === 8) {
    return newEnergyPlateRegex.test(plateNo);
  }
  
  return false;
}

/**
 * 格式化车牌号显示（添加空格分隔）
 * @param {string} plateNo 车牌号
 * @returns {string} 格式化后的车牌号
 */
export function formatPlateNo(plateNo) {
  if (!plateNo) return '';
  
  if (plateNo.length === 7) {
    // 普通车牌：京A 12345
    return `${plateNo.substr(0, 2)} ${plateNo.substr(2)}`;
  } else if (plateNo.length === 8) {
    // 新能源车牌：京AD 12345 或 京A 12345D
    if (plateNo.charAt(2) === 'D' || plateNo.charAt(2) === 'F') {
      return `${plateNo.substr(0, 3)} ${plateNo.substr(3)}`;
    } else {
      return `${plateNo.substr(0, 2)} ${plateNo.substr(2)}`;
    }
  }
  
  return plateNo;
}
