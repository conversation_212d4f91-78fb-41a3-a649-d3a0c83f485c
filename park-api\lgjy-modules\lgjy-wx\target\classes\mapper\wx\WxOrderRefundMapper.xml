<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxOrderRefundMapper">
    
    <resultMap type="WxOrderRefund" id="WxOrderRefundResult">
        <result property="id"    column="id"    />
        <result property="tradeId"    column="trade_id"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="refundType"    column="refund_type"    />
        <result property="originalAmount"    column="original_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
        
    <insert id="insertWxOrderRefund" parameterType="WxOrderRefund" useGeneratedKeys="true" keyProperty="id">
        insert into mini_order_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tradeId != null and tradeId != ''">trade_id,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="originalAmount != null">original_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tradeId != null and tradeId != ''">#{tradeId},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="originalAmount != null">#{originalAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

</mapper>
