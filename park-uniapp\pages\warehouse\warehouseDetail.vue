<template>
    <view class="warehouse-detail">
        <!-- 顶部轮播图 -->
        <view class="carousel-container">
            <swiper class="carousel" :indicator-dots="carouselImages.length > 1" :autoplay="true" :interval="3000" :duration="500">
                <swiper-item v-for="(image, index) in carouselImages" :key="index">
                    <image :src="image" mode="aspectFill" class="carousel-image" />
                </swiper-item>
            </swiper>
        </view>

        <!-- 场库基本信息 -->
        <view class="info-section">
            <view class="warehouse-header">
                <text class="warehouse-name">{{ warehouseDetail.warehouseName || warehouseDetail.projectName }}</text>
                <view class="status-tag" :class="{ active: warehouseDetail.status === 1 }">
                    {{ warehouseDetail.status === 1 ? '营业中' : '暂停营业' }}
                </view>
            </view>
            
            <view class="address-row" @tap="openMap">
                <image src="/static/icon/address.png" class="icon" />
                <text class="address-text">{{ warehouseDetail.address }}</text>
                <text class="nav-text">导航</text>
            </view>
            
            <view class="contact-row" v-if="warehouseDetail.managerPhone" @tap="makeCall">
                <image src="/static/icon/phone.png" class="icon" />
                <text class="contact-text">{{ warehouseDetail.managerPhone }}</text>
                <text class="call-text">拨打</text>
            </view>
        </view>
        <!-- 收费规则 -->
        <view class="info-section" v-if="warehouseDetail.remark">
            <view class="section-title">收费规则</view>
            <view class="remark-content">
                <text class="remark-text">{{ warehouseDetail.remark }}</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getWarehouseDetail } from "@/api/warehouse";

const id = ref(0);
const warehouseDetail = ref({});

// 处理轮播图数据
const carouselImages = computed(() => {
    if (!warehouseDetail.value.carouselImages) return [];
    try {
        const images = JSON.parse(warehouseDetail.value.carouselImages);
        return Array.isArray(images) ? images : [images];
    } catch (e) {
        return [];
    }
});

onLoad((options) => {
    console.log(options);
    id.value = options.id;
    fetchWarehouseDetail();
})

const fetchWarehouseDetail = async () => {
    const res = await getWarehouseDetail(id.value);
    warehouseDetail.value = res.data;
    console.log(warehouseDetail.value);
}

// 打开地图导航
const openMap = () => {
    const { latitude, longitude, warehouseName, address } = warehouseDetail.value;
    uni.openLocation({
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        name: warehouseName,
        address: address,
        success: () => {
            console.log('打开地图成功');
        },
        fail: (err) => {
            console.log('打开地图失败', err);
            uni.showToast({
                title: '无法打开地图',
                icon: 'none'
            });
        }
    });
};

// 拨打电话
const makeCall = () => {
    uni.makePhoneCall({
        phoneNumber: warehouseDetail.value.managerPhone,
        success: () => {
            console.log('拨打电话成功');
        },
        fail: (err) => {
            console.log('拨打电话失败', err);
        }
    });
};


</script>

<style lang="scss" scoped>
.warehouse-detail {
    background-color: #f5f5f5;
    min-height: 100vh;
}

.carousel-container {
    width: 100%;
    height: 400rpx;
    
    .carousel {
        width: 100%;
        height: 100%;
        
        .carousel-image {
            width: 100%;
            height: 100%;
        }
    }
}

.info-section {
    background: white;
    margin: 20rpx 0;
    padding: 30rpx;
    
    &:first-child {
        margin-top: 0;
    }
}

.warehouse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .warehouse-name {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        flex: 1;
    }
    
    .status-tag {
        padding: 8rpx 20rpx;
        background: #f0f0f0;
        color: #999;
        border-radius: 20rpx;
        font-size: 24rpx;
        
        &.active {
            background: #e8f5e8;
            color: #52c41a;
        }
    }
}

.address-row, .contact-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
    }
    
    .address-text, .contact-text {
        flex: 1;
        color: #333;
        font-size: 28rpx;
        line-height: 1.4;
    }
    
    .nav-text, .call-text {
        color: #1890ff;
        font-size: 28rpx;
    }
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}



.remark-content {
    .remark-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
    }
}


</style>