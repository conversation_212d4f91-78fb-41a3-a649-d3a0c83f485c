<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序登录测试用例管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #2193b0, #6dd5ed);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            justify-content: space-between;
        }

        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #2193b0, #6dd5ed);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .file-input {
            display: none;
        }

        .file-label {
            padding: 12px 24px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 8px 12px;
            background: white;
            border-radius: 20px;
            border: 2px solid #e9ecef;
        }

        .stat-passed {
            border-color: #28a745;
            color: #28a745;
        }

        .stat-failed {
            border-color: #dc3545;
            color: #dc3545;
        }

        .stat-tested {
            border-color: #17a2b8;
            color: #17a2b8;
        }

        .stat-not-tested {
            border-color: #6c757d;
            color: #6c757d;
        }

        .table-container {
            overflow-x: auto;
            padding: 0 30px 30px;
        }

        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .test-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .test-table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
            font-size: 13px;
            line-height: 1.4;
        }

        .test-table tr:hover {
            background: #f8f9fa;
        }

        .test-table tr.passed {
            background: rgba(40, 167, 69, 0.1);
        }

        .test-id {
            font-weight: bold;
            color: #495057;
            min-width: 80px;
        }

        .test-scenario {
            font-weight: 500;
            color: #212529;
            min-width: 200px;
        }

        .test-content {
            max-width: 250px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .priority {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            min-width: 30px;
        }

        .priority.P0 {
            background: #dc3545;
            color: white;
        }

        .priority.P1 {
            background: #fd7e14;
            color: white;
        }

        .priority.P2 {
            background: #6c757d;
            color: white;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #28a745;
        }

        .checkbox-cell {
            text-align: center;
            min-width: 60px;
        }

        .test-status-cell {
            text-align: center;
            min-width: 120px;
        }

        .status-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
            min-width: 50px;
            transition: all 0.2s ease;
        }

        .status-btn.success {
            background: #28a745;
            color: white;
        }

        .status-btn.fail {
            background: #dc3545;
            color: white;
        }

        .status-btn.not-tested {
            background: #6c757d;
            color: white;
        }

        .status-btn:hover {
            opacity: 0.8;
            transform: scale(1.05);
        }

        .status-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .filter-section {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .search-input {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            min-width: 200px;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close:hover {
            color: #000;
        }

        .detail-item {
            margin-bottom: 15px;
        }

        .detail-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .detail-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            white-space: pre-wrap;
            border-left: 4px solid #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        .no-data {
            text-align: center;
            padding: 60px;
            color: #999;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .control-group {
                justify-content: center;
            }

            .stats {
                justify-content: center;
                flex-wrap: wrap;
            }

            .filter-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
                width: 100%;
            }

            .test-table {
                font-size: 12px;
            }

            .test-table th,
            .test-table td {
                padding: 8px 6px;
            }

            .test-content {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 小程序登录测试用例管理系统</h1>
            <p>高效管理测试用例，跟踪测试进度，确保产品质量</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <input type="file" id="jsonFile" class="file-input" accept=".json">
                <label for="jsonFile" class="file-label">
                    📁 导入JSON文件
                </label>
                <button class="btn btn-success" onclick="showExportModal('tested')">
                    📋 导出已测试
                </button>
                <button class="btn btn-info" onclick="showExportModal('not-tested')">
                    ⏳ 导出未测试
                </button>
                <button class="btn btn-warning" onclick="showExportModal('all')">
                    📊 导出全部
                </button>
            </div>

            <div class="stats">
                <div class="stat-item stat-total">
                    <span>总计: </span>
                    <span id="totalCount">0</span>
                </div>
                <div class="stat-item stat-tested">
                    <span>🧪 已测试: </span>
                    <span id="testedCount">0</span>
                </div>
                <div class="stat-item stat-not-tested">
                    <span>⏳ 未测试: </span>
                    <span id="notTestedCount">0</span>
                </div>
                <div class="stat-item stat-passed">
                    <span>✅ 成功: </span>
                    <span id="passedCount">0</span>
                </div>
                <div class="stat-item stat-failed">
                    <span>❌ 失败: </span>
                    <span id="failedCount">0</span>
                </div>
            </div>
        </div>

        <div class="table-container">
            <div class="filter-section">
                <div class="filter-group">
                    <label>筛选状态:</label>
                    <select class="filter-select" id="statusFilter" onchange="filterTests()">
                        <option value="all">全部</option>
                        <option value="tested">已测试</option>
                        <option value="not-tested">未测试</option>
                        <option value="passed">测试成功</option>
                        <option value="failed">测试失败</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>优先级:</label>
                    <select class="filter-select" id="priorityFilter" onchange="filterTests()">
                        <option value="all">全部</option>
                        <option value="P0">P0</option>
                        <option value="P1">P1</option>
                        <option value="P2">P2</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>搜索:</label>
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索测试用例..." oninput="filterTests()">
                </div>
            </div>

            <div id="tableContainer">
                <div class="loading">正在加载测试用例...</div>
            </div>
        </div>
    </div>

    <!-- 导出模态框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>导出测试用例</h2>
                <span class="close" onclick="closeExportModal()">&times;</span>
            </div>
            <div id="exportModalBody">
                <div class="detail-item">
                    <div class="detail-label">文件名称</div>
                    <input type="text" id="exportFileName" class="search-input" placeholder="请输入文件名称" style="width: 100%; margin-top: 5px;">
                </div>
                <div class="detail-item">
                    <div class="detail-label">导出范围</div>
                    <div class="detail-content" id="exportRange"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">用例数量</div>
                    <div class="detail-content" id="exportCount"></div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="confirmExport()" style="margin-right: 10px;">
                        📄 确认导出
                    </button>
                    <button class="btn" onclick="closeExportModal()" style="background: #6c757d; color: white;">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>测试用例详情</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalBody"></div>
        </div>
    </div>

    <script>
        let testCases = [];
        let filteredTestCases = [];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadDefaultData();
            setupFileInput();
        });

        // 设置文件输入监听
        function setupFileInput() {
            document.getElementById('jsonFile').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            loadTestCases(data);
                            showNotification('✅ JSON文件导入成功！', 'success');
                        } catch (error) {
                            showNotification('❌ JSON文件格式错误！', 'error');
                            console.error('JSON解析错误:', error);
                        }
                    };
                    reader.readAsText(file);
                }
            });
        }

        // 加载默认数据
        function loadDefaultData() {
            // 空的默认数据
            const defaultData = [];
            
            // 检查是否有本地存储的数据
            const savedData = localStorage.getItem('testCases');
            if (savedData) {
                try {
                    loadTestCases(JSON.parse(savedData));
                } catch (error) {
                    loadTestCases(defaultData);
                }
            } else {
                loadTestCases(defaultData);
            }
        }

        // 加载测试用例
        function loadTestCases(data) {
            // 确保每个测试用例都有必要的属性
            testCases = data.map(testCase => ({
                ...testCase,
                tested: testCase.tested !== undefined ? testCase.tested : false,
                testResult: testCase.testResult !== undefined ? testCase.testResult : null // null: 未测试, true: 成功, false: 失败
            }));
            filteredTestCases = [...testCases];
            renderTable();
            updateStats();
            saveToLocalStorage();
        }

        // 渲染表格
        function renderTable() {
            const container = document.getElementById('tableContainer');
            
            if (filteredTestCases.length === 0) {
                container.innerHTML = '<div class="no-data">暂无测试用例数据</div>';
                return;
            }

            const table = `
                <table class="test-table">
                    <thead>
                        <tr>
                            <th>用例ID</th>
                            <th>测试场景</th>
                            <th>前置条件</th>
                            <th>测试步骤</th>
                            <th>输入数据</th>
                            <th>预期结果</th>
                            <th>优先级</th>
                            <th>是否测试</th>
                            <th>测试结果</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredTestCases.map(testCase => `
                            <tr class="${testCase.tested && testCase.testResult ? 'passed' : ''}">
                                <td class="test-id">${testCase.id}</td>
                                <td class="test-scenario">${testCase.scenario}</td>
                                <td class="test-content">${truncateText(testCase.preconditions, 100)}</td>
                                <td class="test-content">${truncateText(testCase.steps, 100)}</td>
                                <td class="test-content">${truncateText(testCase.inputData, 80)}</td>
                                <td class="test-content">${truncateText(testCase.expectedResult, 100)}</td>
                                <td><span class="priority ${testCase.priority}">${testCase.priority}</span></td>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="checkbox" 
                                           ${testCase.tested ? 'checked' : ''} 
                                           onchange="toggleTested('${testCase.id}')">
                                </td>
                                <td class="test-status-cell">
                                    <button class="status-btn ${getStatusClass(testCase)}" 
                                            ${!testCase.tested ? 'disabled' : ''}
                                            onclick="toggleTestResult('${testCase.id}')">
                                        ${getStatusText(testCase)}
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;" 
                                            onclick="showDetail('${testCase.id}')">
                                        查看详情
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        // 截断文本
        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // 获取状态样式类
        function getStatusClass(testCase) {
            if (!testCase.tested) return 'not-tested';
            if (testCase.testResult === true) return 'success';
            if (testCase.testResult === false) return 'fail';
            return 'not-tested';
        }

        // 获取状态文本
        function getStatusText(testCase) {
            if (!testCase.tested) return '未测试';
            if (testCase.testResult === true) return '成功';
            if (testCase.testResult === false) return '失败';
            return '未测试';
        }

        // 切换是否测试状态
        function toggleTested(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (testCase) {
                testCase.tested = !testCase.tested;
                if (!testCase.tested) {
                    testCase.testResult = null; // 如果取消测试，清除测试结果
                }
                updateStats();
                renderTable();
                saveToLocalStorage();
            }
        }

        // 切换测试结果
        function toggleTestResult(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (testCase && testCase.tested) {
                // 循环切换：null -> true -> false -> true -> ...
                if (testCase.testResult === null) {
                    testCase.testResult = true; // 成功
                } else if (testCase.testResult === true) {
                    testCase.testResult = false; // 失败
                } else {
                    testCase.testResult = true; // 回到成功
                }
                updateStats();
                renderTable();
                saveToLocalStorage();
            }
        }

        // 更新统计
        function updateStats() {
            const total = testCases.length;
            const tested = testCases.filter(tc => tc.tested).length;
            const notTested = total - tested;
            const passed = testCases.filter(tc => tc.tested && tc.testResult === true).length;
            const failed = testCases.filter(tc => tc.tested && tc.testResult === false).length;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('testedCount').textContent = tested;
            document.getElementById('notTestedCount').textContent = notTested;
            document.getElementById('passedCount').textContent = passed;
            document.getElementById('failedCount').textContent = failed;
        }

        // 筛选测试用例
        function filterTests() {
            const statusFilter = document.getElementById('statusFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredTestCases = testCases.filter(testCase => {
                // 状态筛选
                let statusMatch = false;
                if (statusFilter === 'all') {
                    statusMatch = true;
                } else if (statusFilter === 'tested') {
                    statusMatch = testCase.tested;
                } else if (statusFilter === 'not-tested') {
                    statusMatch = !testCase.tested;
                } else if (statusFilter === 'passed') {
                    statusMatch = testCase.tested && testCase.testResult === true;
                } else if (statusFilter === 'failed') {
                    statusMatch = testCase.tested && testCase.testResult === false;
                }

                // 优先级筛选
                const priorityMatch = priorityFilter === 'all' || testCase.priority === priorityFilter;

                // 搜索筛选
                const searchMatch = searchTerm === '' || 
                    testCase.id.toLowerCase().includes(searchTerm) ||
                    testCase.scenario.toLowerCase().includes(searchTerm) ||
                    testCase.preconditions.toLowerCase().includes(searchTerm) ||
                    testCase.steps.toLowerCase().includes(searchTerm) ||
                    testCase.inputData.toLowerCase().includes(searchTerm) ||
                    testCase.expectedResult.toLowerCase().includes(searchTerm);

                return statusMatch && priorityMatch && searchMatch;
            });

            renderTable();
        }

        // 显示详情
        function showDetail(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (!testCase) return;

            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="detail-item">
                    <div class="detail-label">测试用例ID</div>
                    <div class="detail-content">${testCase.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试场景</div>
                    <div class="detail-content">${testCase.scenario}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">前置条件</div>
                    <div class="detail-content">${testCase.preconditions}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试步骤</div>
                    <div class="detail-content">${testCase.steps}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">输入数据</div>
                    <div class="detail-content">${testCase.inputData}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">预期结果</div>
                    <div class="detail-content">${testCase.expectedResult}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">优先级</div>
                    <div class="detail-content"><span class="priority ${testCase.priority}">${testCase.priority}</span></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">是否测试</div>
                    <div class="detail-content">${testCase.tested ? '✅ 已测试' : '⏳ 未测试'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试结果</div>
                    <div class="detail-content">${getTestResultText(testCase)}</div>
                </div>
            `;

            document.getElementById('detailModal').style.display = 'block';
        }

        // 获取测试结果文本
        function getTestResultText(testCase) {
            if (!testCase.tested) return '⏳ 未测试';
            if (testCase.testResult === true) return '✅ 测试成功';
            if (testCase.testResult === false) return '❌ 测试失败';
            return '⏳ 未测试';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('detailModal').style.display = 'none';
        }

        // 显示导出模态框
        function showExportModal(type) {
            let data, rangeText, fileName;
            
            switch(type) {
                case 'tested':
                    data = testCases.filter(tc => tc.tested);
                    rangeText = '已测试的用例';
                    fileName = '已测试用例';
                    break;
                case 'not-tested':
                    data = testCases.filter(tc => !tc.tested);
                    rangeText = '未测试的用例';
                    fileName = '未测试用例';
                    break;
                case 'all':
                    data = testCases;
                    rangeText = '全部用例';
                    fileName = '全部测试用例';
                    break;
                default:
                    return;
            }

            document.getElementById('exportRange').textContent = rangeText;
            document.getElementById('exportCount').textContent = `${data.length} 个测试用例`;
            document.getElementById('exportFileName').value = fileName;
            
            // 存储当前导出数据到全局变量
            window.currentExportData = data;
            
            document.getElementById('exportModal').style.display = 'block';
        }

        // 关闭导出模态框
        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        // 确认导出
        function confirmExport() {
            const fileName = document.getElementById('exportFileName').value.trim();
            if (!fileName) {
                showNotification('❌ 请输入文件名称！', 'error');
                return;
            }
            
            if (window.currentExportData) {
                exportToJson(window.currentExportData, fileName);
                closeExportModal();
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const detailModal = document.getElementById('detailModal');
            const exportModal = document.getElementById('exportModal');
            if (event.target === detailModal) {
                detailModal.style.display = 'none';
            }
            if (event.target === exportModal) {
                exportModal.style.display = 'none';
            }
        }

        // 导出功能 - 移除旧的导出函数，只保留通用的exportToJson
        function exportToJson(data, filename) {
            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showNotification(`✅ ${filename}导出成功！`, 'success');
        }

        // 保存到本地存储
        function saveToLocalStorage() {
            localStorage.setItem('testCases', JSON.stringify(testCases));
        }

        // 显示通知
        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 9999;
                animation: slideIn 0.3s ease;
                ${type === 'success' ? 'background: linear-gradient(45deg, #56ab2f, #a8e6cf);' : 'background: linear-gradient(45deg, #ff416c, #ff4b2b);'}
            `;
            notification.textContent = message;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>