# 小程序用户协议和隐私协议数据不匹配问题修复说明

## 问题描述

小程序端的用户协议和隐私协议调用的数据库中的数据不匹配，导致：
- 点击"用户协议"显示的是隐私协议内容
- 点击"隐私协议"显示的是发票抬头协议内容

## 问题原因分析

### 数据库中的实际数据
根据 `park-api/sql/sys_agreement.sql` 文件，数据库中的协议类型定义为：
- `agreement_type = 0`: 用户服务条款
- `agreement_type = 1`: 隐私协议  
- `agreement_type = 2`: 发票抬头协议

### 小程序代码中的调用
小程序代码中的调用参数：
- 用户协议页面：`getAgreementByType(1)` - 期望获取用户协议，但实际获取到隐私协议
- 隐私协议页面：`getAgreementByType(2)` - 期望获取隐私协议，但实际获取到发票抬头协议

### 代码注释中的定义
在多个文件的注释中，协议类型被错误地定义为：
- `1=用户服务协议 2=隐私协议 3=发票抬头协议`

## 修复方案

选择修改小程序代码而不是数据库，因为：
1. 数据库可能被其他系统依赖
2. 修改代码更安全，不会影响数据完整性
3. 保持与数据库实际结构的一致性

## 修复内容

### 1. 修改小程序调用参数

**文件：`park-uniapp/pages/aggrement/user-aggrement.vue`**
```javascript
// 修改前
const response = await getAgreementByType(1)

// 修改后  
const response = await getAgreementByType(0)
```

**文件：`park-uniapp/pages/aggrement/privacy-aggrement.vue`**
```javascript
// 修改前
const response = await getAgreementByType(2)

// 修改后
const response = await getAgreementByType(1)
```

### 2. 更新API文档注释

**文件：`park-uniapp/api/agreement.js`**
```javascript
// 修改前
* @param {number} agreementType 协议类型 1-用户服务协议 2-隐私协议 3-发票抬头协议

// 修改后
* @param {number} agreementType 协议类型 0-用户服务协议 1-隐私协议 2-发票抬头协议
```

### 3. 更新后端控制器注释和校验

**文件：`park-api/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxAgreementController.java`**
```java
// 修改注释
* @param agreementType 协议类型 0-用户服务协议 1-隐私协议 2-发票抬头协议

// 修改参数校验
if (agreementType < 0 || agreementType > 2) {
    return R.fail("协议类型参数错误");
}
```

### 4. 更新实体类注释

**文件：`park-api/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/domain/WxAgreement.java`**
```java
// 修改注释
/** 协议类型（0=用户服务条款 1=隐私政策 2=发票抬头协议） */

// 修改Excel注解
@Excel(name = "协议类型", readConverterExp = "0=用户服务条款,1=隐私政策,2=发票抬头协议")
```

## 验证方法

修复完成后，可以通过以下方式验证：

1. **前端测试**：
   - 在小程序中点击"用户协议"，应该显示用户服务协议内容
   - 在小程序中点击"隐私协议"，应该显示隐私政策内容

2. **API测试**：
   - 调用 `/wx/agreement/getByType?agreementType=0` 应返回用户服务协议
   - 调用 `/wx/agreement/getByType?agreementType=1` 应返回隐私协议
   - 调用 `/wx/agreement/getByType?agreementType=2` 应返回发票抬头协议

## 注意事项

1. 此修复保持了与数据库实际结构的一致性
2. 所有相关的注释和文档都已同步更新
3. 参数校验逻辑已相应调整
4. 不会影响其他可能依赖当前数据库结构的系统

## 修复文件清单

1. `park-uniapp/pages/aggrement/user-aggrement.vue`
2. `park-uniapp/pages/aggrement/privacy-aggrement.vue`
3. `park-uniapp/api/agreement.js`
4. `park-api/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxAgreementController.java`
5. `park-api/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/domain/WxAgreement.java`
