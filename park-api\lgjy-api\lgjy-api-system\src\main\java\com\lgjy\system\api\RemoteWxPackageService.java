package com.lgjy.system.api;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.constant.ServiceNameConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.factory.RemoteWxPackageFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 小程序会员套餐服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteWxPackageService", value = ServiceNameConstants.WX_SERVICE, fallbackFactory = RemoteWxPackageFallbackFactory.class)
public interface RemoteWxPackageService {
    
    /**
     * 会员套餐订单退款
     *
     * @param tradeId 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/package/order/refund")
    R<JSONObject> refundPackageOrder(@RequestParam("tradeId") String tradeId,
                                     @RequestParam("refundAmount") BigDecimal refundAmount,
                                     @RequestParam("refundReason") String refundReason,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
