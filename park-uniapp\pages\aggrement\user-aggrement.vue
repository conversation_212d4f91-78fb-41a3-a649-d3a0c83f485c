<template>
  <view class="agreement-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="navbar-title">
          <text class="title-text">协议详情</text>
        </view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <view v-if="loading" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="error" class="error-container">
        <text class="error-text">{{ error }}</text>
        <button class="retry-btn" @click="loadAgreement">重新加载</button>
      </view>
      
      <view v-else class="agreement-content">
        <view class="agreement-body">
          <rich-text :nodes="agreementData.agreementContent || '暂无内容'"></rich-text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAgreementByType } from '@/api/agreement'

const loading = ref(true)
const error = ref('')
const agreementData = ref({})

// 加载用户协议
const loadAgreement = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const response = await getAgreementByType(0)
    
    if (response.code === 200) {
      agreementData.value = response.data || {}
    } else {
      error.value = response.msg || '获取协议失败'
    }
  } catch (err) {
    console.error('加载用户协议失败:', err)
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onLoad(() => {
  loadAgreement()
})
</script>

<style lang="scss" scoped>
.agreement-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  padding-top: var(--status-bar-height);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
}

.navbar-left {
  width: 80rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: 300;
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  padding-bottom: 16rpx; /* 增加标题下方边距 */
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 80rpx;
}

.content-wrapper {
  padding: 32rpx;
  margin-top: calc(var(--status-bar-height) + 88rpx);
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.error-text {
  font-size: 28rpx;
  color: #f56c6c;
  margin-bottom: 32rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.agreement-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.agreement-body {
  line-height: 1.8;
  color: #666;
  font-size: 28rpx;
}

/* rich-text 内容样式 */
.agreement-body :deep(p) {
  margin: 16rpx 0;
  line-height: 1.8;
}

.agreement-body :deep(strong) {
  font-weight: 600;
  color: #333;
}

.agreement-body :deep(h1),
.agreement-body :deep(h2),
.agreement-body :deep(h3) {
  font-weight: 600;
  color: #333;
  margin: 32rpx 0 32rpx 0;
}

.agreement-body :deep(h1) {
  font-size: 36rpx;
}

.agreement-body :deep(h2) {
  font-size: 32rpx;
}

.agreement-body :deep(h3) {
  font-size: 30rpx;
}
</style>
