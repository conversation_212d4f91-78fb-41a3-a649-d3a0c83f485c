package com.lgjy.wx.task;

import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.WxUserPackageRecord;
import com.lgjy.wx.mapper.WxPackageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 订单超时处理定时任务
 * 每5分钟检查一次超过15分钟的待支付订单，将其状态更新为已取消
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Component
@Slf4j
public class OrderTimeoutTask {
    
    @Autowired
    private WxPackageMapper wxPackageMapper;
    
    /**
     * 每5分钟执行一次，处理超时订单
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void processTimeoutOrders() {
        try {
            log.info("开始执行订单超时处理任务");
            
            // 查询15分钟前创建且状态为待支付的订单
            Date fifteenMinutesAgo = new Date(System.currentTimeMillis() - 15 * 60 * 1000);
            List<WxUserPackageRecord> timeoutOrders = wxPackageMapper.selectTimeoutOrders(
                    fifteenMinutesAgo, 
                    PayConstants.ORDER_PAY_STATUS_PROGRESS
            );
            
            if (timeoutOrders.isEmpty()) {
                log.info("订单超时处理任务完成，没有发现超时订单");
                return;
            }
            
            log.info("发现{}个超时订单，开始处理", timeoutOrders.size());
            
            int processedCount = 0;
            for (WxUserPackageRecord order : timeoutOrders) {
                try {
                    // 更新订单状态为已取消
                    order.setPayStatus(PayConstants.ORDER_PAY_STATUS_CANCEL);
                    order.setUpdateTime(new Date());
                    int result = wxPackageMapper.updateWxUserPackageRecord(order);
                    
                    if (result > 0) {
                        processedCount++;
                        log.info("订单超时处理成功，tradeId: {}, 状态从待支付(1)更新为已取消(3)", 
                                order.getTradeId());
                    } else {
                        log.error("订单超时处理失败，数据库更新失败，tradeId: {}", order.getTradeId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理单个订单超时异常，tradeId: {}", order.getTradeId(), e);
                }
            }
            
            log.info("订单超时处理任务完成，共处理{}个订单", processedCount);
            
        } catch (Exception e) {
            log.error("订单超时处理任务执行失败", e);
        }
    }
}
