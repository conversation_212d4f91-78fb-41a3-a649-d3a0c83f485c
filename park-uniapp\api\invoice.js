import request from '../utils/request'

// 获取发票抬头列表
export const getInvoiceTitleList = () => request.get('/wx/invoiceTitle/list')

// 添加发票抬头
export const addInvoiceTitle = (data) => request.post('/wx/invoiceTitle/insert', data)

// 编辑发票抬头
export const editInvoiceTitle = (data) => request.put('/wx/invoiceTitle/update', data)

// 删除发票抬头
export const deleteInvoiceTitle = (data) => request.delete('/wx/invoiceTitle/delete', data)

// 获取发票抬头详情
export const getInvoiceTitleDetail = (data) => request.get('/wx/invoiceTitle/detail', data)

// 发送发票到邮箱
export const postInvoiceSend = (data) => request.post('/wx//invoice/record/send', data)

// 保存开票记录
export const postSaveInvoiceRecord = (data) => request.post('/wx/invoice/record/insert', data)

// 发票重开
export const postResumeInvoice = (data) => request.post('/wx/invoice/resume', data)

// 获取开票记录列表
export const getInvoiceRecordList = () => request.post('/wx/invoice/record/list')
