// 星空主题样式文件
// ===========================

// 星空动画关键帧定义
@keyframes starry-twinkle {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.2); 
  }
}

@keyframes shooting-star {
  0% {
    transform: translateX(-100px) translateY(100px);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% {
    transform: translateX(100vw) translateY(-100px);
    opacity: 0;
  }
}

@keyframes shooting-star-2 {
  0% {
    transform: translateX(-50px) translateY(50px);
    opacity: 0;
  }
  15% { opacity: 0.8; }
  85% { opacity: 0.8; }
  100% {
    transform: translateX(120vw) translateY(-80px);
    opacity: 0;
  }
}

@keyframes shooting-star-3 {
  0% {
    transform: translateX(-80px) translateY(150px);
    opacity: 0;
  }
  12% { opacity: 0.6; }
  88% { opacity: 0.6; }
  100% {
    transform: translateX(110vw) translateY(-120px);
    opacity: 0;
  }
}

@keyframes aurora-glow {
  0%, 100% { 
    opacity: 0.2; 
    transform: translateX(-50%) scaleX(1);
  }
  50% { 
    opacity: 0.6; 
    transform: translateX(-50%) scaleX(1.2);
  }
}

@keyframes starry-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(100, 200, 255, 0.3);
  }
  50% { 
    box-shadow: 0 0 20px rgba(100, 200, 255, 0.6), 0 0 30px rgba(100, 200, 255, 0.4);
  }
}

// 星空背景混合器
@mixin starry-background {
  background:
    radial-gradient(ellipse at top, #0a0a0a 0%, #1a1a2e 30%, #16213e 60%, #000000 100%),
    radial-gradient(ellipse at bottom, #000000 0%, #0f0f23 40%, #1a1a2e 80%, #000000 100%);
  background-attachment: fixed;
  position: relative;
  
  // 星星层
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent),
      radial-gradient(1px 1px at 200px 50px, rgba(255,255,255,0.7), transparent),
      radial-gradient(2px 2px at 250px 90px, #fff, transparent),
      radial-gradient(1px 1px at 300px 20px, rgba(255,255,255,0.5), transparent);
    background-repeat: repeat;
    background-size: 350px 200px;
    animation: starry-twinkle 4s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 1;
  }
  
  // 极光层
  &::after {
    content: '';
    position: fixed;
    top: 0;
    left: 50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(45deg, 
      rgba(64, 224, 208, 0.1) 0%, 
      transparent 25%, 
      transparent 50%,
      rgba(138, 43, 226, 0.1) 75%,
      transparent 100%);
    animation: aurora-glow 8s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 2;
    transform: translateX(-50%);
  }
}

// 流星效果混合器
@mixin shooting-star-effect {
  // 第一颗流星
  &::after {
    content: '';
    position: fixed;
    top: 10%;
    left: -5%;
    width: 3px;
    height: 3px;
    background: #f8f8ff; /* 月光白流星 */
    border-radius: 50%;
    box-shadow:
      0 0 6px #f8f8ff,
      0 0 12px #f8f8ff,
      0 0 18px #f8f8ff,
      -20px 0 8px rgba(248, 248, 255, 0.3),
      -40px 0 6px rgba(248, 248, 255, 0.2);
    animation: shooting-star 8s linear infinite;
    animation-delay: 2s;
    pointer-events: none;
    z-index: 3;
  }
}

// 多流星效果混合器
@mixin multiple-shooting-stars {
  // 第二颗流星
  &::before {
    content: '';
    position: fixed;
    top: 30%;
    left: -3%;
    width: 2px;
    height: 2px;
    background: #e6e6fa; /* 淡紫色流星 */
    border-radius: 50%;
    box-shadow:
      0 0 4px #e6e6fa,
      0 0 8px #e6e6fa,
      0 0 12px #e6e6fa,
      -15px 0 6px rgba(230, 230, 250, 0.3);
    animation: shooting-star-2 12s linear infinite;
    animation-delay: 6s;
    pointer-events: none;
    z-index: 3;
  }
}

// 第三颗流星（通过伪元素实现）
@mixin third-shooting-star {
  position: relative;

  &::after {
    content: '';
    position: fixed;
    top: 20%;
    left: -4%;
    width: 2px;
    height: 2px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow:
      0 0 5px #ffffff,
      0 0 10px #ffffff,
      0 0 15px #ffffff,
      -18px 0 7px rgba(255, 255, 255, 0.3);
    animation: shooting-star-3 15s linear infinite;
    animation-delay: 10s;
    pointer-events: none;
    z-index: 3;
  }
}

// 星空卡片样式混合器
@mixin starry-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  
  // 光晕效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(100, 200, 255, 0.1), 
      transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    border-color: rgba(100, 200, 255, 0.4) !important;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(100, 200, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    
    &::before {
      left: 100%;
    }
  }
}

// 星空按钮样式混合器
@mixin starry-button {
  background: linear-gradient(135deg, 
    rgba(100, 200, 255, 0.2) 0%, 
    rgba(138, 43, 226, 0.2) 100%) !important;
  border: 1px solid rgba(100, 200, 255, 0.3) !important;
  color: #e3f2fd !important;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, 
      rgba(255, 255, 255, 0.3) 0%, 
      transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }
  
  &:hover {
    background: linear-gradient(135deg, 
      rgba(100, 200, 255, 0.3) 0%, 
      rgba(138, 43, 226, 0.3) 100%) !important;
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 
      0 0 20px rgba(100, 200, 255, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    color: #ffffff !important;
    
    &::before {
      width: 100px;
      height: 100px;
    }
  }
  
  &:focus {
    background: linear-gradient(135deg, 
      rgba(100, 200, 255, 0.3) 0%, 
      rgba(138, 43, 226, 0.3) 100%) !important;
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 
      0 0 20px rgba(100, 200, 255, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
  }
}

// 星空文字样式混合器
@mixin starry-text($size: 'normal') {
  @if $size == 'large' {
    color: #e3f2fd !important;
    text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
    font-weight: 600;
  } @else if $size == 'small' {
    color: #b3e5fc !important;
    text-shadow: 0 0 5px rgba(100, 200, 255, 0.3);
  } @else {
    color: #e1f5fe !important;
    text-shadow: 0 0 8px rgba(100, 200, 255, 0.4);
  }
}

// 星空图标样式混合器
@mixin starry-icon($color: #64b5f6) {
  color: $color !important;
  filter: drop-shadow(0 0 8px rgba(red($color), green($color), blue($color), 0.6));
  transition: all 0.3s ease;
  
  &:hover {
    filter: drop-shadow(0 0 12px rgba(red($color), green($color), blue($color), 0.8));
    transform: scale(1.1);
  }
}

// 星空输入框样式混合器
@mixin starry-input {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  color: #e1f5fe !important;
  border-radius: 6px;
  
  &:focus {
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 0 0 10px rgba(100, 200, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.08) !important;
  }
  
  &::placeholder {
    color: #81d4fa !important;
  }
}

// 星空下拉菜单样式混合器
@mixin starry-dropdown {
  background: rgba(15, 20, 25, 0.95) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  border-radius: 8px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(100, 200, 255, 0.1);
}

// 星空弹窗样式混合器
@mixin starry-dialog {
  .el-dialog {
    background: rgba(15, 20, 25, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    border-radius: 16px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.5),
      0 0 40px rgba(100, 200, 255, 0.1);

    .el-dialog__header {
      background: linear-gradient(135deg,
        rgba(100, 200, 255, 0.1) 0%,
        rgba(138, 43, 226, 0.1) 100%) !important;
      border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
      border-radius: 16px 16px 0 0;

      .el-dialog__title {
        @include starry-text('large');
      }
    }

    .el-dialog__body {
      background: rgba(15, 20, 25, 0.8) !important;
      @include starry-text('normal');
    }

    .el-dialog__footer {
      background: rgba(15, 20, 25, 0.9) !important;
      border-top: 1px solid rgba(100, 200, 255, 0.2) !important;
      border-radius: 0 0 16px 16px;
    }
  }

  .el-overlay {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(5px);
  }
}

// 星空表格样式混合器
@mixin starry-table {
  .el-table {
    background: rgba(0, 0, 0, 0.8) !important; /* 深黑背景 */
    border: 1px solid rgba(248, 248, 255, 0.2) !important; /* 月光白边框 */
    border-radius: 12px;
    overflow: hidden;

    .el-table__header {
      background: linear-gradient(135deg,
        rgba(248, 248, 255, 0.1) 0%,
        rgba(221, 160, 221, 0.1) 100%) !important; /* 月光白到梅花色渐变 */

      th {
        background: transparent !important;
        border-bottom: 1px solid rgba(248, 248, 255, 0.2) !important;
        color: #f8f8ff !important; /* 月光白文字 */
        text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
      }
    }

    .el-table__body {
      tr {
        background: rgba(0, 0, 0, 0.6) !important; /* 深黑半透明背景 */

        &:hover {
          background: rgba(248, 248, 255, 0.1) !important; /* 悬停月光白背景 */
        }

        td {
          border-bottom: 1px solid rgba(248, 248, 255, 0.1) !important;
          color: #e6e6fa !important; /* 淡紫色文字 */
          text-shadow: 0 0 3px rgba(230, 230, 250, 0.3);
        }
      }
    }

    // 固定列样式 - 解决透明度问题
    .el-table__fixed,
    .el-table__fixed-right {
      background: rgba(15, 20, 25, 1) !important; /* 100%不透明度深色背景 */
      backdrop-filter: none !important; /* 移除模糊效果 */

      .el-table__fixed-header-wrapper,
      .el-table__fixed-body-wrapper {
        background: rgba(15, 20, 25, 1) !important; /* 100%不透明度 */
        backdrop-filter: none !important;
      }

      .el-table__header {
        background: rgba(15, 20, 25, 1) !important; /* 固定表头100%不透明 */

        th {
          background: rgba(15, 20, 25, 1) !important; /* 固定表头单元格100%不透明 */
          border-bottom: 1px solid rgba(248, 248, 255, 0.3) !important;
          backdrop-filter: none !important;
        }
      }

      .el-table__body {
        tr {
          background: rgba(15, 20, 25, 1) !important; /* 固定列行100%不透明 */

          &:hover {
            background: rgba(30, 40, 50, 1) !important; /* 悬停时深色不透明背景 */
          }

          td {
            background: rgba(15, 20, 25, 1) !important; /* 固定列单元格100%不透明 */
            border-bottom: 1px solid rgba(248, 248, 255, 0.2) !important;
            backdrop-filter: none !important;
          }
        }
      }
    }

    // 操作列特殊样式 - 增强不透明度
    .fixed-width {
      background: rgba(15, 20, 25, 1) !important; /* 操作列100%不透明背景 */
      backdrop-filter: none !important;

      .cell {
        background: rgba(15, 20, 25, 1) !important; /* 操作列单元格100%不透明 */
        backdrop-filter: none !important;
      }

      .el-button {
        @include starry-button;
        margin-right: 4px;
        padding: 4px 8px;
        font-size: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    // 小间距操作列样式增强
    .small-padding.fixed-width {
      background: rgba(15, 20, 25, 1) !important;
      backdrop-filter: none !important;

      .cell {
        background: rgba(15, 20, 25, 1) !important;
        backdrop-filter: none !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
      }
    }

    // 小间距样式
    .small-padding {
      .cell {
        padding-left: 8px !important;
        padding-right: 8px !important;
      }
    }
  }
}

// 星空侧边栏样式混合器
@mixin starry-sidebar {
  background: rgba(15, 20, 25, 0.9) !important;
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(100, 200, 255, 0.2) !important;

  .el-menu {
    background: transparent !important;
    border: none !important;

    .el-menu-item {
      background: transparent !important;
      @include starry-text('normal');
      transition: all 0.3s ease;

      &:hover {
        background: rgba(100, 200, 255, 0.1) !important;
        @include starry-text('large');
      }

      &.is-active {
        background: rgba(100, 200, 255, 0.2) !important;
        @include starry-text('large');
        border-right: 3px solid #64b5f6;
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        background: transparent !important;
        @include starry-text('normal');

        &:hover {
          background: rgba(100, 200, 255, 0.1) !important;
          @include starry-text('large');
        }
      }
    }
  }
}

// 星空导航栏样式混合器
@mixin starry-navbar {
  background: rgba(15, 20, 25, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;

  .navbar-content {
    @include starry-text('normal');
  }
}

// ===========================
// 星空主题全局应用
// ===========================

// 在html.dark下应用星空主题
html.dark {
  // 全局背景
  @include starry-background;
  @include shooting-star-effect;
  @include multiple-shooting-stars;
  min-height: 100vh;

  // 确保body也有星空背景
  body {
    @include starry-background;
    min-height: 100vh;
    position: relative;
  }

  // 应用程序容器
  #app {
    @include starry-background;
    @include third-shooting-star;
    min-height: 100vh;
    position: relative;
    z-index: 10;
  }

  // Element Plus 组件覆盖
  .el-card {
    @include starry-card;
  }

  .el-button {
    @include starry-button;
  }

  .el-input__wrapper {
    @include starry-input;
  }

  .el-select .el-input__wrapper {
    @include starry-input;
  }

  .el-textarea__inner {
    @include starry-input;
  }

  // 弹窗组件
  @include starry-dialog;

  // 表格组件
  @include starry-table;

  // 额外的表格固定列样式增强
  .el-table {
    // 确保固定列的阴影效果
    .el-table__fixed-right::before {
      background: linear-gradient(90deg,
        rgba(248, 248, 255, 0.1) 0%,
        transparent 100%) !important;
    }

    .el-table__fixed::after {
      background: linear-gradient(-90deg,
        rgba(248, 248, 255, 0.1) 0%,
        transparent 100%) !important;
    }

    // 固定列边框增强
    .el-table__fixed-right {
      border-left: 1px solid rgba(248, 248, 255, 0.2) !important;
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3) !important;
    }

    .el-table__fixed {
      border-right: 1px solid rgba(248, 248, 255, 0.2) !important;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3) !important;
    }
  }

  // 下拉菜单
  .el-dropdown-menu {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(248, 248, 255, 0.2) !important;
    border-radius: 8px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.5),
      0 0 40px rgba(248, 248, 255, 0.1);

    .el-dropdown-menu__item {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(248, 248, 255, 0.1) !important;
        color: #ffffff !important;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
      }
    }
  }

  // 选择器下拉面板
  .el-select-dropdown {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(248, 248, 255, 0.2) !important;
    border-radius: 8px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.5),
      0 0 40px rgba(248, 248, 255, 0.1);

    .el-select-dropdown__item {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(248, 248, 255, 0.1) !important;
        color: #ffffff !important;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
      }

      &.is-selected {
        background: rgba(248, 248, 255, 0.15) !important;
        color: #f8f8ff !important;
        text-shadow: 0 0 10px rgba(248, 248, 255, 0.8);
      }
    }
  }

  // 消息提示
  .el-message {
    background: rgba(15, 20, 25, 0.95) !important;
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    backdrop-filter: blur(10px);
    @include starry-text('normal');
  }

  .el-notification {
    background: rgba(15, 20, 25, 0.95) !important;
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    backdrop-filter: blur(10px);

    .el-notification__title {
      @include starry-text('large');
    }

    .el-notification__content {
      @include starry-text('normal');
    }
  }

  // 分页组件 - 深黑星空主题
  .el-pagination {
    background: transparent !important;

    // 总数和跳转文字
    .el-pagination__total,
    .el-pagination__jump {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
    }

    // 跳转输入框
    .el-pagination__jump .el-input__wrapper {
      background: rgba(0, 0, 0, 0.6) !important;
      border: 1px solid rgba(248, 248, 255, 0.3) !important;
      box-shadow: none !important;

      .el-input__inner {
        color: #f8f8ff !important; /* 月光白 */
        background: transparent !important;
        text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);

        &::placeholder {
          color: #e6e6fa !important; /* 淡紫色 */
        }
      }

      &:hover {
        border-color: rgba(248, 248, 255, 0.5) !important;
        box-shadow: 0 0 8px rgba(248, 248, 255, 0.2) !important;
      }

      &.is-focus {
        border-color: rgba(248, 248, 255, 0.6) !important;
        box-shadow: 0 0 12px rgba(248, 248, 255, 0.3) !important;
      }
    }

    // 每页条数选择器
    .el-pagination__sizes .el-select .el-input__wrapper {
      background: rgba(0, 0, 0, 0.6) !important;
      border: 1px solid rgba(248, 248, 255, 0.3) !important;
      box-shadow: none !important;

      .el-input__inner {
        color: #f8f8ff !important; /* 月光白 */
        background: transparent !important;
        text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
      }

      .el-input__suffix {
        .el-icon {
          color: #f8f8ff !important; /* 月光白 */
        }
      }

      &:hover {
        border-color: rgba(248, 248, 255, 0.5) !important;
        box-shadow: 0 0 8px rgba(248, 248, 255, 0.2) !important;
      }
    }

    // 页码按钮
    .el-pager li {
      background: rgba(0, 0, 0, 0.6) !important; /* 深黑背景 */
      border: 1px solid rgba(248, 248, 255, 0.3) !important; /* 月光白边框 */
      color: #f8f8ff !important; /* 月光白文字 */
      text-shadow: 0 0 5px rgba(248, 248, 255, 0.4);
      border-radius: 6px;
      margin: 0 2px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(248, 248, 255, 0.1) !important;
        border-color: rgba(248, 248, 255, 0.5) !important;
        color: #ffffff !important;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(248, 248, 255, 0.2);
      }

      &.is-active {
        background: rgba(248, 248, 255, 0.15) !important; /* 月光白激活背景 */
        border-color: rgba(248, 248, 255, 0.6) !important;
        color: #f8f8ff !important; /* 月光白文字 */
        text-shadow: 0 0 10px rgba(248, 248, 255, 0.8);
        box-shadow:
          0 0 15px rgba(248, 248, 255, 0.3),
          inset 0 1px 0 rgba(248, 248, 255, 0.2);
      }

      &.is-disabled {
        background: rgba(0, 0, 0, 0.3) !important;
        border-color: rgba(248, 248, 255, 0.1) !important;
        color: rgba(248, 248, 255, 0.3) !important;
        cursor: not-allowed;
      }
    }

    // 上一页/下一页按钮
    .btn-prev,
    .btn-next {
      background: rgba(0, 0, 0, 0.6) !important; /* 深黑背景 */
      border: 1px solid rgba(248, 248, 255, 0.3) !important; /* 月光白边框 */
      color: #f8f8ff !important; /* 月光白文字 */
      border-radius: 6px;
      transition: all 0.3s ease;

      .el-icon {
        color: #f8f8ff !important; /* 月光白图标 */
        filter: drop-shadow(0 0 5px rgba(248, 248, 255, 0.4));
      }

      &:hover {
        background: rgba(248, 248, 255, 0.1) !important;
        border-color: rgba(248, 248, 255, 0.5) !important;
        color: #ffffff !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(248, 248, 255, 0.2);

        .el-icon {
          color: #ffffff !important;
          filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
        }
      }

      &:disabled {
        background: rgba(0, 0, 0, 0.3) !important;
        border-color: rgba(248, 248, 255, 0.1) !important;
        color: rgba(248, 248, 255, 0.3) !important;
        cursor: not-allowed;

        .el-icon {
          color: rgba(248, 248, 255, 0.3) !important;
          filter: none;
        }
      }
    }
  }

  // 标签页
  .el-tabs {
    .el-tabs__header {
      background: rgba(15, 20, 25, 0.8) !important;
      border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
    }

    .el-tabs__item {
      @include starry-text('normal');

      &:hover {
        @include starry-text('large');
      }

      &.is-active {
        @include starry-text('large');
        border-bottom-color: #64b5f6 !important;
      }
    }
  }

  // 面包屑
  .el-breadcrumb {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        @include starry-text('normal');

        &:hover {
          @include starry-text('large');
        }
      }
    }
  }
}
