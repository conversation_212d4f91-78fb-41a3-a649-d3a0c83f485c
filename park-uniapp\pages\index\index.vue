<template>
  <view class="container">
    <!-- 头部区域 -->
    <view class="header">
      <image class="logo" src="/static/image/logo.png" mode="aspectFit"></image>
      <text class="app-name">智慧停车充电</text>
      <text class="version">v2.0</text>
    </view>
    
    <!-- 主要内容 -->
    <view class="content">
      <view class="maintenance-icon">🔧</view>
      <view class="title">系统升级中</view>
      <view class="subtitle">我们正在为您带来更好的停车体验</view>
      <view class="description">
        新版本将包含：
        <text class="feature">• 更快的缴费速度</text>
        <text class="feature">• 更简洁的操作界面</text>
        <text class="feature">• 更多的支付方式</text>
        <text class="feature">• 更稳定的系统性能</text>
      </view>
      <view class="time-info">正在全力升级中，即将为您提供更优质的服务</view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="button-area">
      <button class="main-btn" @click="handleMainFunction">停车缴费</button>
      <button class="secondary-btn" @click="handleContact">联系客服</button>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <text class="contact-info">如有疑问，请联系客服</text>
      <text class="company-info">© 2025 智慧停车系统</text>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app'

onLoad(() => {
  console.log('占位版本加载')
  
  // 延迟显示升级提示
  setTimeout(() => {
    uni.showModal({
      title: '系统升级通知',
      content: '小程序正在进行重大升级，将为您带来全新的停车缴费体验！感谢您的耐心等待。',
      showCancel: false,
      confirmText: '期待新版本',
      success: () => {
        console.log('用户确认升级通知')
      }
    })
  }, 1500)
})

onShow(() => {
  // 每次显示页面时的处理
  console.log('维护页面显示')
})

// 主要功能按钮 - 所有功能都显示维护中
const handleMainFunction = () => {
  uni.showToast({
    title: '功能升级中，敬请期待',
    icon: 'none',
    duration: 2000
  })
}


// 联系客服 - 显示开发中
const handleContact = () => {
  uni.showToast({
    title: '功能开发中，敬请期待',
    icon: 'none',
    duration: 2000
  })
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background-color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.version {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.content {
  flex: 1;
  text-align: center;
}

.maintenance-icon {
  font-size: 100rpx;
  margin-bottom: 40rpx;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.description {
  text-align: left;
  margin: 40rpx 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.feature {
  display: block;
  margin: 15rpx 0;
  font-size: 26rpx;
  line-height: 1.4;
}

.time-info {
  font-size: 24rpx;
  opacity: 0.8;
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 15rpx;
}

.button-area {
  margin: 60rpx 0 40rpx 0;
}

.main-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.main-btn:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}


.secondary-btn {
  background: transparent;
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  font-size: 28rpx;
  padding: 15rpx;
}

.secondary-btn:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}

.footer {
  text-align: center;
  opacity: 0.7;
  margin-top: 40rpx;
}

.contact-info {
  display: block;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.company-info {
  font-size: 20rpx;
  opacity: 0.6;
}
</style>
