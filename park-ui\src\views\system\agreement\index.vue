<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="协议标题" prop="agreementTitle">
        <el-input
          v-model="queryParams.agreementTitle"
          placeholder="请输入协议标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="协议类型" prop="agreementType">
        <el-select v-model="queryParams.agreementType" placeholder="请选择协议类型" clearable>
          <el-option
            v-for="dict in agreement_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:agreement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:agreement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:agreement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:agreement:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Refresh"
          @click="handleCleanContent"
          v-hasPermi="['system:agreement:edit']"
        >清理格式</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agreementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="协议ID" align="center" prop="id" />
      <el-table-column label="协议标题" align="center" prop="agreementTitle" :show-overflow-tooltip="true" />
      <el-table-column label="协议类型" align="center" prop="agreementType">
        <template #default="scope">
          <dict-tag :options="agreement_type" :value="scope.row.agreementType"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['system:agreement:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:agreement:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:agreement:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 原来的编辑弹窗已被手机样式弹窗替换，此处保留以防需要回退 -->
    <!-- <el-dialog v-if="false"> ... </el-dialog> -->

    <!-- 协议预览弹窗组件 -->
    <AgreementPreviewDialog
      v-model:visible="viewOpen"
      :agreement-data="viewForm"
    />

    <!-- 协议编辑弹窗组件 -->
    <AgreementEditDialog
      v-model:visible="editOpen"
      :agreement-data="editForm"
      :is-edit="isEdit"
      @save="handleSaveAgreement"
    />
  </div>
</template>

<script setup name="Agreement">
import { listAgreement, getAgreement, delAgreement, addAgreement, updateAgreement } from "@/api/system/agreement";
import AgreementPreviewDialog from './AgreementPreviewDialog.vue';
import AgreementEditDialog from './AgreementEditDialog.vue';

const { proxy } = getCurrentInstance();
const { agreement_type } = proxy.useDict('agreement_type');

const agreementList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const editOpen = ref(false);
const isEdit = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  viewForm: {},
  editForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    agreementTitle: null,
    agreementType: null,
    deleteFlag: 0
  },
  rules: {
    agreementType: [
      { required: true, message: "协议类型不能为空", trigger: "change" }
    ],
    agreementTitle: [
      { required: true, message: "协议标题不能为空", trigger: "blur" }
    ],
    agreementContent: [
      { required: true, message: "协议内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, viewForm, editForm, rules } = toRefs(data);



// 富文本编辑器配置已移至编辑组件内部

/** 查询系统协议列表 */
function getList() {
  loading.value = true;
  listAgreement(queryParams.value).then(response => {
    agreementList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    agreementType: null,
    agreementTitle: null,
    agreementContent: null,
    deleteFlag: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("agreementRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  editForm.value = {};
  isEdit.value = false;
  editOpen.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  editForm.value = {};
  const _id = row.id || ids.value
  getAgreement(_id).then(response => {
    editForm.value = response.data;
    isEdit.value = true;
    editOpen.value = true;
  });
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = {};
  const _id = row.id;
  getAgreement(_id).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 保存协议操作 */
function handleSaveAgreement(formData) {
  if (formData.id != null) {
    updateAgreement(formData).then(response => {
      proxy.$modal.msgSuccess("修改成功");
      editOpen.value = false;
      getList();
    }).catch(error => {
      console.error('更新协议失败:', error);
      proxy.$modal.msgError("更新失败：" + (error.response?.data?.msg || error.message));
    });
  } else {
    addAgreement(formData).then(response => {
      proxy.$modal.msgSuccess("新增成功");
      editOpen.value = false;
      getList();
    }).catch(error => {
      console.error('新增协议失败:', error);
      proxy.$modal.msgError("新增失败：" + (error.response?.data?.msg || error.message));
    });
  }
}

// 原来的提交函数已被新的保存函数替换

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除系统协议编号为"' + _ids + '"的数据项？').then(function() {
    return delAgreement(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/agreement/export', {
    ...queryParams.value
  }, `agreement_${new Date().getTime()}.xlsx`)
}

/** 格式化协议内容 */
function formatAgreementContent(content) {
  if (!content) return '';

  // 保留格式的清理
  let formatted = content
    // 移除完全空的p标签
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    // 移除只包含&nbsp;的p标签
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // 给p标签添加适当的样式，保持段落间距
    .replace(/<p([^>]*)>/gi, '<p$1 style="margin: 8px 0; line-height: 1.6;">')
    // 保留文本对齐样式
    .replace(/text-align:\s*center/gi, 'text-align: center !important')
    .replace(/text-align:\s*left/gi, 'text-align: left !important')
    .replace(/text-align:\s*right/gi, 'text-align: right !important')
    // 减少过多的&nbsp;但保留一些空格
    .replace(/&nbsp;{4,}/g, '&nbsp;&nbsp;&nbsp;')
    // 保留强调标签
    .replace(/<strong([^>]*)>/gi, '<strong$1 style="font-weight: bold;">')
    // 清理过多的连续空白，但不要太激进
    .replace(/\s{3,}/g, ' ');

  return formatted;
}

// 编辑器滚动修复函数已移至编辑组件内部

/** 清理所有协议内容格式 */
function handleCleanContent() {
  proxy.$modal.confirm('此操作将清理所有协议的格式问题（移除多余空行），是否继续？').then(function() {
    loading.value = true;

    // 获取所有协议数据
    listAgreement({ pageNum: 1, pageSize: 1000, deleteFlag: 0 }).then(response => {
      const agreements = response.rows;
      let updatePromises = [];

      agreements.forEach(agreement => {
        if (agreement.agreementContent) {
          const cleanedContent = formatAgreementContent(agreement.agreementContent);
          if (cleanedContent !== agreement.agreementContent) {
            agreement.agreementContent = cleanedContent;
            updatePromises.push(updateAgreement(agreement));
          }
        }
      });

      if (updatePromises.length > 0) {
        Promise.all(updatePromises).then(() => {
          proxy.$modal.msgSuccess(`成功清理了 ${updatePromises.length} 个协议的格式`);
          getList();
        }).catch(() => {
          proxy.$modal.msgError("清理格式时发生错误");
        }).finally(() => {
          loading.value = false;
        });
      } else {
        proxy.$modal.msgInfo("没有需要清理的内容");
        loading.value = false;
      }
    }).catch(() => {
      loading.value = false;
    });
  }).catch(() => {});
}



getList();

// 弹窗监听已移至编辑组件内部
</script>

<style>
/* 原来的编辑弹窗样式已移至独立的编辑组件中 */
</style>

<style scoped>
/* 协议详情对话框样式 */
.agreement-dialog :deep(.el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  height: 90vh !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.agreement-dialog :deep(.el-dialog__body) {
  flex: 1 !important;
  padding: 20px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  height: 100% !important;
}

.agreement-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.agreement-info {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.agreement-content-wrapper {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.agreement-content {
  overflow-y: auto;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  line-height: 1.6;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
  height: 60vh;
  max-height: 60vh;
}

/* 优化协议内容显示 */
.agreement-content :deep(p) {
  margin: 8px 0 !important;
  line-height: 1.6 !important;
  text-indent: 0 !important;
}

.agreement-content :deep(p:empty) {
  display: none !important;
}

.agreement-content :deep(strong) {
  font-weight: bold !important;
}

/* 强制文本对齐样式生效 */
.agreement-content :deep(p[style*="text-align: center"]) {
  text-align: center !important;
}

.agreement-content :deep(p[style*="text-align: left"]) {
  text-align: left !important;
}

.agreement-content :deep(p[style*="text-align: right"]) {
  text-align: right !important;
}

.agreement-content :deep(h1),
.agreement-content :deep(h2),
.agreement-content :deep(h3),
.agreement-content :deep(h4),
.agreement-content :deep(h5),
.agreement-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: bold;
}

.agreement-content :deep(ul),
.agreement-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.agreement-content :deep(li) {
  margin: 4px 0;
}

/* 文本对齐样式 */
.agreement-content :deep(.text-left) {
  text-align: left !important;
}

.agreement-content :deep(.text-center) {
  text-align: center !important;
}

.agreement-content :deep(.text-right) {
  text-align: right !important;
}

.agreement-content :deep(.text-justify) {
  text-align: justify !important;
}

/* 滚动条样式优化 */
.agreement-content::-webkit-scrollbar {
  width: 8px;
}

.agreement-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.agreement-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.agreement-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agreement-dialog :deep(.el-dialog) {
    margin: 0 !important;
    height: 100vh;
    width: 100% !important;
    border-radius: 0;
  }

  .agreement-content {
    padding: 15px;
    font-size: 13px;
  }
}
</style>
