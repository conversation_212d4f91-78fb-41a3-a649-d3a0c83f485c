<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="发票号码" prop="invoiceNo">
        <el-input
          v-model="queryParams.invoiceNo"
          placeholder="请输入发票号码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交易订单号" prop="tradeId">
        <el-input
          v-model="queryParams.tradeId"
          placeholder="请输入交易订单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票抬头" prop="invoiceTitleContent">
        <el-input
          v-model="queryParams.invoiceTitleContent"
          placeholder="请输入发票抬头"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择发票状态" clearable>
          <el-option label="已开具" value="ISSUED" />
          <el-option label="已冲红" value="REVERSED" />
          <el-option label="开票中" value="ISSUING" />
          <el-option label="开票失败" value="FAILED" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:invoiceRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['operation:invoiceRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="invoiceRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发票号码" align="center" prop="invoiceNo" width="180" />
      <el-table-column label="发票代码" align="center" prop="invoiceCode" width="120" />
      <el-table-column label="交易订单号" align="center" prop="tradeId" width="200" />
      <el-table-column label="发票抬头" align="center" prop="invoiceTitleContent" width="150" show-overflow-tooltip />
      <el-table-column label="场库名称" align="center" prop="warehouseName" width="120" />
      <el-table-column label="开票金额" align="center" prop="totalMoney" width="100">
        <template #default="scope">
          <span class="amount-text">¥{{ scope.row.totalMoney || '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发票状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 'ISSUED'" type="success">已开具</el-tag>
          <el-tag v-else-if="scope.row.status === 'REVERSED'" type="danger">已冲红</el-tag>
          <el-tag v-else-if="scope.row.status === 'ISSUING'" type="warning">开票中</el-tag>
          <el-tag v-else-if="scope.row.status === 'FAILED'" type="danger">开票失败</el-tag>
          <el-tag v-else type="info">{{ scope.row.status || '未知' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开票日期" align="center" prop="issueDate" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['operation:invoiceRecord:query']">查看</el-button>
          <el-button
            link
            type="warning"
            icon="RefreshRight"
            @click="handleReopen(scope.row)"
            v-hasPermi="['operation:invoiceRecord:reopen']"
            v-if="scope.row.status === 'ISSUED'"
          >重开</el-button>
          <el-button
            link
            type="danger"
            icon="Close"
            @click="handleReverse(scope.row)"
            v-hasPermi="['operation:invoiceRecord:reverse']"
            v-if="scope.row.status === 'ISSUED'"
          >冲红</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['operation:invoiceRecord:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 发票详情对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <div v-if="isViewMode" class="invoice-detail-view">
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">发票基本信息</span>
              <el-tag type="primary" size="small">{{ form.invoiceNo || '--' }}</el-tag>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="发票号码">{{ form.invoiceNo || '--' }}</el-descriptions-item>
            <el-descriptions-item label="发票代码">{{ form.invoiceCode || '--' }}</el-descriptions-item>
            <el-descriptions-item label="交易订单号">{{ form.tradeId || '--' }}</el-descriptions-item>
            <el-descriptions-item label="发票状态">
              <el-tag v-if="form.status === 'ISSUED'" type="success">已开具</el-tag>
              <el-tag v-else-if="form.status === 'REVERSED'" type="danger">已冲红</el-tag>
              <el-tag v-else-if="form.status === 'ISSUING'" type="warning">开票中</el-tag>
              <el-tag v-else-if="form.status === 'FAILED'" type="danger">开票失败</el-tag>
              <el-tag v-else type="info">{{ form.status || '未知' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开票金额">
              <span class="amount-text">¥{{ form.totalMoney || '0.00' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="税额">
              <span class="tax-text">¥{{ form.totalTax || '0.00' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="开票日期">{{ parseTime(form.issueDate, '{y}-{m}-{d} {h}:{i}:{s}') || '--' }}</el-descriptions-item>
            <el-descriptions-item label="场库名称">{{ form.warehouseName || '--' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">发票抬头信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="发票抬头">{{ form.invoiceTitleContent || '--' }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号">{{ form.unitDutyParagraph || '--' }}</el-descriptions-item>
            <el-descriptions-item label="注册地址">{{ form.registerAddress || '--' }}</el-descriptions-item>
            <el-descriptions-item label="注册电话">{{ form.registerPhone || '--' }}</el-descriptions-item>
            <el-descriptions-item label="开户银行">{{ form.depositBank || '--' }}</el-descriptions-item>
            <el-descriptions-item label="银行账号">{{ form.bankAccount || '--' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">联系信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="通知手机号">{{ form.notifyMobileNo || '--' }}</el-descriptions-item>
            <el-descriptions-item label="通知邮箱">{{ form.notifyEmail || '--' }}</el-descriptions-item>
            <el-descriptions-item label="PDF地址" v-if="form.pdfUrl">
              <el-link :href="form.pdfUrl" target="_blank" type="primary">查看PDF</el-link>
            </el-descriptions-item>
            <el-descriptions-item label="PDF预览地址" v-if="form.pdfPreviewUrl">
              <el-link :href="form.pdfPreviewUrl" target="_blank" type="primary">预览PDF</el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InvoiceRecord">
import { parseTime } from "@/utils/ruoyi";
import { listInvoiceRecord, getInvoiceRecord, delInvoiceRecord, reopenInvoice, reverseInvoice } from "@/api/operation/invoiceRecord";

const { proxy } = getCurrentInstance();

const invoiceRecordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const isViewMode = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    invoiceNo: null,
    tradeId: null,
    invoiceTitleContent: null,
    status: null,
  },
});

const { queryParams, form } = toRefs(data);

/** 查询发票记录管理列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != dateRange.value && '' != dateRange.value) {
    queryParams.value.params["beginTime"] = dateRange.value[0];
    queryParams.value.params["endTime"] = dateRange.value[1];
  }
  listInvoiceRecord(queryParams.value).then(response => {
    invoiceRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  isViewMode.value = true;
  const id = row.id;
  getInvoiceRecord(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "查看发票详情";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const invoiceIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除发票记录编号为"' + invoiceIds + '"的数据项？').then(function() {
    return delInvoiceRecord(invoiceIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 发票重开 */
function handleReopen(row) {
  proxy.$modal.confirm('确认要重开发票"' + row.invoiceNo + '"吗？').then(function() {
    return reopenInvoice(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发票重开成功");
  }).catch(() => {});
}

/** 发票冲红 */
function handleReverse(row) {
  proxy.$modal.confirm('确认要冲红发票"' + row.invoiceNo + '"吗？冲红后发票将无法使用！').then(function() {
    return reverseInvoice(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发票冲红成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/operation/invoiceRecord/export', {
    ...queryParams.value
  }, `invoice_record_${new Date().getTime()}.xlsx`)
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {};
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.amount-text {
  color: #e6a23c;
  font-weight: bold;
}

.tax-text {
  color: #67c23a;
  font-weight: bold;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}
</style>


