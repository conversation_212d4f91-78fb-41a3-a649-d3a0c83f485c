[{"id": "TC_001", "scenario": "新用户手机号验证码注册登录", "preconditions": "1.网络正常\n2.用户未注册\n3.已同意用户协议", "steps": "1.输入手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "inputData": "手机号：13800138000\n验证码：123456", "expectedResult": "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.用户信息存储成功", "priority": "P0", "passed": false}, {"id": "TC_002", "scenario": "老用户手机号验证码登录", "preconditions": "1.网络正常\n2.用户已注册\n3.已同意用户协议", "steps": "1.输入已注册手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "inputData": "手机号：13800138001\n验证码：123456", "expectedResult": "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.更新用户openid", "priority": "P0", "passed": false}, {"id": "TC_003", "scenario": "老用户openid已存在且匹配", "preconditions": "1.网络正常\n2.用户已注册\n3.openid已绑定\n4.已同意用户协议", "steps": "1.输入已注册手机号\n2.点击获取验证码\n3.输入验证码\n4.点击登录", "inputData": "手机号：13800138002\n验证码：123456", "expectedResult": "1.验证码发送成功\n2.登录成功\n3.跳转到首页\n4.不更新openid", "priority": "P0", "passed": false}, {"id": "TC_004", "scenario": "手机号格式错误", "preconditions": "1.网络正常", "steps": "1.输入错误格式手机号\n2.点击获取验证码", "inputData": "手机号：1380013800", "expectedResult": "显示\"手机号格式错误\"提示", "priority": "P1", "passed": false}, {"id": "TC_005", "scenario": "手机号为空", "preconditions": "1.网络正常", "steps": "1.不输入手机号\n2.点击获取验证码", "inputData": "手机号：空", "expectedResult": "显示\"请输入手机号\"提示", "priority": "P1", "passed": false}, {"id": "TC_006", "scenario": "验证码错误", "preconditions": "1.网络正常\n2.已获取验证码\n3.已同意用户协议", "steps": "1.输入正确手机号\n2.输入错误验证码\n3.点击登录", "inputData": "手机号：13800138000\n验证码：000000", "expectedResult": "显示\"验证码错误\"提示", "priority": "P1", "passed": false}, {"id": "TC_007", "scenario": "验证码为空", "preconditions": "1.网络正常\n2.已输入手机号\n3.已同意用户协议", "steps": "1.输入正确手机号\n2.不输入验证码\n3.点击登录", "inputData": "手机号：13800138000\n验证码：空", "expectedResult": "显示\"请输入正确的手机号或者验证码\"提示", "priority": "P1", "passed": false}, {"id": "TC_008", "scenario": "验证码过期", "preconditions": "1.网络正常\n2.验证码已过期（5分钟后）\n3.已同意用户协议", "steps": "1.输入正确手机号\n2.输入过期验证码\n3.点击登录", "inputData": "手机号：13800138000\n验证码：123456（过期）", "expectedResult": "显示\"验证码未发送或已过期\"提示", "priority": "P1", "passed": false}, {"id": "TC_009", "scenario": "未同意用户协议", "preconditions": "1.网络正常\n2.已获取验证码", "steps": "1.输入正确手机号\n2.输入正确验证码\n3.未勾选用户协议\n4.点击登录", "inputData": "手机号：13800138000\n验证码：123456", "expectedResult": "显示\"请阅读并同意用户协议和隐私政策\"提示", "priority": "P1", "passed": false}, {"id": "TC_010", "scenario": "频繁获取验证码", "preconditions": "1.网络正常\n2.已获取一次验证码", "steps": "1.输入手机号\n2.连续点击获取验证码", "inputData": "手机号：13800138000", "expectedResult": "显示\"禁止频繁获取验证码\"提示，按钮倒计时60秒", "priority": "P1", "passed": false}, {"id": "TC_011", "scenario": "网络异常", "preconditions": "1.网络断开", "steps": "1.输入手机号\n2.点击获取验证码", "inputData": "手机号：13800138000", "expectedResult": "显示网络错误提示", "priority": "P2", "passed": false}, {"id": "TC_012", "scenario": "获取微信code失败", "preconditions": "1.网络正常\n2.微信授权失败\n3.已同意用户协议", "steps": "1.输入正确手机号和验证码\n2.点击登录", "inputData": "手机号：13800138000\n验证码：123456", "expectedResult": "显示\"获取微信code失败\"提示", "priority": "P2", "passed": false}, {"id": "TC_013", "scenario": "用户被封禁", "preconditions": "1.网络正常\n2.用户状态为封禁（status=1）\n3.已同意用户协议", "steps": "1.输入被封禁用户手机号\n2.获取验证码\n3.输入验证码\n4.点击登录", "inputData": "手机号：13800138003\n验证码：123456", "expectedResult": "显示用户被封禁相关提示", "priority": "P1", "passed": false}, {"id": "TC_014", "scenario": "新用户微信一键登录注册", "preconditions": "1.网络正常\n2.微信授权正常\n3.用户未注册\n4.已同意用户协议", "steps": "1.勾选用户协议\n2.点击微信登录按钮\n3.授权获取手机号", "inputData": "微信授权手机号：13900139000", "expectedResult": "1.获取手机号成功\n2.自动注册新用户\n3.登录成功\n4.跳转到首页", "priority": "P0", "passed": false}, {"id": "TC_015", "scenario": "老用户微信一键登录", "preconditions": "1.网络正常\n2.微信授权正常\n3.用户已注册\n4.已同意用户协议", "steps": "1.勾选用户协议\n2.点击微信登录按钮\n3.授权获取手机号", "inputData": "微信授权手机号：13900139001", "expectedResult": "1.获取手机号成功\n2.登录成功\n3.更新用户openid\n4.跳转到首页", "priority": "P0", "passed": false}, {"id": "TC_016", "scenario": "未同意用户协议点击微信登录", "preconditions": "1.网络正常\n2.未勾选用户协议", "steps": "1.不勾选用户协议\n2.点击微信登录按钮", "inputData": "无", "expectedResult": "显示\"请阅读并同意用户协议和隐私政策\"提示", "priority": "P1", "passed": false}, {"id": "TC_017", "scenario": "微信授权失败", "preconditions": "1.网络正常\n2.已同意用户协议", "steps": "1.勾选用户协议\n2.点击微信登录按钮\n3.拒绝微信授权", "inputData": "无", "expectedResult": "显示\"获取微信授权失败\"提示", "priority": "P1", "passed": false}, {"id": "TC_018", "scenario": "获取手机号失败", "preconditions": "1.网络正常\n2.已同意用户协议\n3.微信授权成功", "steps": "1.勾选用户协议\n2.点击微信登录按钮\n3.拒绝手机号授权", "inputData": "无", "expectedResult": "显示\"获取手机号失败\"提示", "priority": "P1", "passed": false}, {"id": "TC_019", "scenario": "微信登录网络异常", "preconditions": "1.网络异常\n2.已同意用户协议", "steps": "1.勾选用户协议\n2.点击微信登录按钮", "inputData": "无", "expectedResult": "显示\"登录失败，请重试\"提示", "priority": "P2", "passed": false}, {"id": "TC_020", "scenario": "页面加载正常", "preconditions": "1.小程序启动正常", "steps": "1.进入登录页面", "inputData": "无", "expectedResult": "1.页面布局正常\n2.所有元素显示正常\n3.返回按钮显示正常", "priority": "P0", "passed": false}, {"id": "TC_021", "scenario": "手机号输入框限制", "preconditions": "1.进入登录页面", "steps": "1.在手机号输入框输入超长字符", "inputData": "手机号：138001380001234", "expectedResult": "输入框最多显示11位数字", "priority": "P1", "passed": false}, {"id": "TC_022", "scenario": "验证码输入框限制", "preconditions": "1.进入登录页面", "steps": "1.在验证码输入框输入超长字符", "inputData": "验证码：1234567890", "expectedResult": "输入框最多显示6位字符", "priority": "P1", "passed": false}, {"id": "TC_023", "scenario": "获取验证码按钮倒计时", "preconditions": "1.进入登录页面\n2.已输入手机号", "steps": "1.点击获取验证码按钮", "inputData": "手机号：13800138000", "expectedResult": "按钮显示倒计时60秒，期间不可点击", "priority": "P1", "passed": false}, {"id": "TC_024", "scenario": "用户协议勾选状态", "preconditions": "1.进入登录页面", "steps": "1.点击用户协议勾选框", "inputData": "无", "expectedResult": "勾选状态正确切换，微信登录按钮状态相应变化", "priority": "P1", "passed": false}, {"id": "TC_025", "scenario": "用户协议链接跳转", "preconditions": "1.进入登录页面", "steps": "1.点击\"用户协议\"链接", "inputData": "无", "expectedResult": "跳转到用户协议页面", "priority": "P2", "passed": false}, {"id": "TC_026", "scenario": "隐私政策链接跳转", "preconditions": "1.进入登录页面", "steps": "1.点击\"隐私政策\"链接", "inputData": "无", "expectedResult": "跳转到隐私政策页面", "priority": "P2", "passed": false}, {"id": "TC_027", "scenario": "返回按钮功能", "preconditions": "1.从其他页面进入登录页面", "steps": "1.点击返回按钮", "inputData": "无", "expectedResult": "返回上一页面或首页", "priority": "P1", "passed": false}, {"id": "TC_028", "scenario": "登录加载状态", "preconditions": "1.网络正常\n2.已输入正确信息\n3.已同意用户协议", "steps": "1.点击登录按钮", "inputData": "手机号：13800138000\n验证码：123456", "expectedResult": "显示\"登录中...\"加载提示，按钮不可重复点击", "priority": "P1", "passed": false}, {"id": "TC_029", "scenario": "验证码发送加载状态", "preconditions": "1.网络正常\n2.已输入手机号", "steps": "1.点击获取验证码按钮", "inputData": "手机号：13800138000", "expectedResult": "显示发送中状态，按钮暂时不可点击", "priority": "P2", "passed": false}, {"id": "TC_030", "scenario": "登录成功后token存储", "preconditions": "1.登录成功", "steps": "1.检查本地存储", "inputData": "无", "expectedResult": "token和wxUser信息正确存储到本地", "priority": "P0", "passed": false}, {"id": "TC_031", "scenario": "登录失败后存储清理", "preconditions": "1.登录失败", "steps": "1.检查本地存储", "inputData": "无", "expectedResult": "不存储任何登录信息", "priority": "P1", "passed": false}, {"id": "TC_032", "scenario": "401错误后存储清理", "preconditions": "1.token过期或无效", "steps": "1.发起需要认证的请求", "inputData": "无", "expectedResult": "自动清除本地token和用户信息", "priority": "P1", "passed": false}, {"id": "TC_033", "scenario": "手机号最短长度", "preconditions": "1.进入登录页面", "steps": "1.输入10位数字\n2.点击获取验证码", "inputData": "手机号：1380013800", "expectedResult": "显示手机号格式错误提示", "priority": "P2", "passed": false}, {"id": "TC_034", "scenario": "手机号最长长度", "preconditions": "1.进入登录页面", "steps": "1.输入12位数字\n2.点击获取验证码", "inputData": "手机号：138001380001", "expectedResult": "只接受前11位，或显示格式错误", "priority": "P2", "passed": false}, {"id": "TC_035", "scenario": "验证码最短长度", "preconditions": "1.已获取验证码", "steps": "1.输入5位验证码\n2.点击登录", "inputData": "验证码：12345", "expectedResult": "显示验证码格式错误提示", "priority": "P2", "passed": false}, {"id": "TC_036", "scenario": "验证码最长长度", "preconditions": "1.已获取验证码", "steps": "1.输入7位验证码\n2.点击登录", "inputData": "验证码：1234567", "expectedResult": "只接受前6位，或显示格式错误", "priority": "P2", "passed": false}, {"id": "TC_037", "scenario": "特殊字符输入", "preconditions": "1.进入登录页面", "steps": "1.在手机号框输入特殊字符", "inputData": "手机号：138-0013-8000", "expectedResult": "过滤特殊字符或显示格式错误", "priority": "P2", "passed": false}, {"id": "TC_038", "scenario": "验证码发送响应时间", "preconditions": "1.网络正常", "steps": "1.输入手机号\n2.点击获取验证码\n3.记录响应时间", "inputData": "手机号：13800138000", "expectedResult": "响应时间 < 3秒", "priority": "P2", "passed": false}, {"id": "TC_039", "scenario": "登录响应时间", "preconditions": "1.网络正常\n2.已获取验证码", "steps": "1.输入正确信息\n2.点击登录\n3.记录响应时间", "inputData": "手机号：13800138000\n验证码：123456", "expectedResult": "响应时间 < 5秒", "priority": "P2", "passed": false}, {"id": "TC_040", "scenario": "微信登录响应时间", "preconditions": "1.网络正常\n2.微信授权正常", "steps": "1.点击微信登录\n2.完成授权\n3.记录响应时间", "inputData": "无", "expectedResult": "响应时间 < 5秒", "priority": "P2", "passed": false}, {"id": "TC_041", "scenario": "iOS设备登录", "preconditions": "1.iOS设备\n2.微信最新版本", "steps": "1.执行完整登录流程", "inputData": "标准测试数据", "expectedResult": "功能正常，界面适配良好", "priority": "P1", "passed": false}, {"id": "TC_042", "scenario": "Android设备登录", "preconditions": "1.Android设备\n2.微信最新版本", "steps": "1.执行完整登录流程", "inputData": "标准测试数据", "expectedResult": "功能正常，界面适配良好", "priority": "P1", "passed": false}, {"id": "TC_043", "scenario": "不同屏幕尺寸适配", "preconditions": "1.不同尺寸设备", "steps": "1.检查页面布局\n2.执行登录流程", "inputData": "标准测试数据", "expectedResult": "界面元素正常显示，功能正常", "priority": "P2", "passed": false}, {"id": "TC_044", "scenario": "敏感信息存储安全", "preconditions": "1.登录成功", "steps": "1.检查本地存储内容", "inputData": "无", "expectedResult": "密码等敏感信息不明文存储", "priority": "P0", "passed": false}, {"id": "TC_045", "scenario": "token有效期验证", "preconditions": "1.登录成功\n2.等待token过期", "steps": "1.使用过期token请求接口", "inputData": "无", "expectedResult": "返回401错误，自动清除本地信息", "priority": "P1", "passed": false}, {"id": "TC_046", "scenario": "重放攻击防护", "preconditions": "1.已获取验证码", "steps": "1.多次使用同一验证码登录", "inputData": "验证码：123456", "expectedResult": "第二次使用时提示验证码无效", "priority": "P1", "passed": false}, {"id": "TC_047", "scenario": "登录后页面跳转", "preconditions": "1.登录成功", "steps": "1.检查页面跳转", "inputData": "无", "expectedResult": "正确跳转到首页tab", "priority": "P0", "passed": false}, {"id": "TC_048", "scenario": "登录状态保持", "preconditions": "1.登录成功\n2.关闭小程序\n3.重新打开", "steps": "1.检查登录状态", "inputData": "无", "expectedResult": "保持登录状态，不需要重新登录", "priority": "P0", "passed": false}, {"id": "TC_049", "scenario": "退出登录功能", "preconditions": "1.已登录状态", "steps": "1.执行退出登录操作", "inputData": "无", "expectedResult": "清除本地存储，返回登录页面", "priority": "P1", "passed": false}]