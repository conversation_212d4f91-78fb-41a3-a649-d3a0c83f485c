[{"id": "TC_SYS_001", "scenario": "系统用户管理 - 查询用户列表", "preconditions": "1.管理员已登录\n2.拥有用户查询权限\n3.系统中存在用户数据", "steps": "1.访问用户管理页面\n2.点击查询按钮\n3.验证用户列表显示", "inputData": "API: GET /system/user/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回用户列表数据\n2.包含用户基本信息\n3.分页信息正确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_SYS_002", "scenario": "系统用户管理 - 新增用户", "preconditions": "1.管理员已登录\n2.拥有用户新增权限\n3.准备有效用户数据", "steps": "1.点击新增用户按钮\n2.填写用户信息\n3.选择角色和部门\n4.提交保存", "inputData": "API: POST /system/user\n数据: {userName:'testuser', nickName:'测试用户', email:'<EMAIL>', phoneNumber:'13800138000'}", "expectedResult": "1.用户创建成功\n2.返回成功提示\n3.用户列表更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_SYS_003", "scenario": "系统用户管理 - 修改用户信息", "preconditions": "1.管理员已登录\n2.拥有用户修改权限\n3.存在可修改的用户", "steps": "1.选择要修改的用户\n2.点击修改按钮\n3.更新用户信息\n4.保存修改", "inputData": "API: PUT /system/user\n数据: {userId:1, nickName:'修改后的昵称', email:'<EMAIL>'}", "expectedResult": "1.用户信息更新成功\n2.返回成功提示\n3.列表显示更新后的信息", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_SYS_004", "scenario": "系统用户管理 - 删除用户", "preconditions": "1.管理员已登录\n2.拥有用户删除权限\n3.存在可删除的用户", "steps": "1.选择要删除的用户\n2.点击删除按钮\n3.确认删除操作", "inputData": "API: DELETE /system/user/{userId}\n参数: userId=2", "expectedResult": "1.用户删除成功\n2.返回成功提示\n3.用户从列表中移除", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_SYS_005", "scenario": "系统用户管理 - 重置用户密码", "preconditions": "1.管理员已登录\n2.拥有密码重置权限\n3.存在需要重置密码的用户", "steps": "1.选择用户\n2.点击重置密码\n3.输入新密码\n4.确认重置", "inputData": "API: PUT /system/user/resetPwd\n数据: {userId:1, password:'123456'}", "expectedResult": "1.密码重置成功\n2.返回成功提示\n3.用户可用新密码登录", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_ROLE_001", "scenario": "角色管理 - 查询角色列表", "preconditions": "1.管理员已登录\n2.拥有角色查询权限\n3.系统中存在角色数据", "steps": "1.访问角色管理页面\n2.查看角色列表\n3.验证角色信息显示", "inputData": "API: GET /system/role/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回角色列表\n2.显示角色名称、权限等信息\n3.分页正常", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ROLE_002", "scenario": "角色管理 - 分配数据权限", "preconditions": "1.管理员已登录\n2.拥有角色编辑权限(system:role:edit)\n3.存在角色和部门数据", "steps": "1.选择角色\n2.点击数据权限按钮\n3.选择数据权限范围\n4.保存权限配置", "inputData": "API: PUT /system/role/dataScope\n数据: {roleId:2, dataScope:'2', deptIds:[100,101]}", "expectedResult": "1.数据权限分配成功\n2.角色数据权限更新\n3.用户数据访问范围生效", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ORDER_001", "scenario": "临停订单管理 - 查询订单列表", "preconditions": "1.管理员已登录\n2.拥有订单查询权限\n3.系统中存在停车订单", "steps": "1.访问停车订单管理页面\n2.设置查询条件\n3.点击查询按钮", "inputData": "API: GET /system/parkingOrder/list\n参数: pageNum=1, pageSize=10, plateNo='京A12345'", "expectedResult": "1.返回订单列表\n2.显示订单详细信息\n3.支持条件筛选", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ORDER_002", "scenario": "临停订单管理 - 订单退款", "preconditions": "1.管理员已登录\n2.拥有订单退款权限\n3.存在已支付的订单", "steps": "1.选择已支付订单\n2.点击退款按钮\n3.填写退款信息\n4.确认退款", "inputData": "API: POST /system/parkingOrder/refund\n数据: {orderId:1, refundAmount:10.00, refundReason:'用户申请退款'}", "expectedResult": "1.退款处理成功\n2.订单状态更新为已退款\n3.退款记录生成", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ORDER_003", "scenario": "临停订单管理 - 导出订单数据", "preconditions": "1.管理员已登录\n2.拥有订单导出权限\n3.存在订单数据", "steps": "1.设置导出条件\n2.点击导出按钮\n3.下载导出文件", "inputData": "API: POST /system/parkingOrder/export\n参数: startTime='2024-01-01', endTime='2024-12-31'", "expectedResult": "1.生成Excel文件\n2.包含订单详细信息\n3.文件下载成功", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_VIP_001", "scenario": "会员管理 - 查询会员列表", "preconditions": "1.管理员已登录\n2.拥有会员查询权限\n3.系统中存在会员数据", "steps": "1.访问会员管理页面\n2.设置查询条件\n3.查看会员列表", "inputData": "API: GET /system/vip/member/list\n参数: pageNum=1, pageSize=10, phoneNumber='138'", "expectedResult": "1.返回会员列表\n2.显示会员基本信息\n3.支持模糊查询", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_VIP_002", "scenario": "会员交易记录 - 查询交易列表", "preconditions": "1.管理员已登录\n2.拥有交易查询权限\n3.存在会员交易记录", "steps": "1.访问会员交易记录页面\n2.设置查询条件\n3.查看交易列表", "inputData": "API: GET /system/vip/transaction/list\n参数: pageNum=1, pageSize=10, payStatus=5", "expectedResult": "1.返回交易记录列表\n2.显示交易详细信息\n3.支持状态筛选", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_VIP_003", "scenario": "会员交易记录 - 交易退款", "preconditions": "1.管理员已登录\n2.拥有交易退款权限\n3.存在已支付的交易记录", "steps": "1.选择已支付交易\n2.点击退款按钮\n3.填写退款信息\n4.确认退款", "inputData": "API: POST /system/vip/transaction/refund\n数据: {transactionId:1, refundAmount:100.00, refundReason:'会员申请退款'}", "expectedResult": "1.退款处理成功\n2.交易状态更新\n3.会员余额调整", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_001", "scenario": "场库管理 - 查询场库列表", "preconditions": "1.管理员已登录\n2.拥有场库查询权限\n3.系统中存在场库数据", "steps": "1.访问场库管理页面\n2.查看场库列表\n3.验证场库信息", "inputData": "API: GET /system/platform/warehouse/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回场库列表\n2.显示场库基本信息\n3.支持树形结构显示", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_002", "scenario": "场库管理 - 新增场库", "preconditions": "1.管理员已登录\n2.拥有场库新增权限\n3.准备场库基本信息", "steps": "1.点击新增场库\n2.填写场库信息\n3.设置场库配置\n4.保存场库", "inputData": "API: POST /system/platform/warehouse\n数据: {warehouseName:'测试停车场', address:'测试地址', totalSpaces:100}", "expectedResult": "1.场库创建成功\n2.返回成功提示\n3.场库列表更新", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_003", "scenario": "场库管理 - 修改场库信息", "preconditions": "1.管理员已登录\n2.拥有场库修改权限\n3.存在可修改的场库", "steps": "1.选择场库\n2.点击修改按钮\n3.更新场库信息\n4.保存修改", "inputData": "API: PUT /system/platform/warehouse\n数据: {id:1, warehouseName:'修改后的停车场', totalSpaces:150}", "expectedResult": "1.场库信息更新成功\n2.返回成功提示\n3.列表显示更新信息", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_BLACKLIST_001", "scenario": "黑名单管理 - 查询黑名单列表", "preconditions": "1.管理员已登录\n2.拥有黑名单查询权限\n3.系统中存在黑名单数据", "steps": "1.访问黑名单管理页面\n2.查看黑名单列表\n3.验证黑名单信息", "inputData": "API: GET /system/owner/blacklist/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回黑名单列表\n2.显示车牌号、原因等信息\n3.分页正常", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_BLACKLIST_002", "scenario": "黑名单管理 - 添加黑名单", "preconditions": "1.管理员已登录\n2.拥有黑名单新增权限\n3.准备车牌号信息", "steps": "1.点击新增黑名单\n2.输入车牌号\n3.填写拉黑原因\n4.保存黑名单", "inputData": "API: POST /system/owner/blacklist\n数据: {plateNo:'京A88888', reason:'违规停车', warehouseId:1}", "expectedResult": "1.黑名单添加成功\n2.车辆被限制进入\n3.列表更新显示", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_WHITELIST_001", "scenario": "白名单管理 - 查询白名单列表", "preconditions": "1.管理员已登录\n2.拥有白名单查询权限\n3.系统中存在白名单数据", "steps": "1.访问白名单管理页面\n2.查看白名单列表\n3.验证白名单信息", "inputData": "API: GET /system/owner/whitelist/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回白名单列表\n2.显示车牌号、类型等信息\n3.分页正常", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_WHITELIST_002", "scenario": "白名单管理 - 添加白名单", "preconditions": "1.管理员已登录\n2.拥有白名单新增权限\n3.准备车牌号信息", "steps": "1.点击新增白名单\n2.输入车牌号\n3.选择白名单类型\n4.保存白名单", "inputData": "API: POST /system/owner/whitelist\n数据: {plateNo:'京A66666', type:'VIP车辆', warehouseId:1}", "expectedResult": "1.白名单添加成功\n2.车辆享受免费通行\n3.列表更新显示", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MENU_001", "scenario": "菜单管理 - 查询菜单列表", "preconditions": "1.管理员已登录\n2.拥有菜单查询权限\n3.系统中存在菜单数据", "steps": "1.访问菜单管理页面\n2.查看菜单树形结构\n3.验证菜单信息", "inputData": "API: GET /system/menu/list\n参数: menuName='', status='0'", "expectedResult": "1.返回菜单树形列表\n2.显示菜单层级关系\n3.包含权限标识", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_MENU_002", "scenario": "菜单管理 - 新增菜单", "preconditions": "1.管理员已登录\n2.拥有菜单新增权限\n3.准备菜单信息", "steps": "1.点击新增菜单\n2.填写菜单信息\n3.设置权限标识\n4.保存菜单", "inputData": "API: POST /system/menu\n数据: {menuName:'测试菜单', parentId:0, menuType:'M', perms:'test:menu:list'}", "expectedResult": "1.菜单创建成功\n2.菜单树更新\n3.权限系统生效", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_DEPT_001", "scenario": "部门管理 - 查询部门列表", "preconditions": "1.管理员已登录\n2.拥有部门查询权限\n3.系统中存在部门数据", "steps": "1.访问部门管理页面\n2.查看部门树形结构\n3.验证部门信息", "inputData": "API: GET /system/dept/list\n参数: deptName='', status='0'", "expectedResult": "1.返回部门树形列表\n2.显示部门层级关系\n3.包含负责人信息", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_DEPT_002", "scenario": "部门管理 - 新增部门", "preconditions": "1.管理员已登录\n2.拥有部门新增权限\n3.准备部门信息", "steps": "1.点击新增部门\n2.填写部门信息\n3.选择上级部门\n4.保存部门", "inputData": "API: POST /system/dept\n数据: {deptName:'测试部门', parentId:100, leader:'张三', phone:'13800138000'}", "expectedResult": "1.部门创建成功\n2.部门树更新\n3.可分配给用户", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_CONFIG_001", "scenario": "系统配置 - 查询配置列表", "preconditions": "1.管理员已登录\n2.拥有配置查询权限\n3.系统中存在配置参数", "steps": "1.访问系统配置页面\n2.查看配置列表\n3.验证配置信息", "inputData": "API: GET /system/config/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回配置参数列表\n2.显示配置键值对\n3.支持搜索筛选", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_CONFIG_002", "scenario": "系统配置 - 修改配置参数", "preconditions": "1.管理员已登录\n2.拥有配置修改权限\n3.存在可修改的配置", "steps": "1.选择配置参数\n2.点击修改按钮\n3.更新配置值\n4.保存修改", "inputData": "API: PUT /system/config\n数据: {configId:1, configValue:'新的配置值'}", "expectedResult": "1.配置更新成功\n2.系统应用新配置\n3.返回成功提示", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_LOG_001", "scenario": "操作日志 - 查询操作日志", "preconditions": "1.管理员已登录\n2.拥有日志查询权限\n3.系统中存在操作日志", "steps": "1.访问操作日志页面\n2.设置查询条件\n3.查看日志列表", "inputData": "API: GET /system/operlog/list\n参数: pageNum=1, pageSize=10, operName='admin'", "expectedResult": "1.返回操作日志列表\n2.显示操作详细信息\n3.支持时间范围查询", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_LOGIN_001", "scenario": "登录日志 - 查询登录日志", "preconditions": "1.管理员已登录\n2.拥有日志查询权限\n3.系统中存在登录日志", "steps": "1.访问登录日志页面\n2.设置查询条件\n3.查看登录记录", "inputData": "API: GET /system/logininfor/list\n参数: pageNum=1, pageSize=10, userName='admin'", "expectedResult": "1.返回登录日志列表\n2.显示登录时间、IP等信息\n3.支持状态筛选", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_EXCEPTION_001", "scenario": "异常数据日志 - 查询错误数据日志", "preconditions": "1.管理员已登录\n2.拥有错误数据查询权限(system:errorDataLog:query)\n3.系统中存在错误数据日志", "steps": "1.访问异常订单管理页面\n2.设置查询条件\n3.查看错误数据日志列表", "inputData": "API: GET /system/errorDataLog/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回错误数据日志列表\n2.显示错误码、车牌号等信息\n3.支持条件筛选", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_EXCEPTION_002", "scenario": "异常数据日志 - 处理异常数据", "preconditions": "1.管理员已登录\n2.拥有错误数据处理权限\n3.存在待处理异常数据", "steps": "1.选择错误数据记录\n2.点击处理按钮\n3.填写处理结果\n4.确认处理", "inputData": "API: POST /system/errorDataLog/handle\n数据: {id:1, handleResult:'已处理', handleRemark:'人工核实后处理'}", "expectedResult": "1.异常数据处理成功\n2.状态更新为已处理\n3.记录处理日志", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_GATE_001", "scenario": "车辆出入场记录 - 查询出入场记录", "preconditions": "1.管理员已登录\n2.拥有出入场记录查询权限\n3.系统中存在出入场数据", "steps": "1.访问出入场记录页面\n2.设置查询条件\n3.查看记录列表", "inputData": "API: GET /system/order/gateParkingInfo/list\n参数: pageNum=1, pageSize=10, plateNo='京A12345'", "expectedResult": "1.返回出入场记录列表\n2.显示进出时间、车牌等信息\n3.支持时间范围查询", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_OPERATOR_001", "scenario": "运营商管理 - 查询运营商列表", "preconditions": "1.管理员已登录\n2.拥有运营商查询权限\n3.系统中存在运营商数据", "steps": "1.访问运营商管理页面\n2.查看运营商列表\n3.验证运营商信息", "inputData": "API: GET /system/platform/operator/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回运营商列表\n2.显示运营商基本信息\n3.包含联系方式等详情", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_OPERATOR_002", "scenario": "运营商管理 - 新增运营商", "preconditions": "1.管理员已登录\n2.拥有运营商新增权限\n3.准备运营商信息", "steps": "1.点击新增运营商\n2.填写运营商信息\n3.设置联系方式\n4.保存运营商", "inputData": "API: POST /system/platform/operator\n数据: {operatorName:'测试运营商', contactPerson:'李四', phone:'13900139000'}", "expectedResult": "1.运营商创建成功\n2.返回成功提示\n3.列表更新显示", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_MGR_001", "scenario": "场库管理员 - 查询管理员列表", "preconditions": "1.管理员已登录\n2.拥有场库管理员查询权限\n3.系统中存在管理员数据", "steps": "1.访问场库管理员页面\n2.查看管理员列表\n3.验证管理员信息", "inputData": "API: GET /system/platform/warehouseManager/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回管理员列表\n2.显示管理员基本信息\n3.包含所管理的场库", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_SPECIAL_USER_001", "scenario": "特殊用户管理 - 查询特殊用户", "preconditions": "1.管理员已登录\n2.拥有特殊用户查询权限\n3.系统中存在特殊用户数据", "steps": "1.访问特殊用户管理页面\n2.查看特殊用户列表\n3.验证用户信息", "inputData": "API: GET /system/vip/specialUser/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回特殊用户列表\n2.显示用户类型和权限\n3.包含有效期信息", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_001", "scenario": "会员套餐管理 - 查询套餐列表", "preconditions": "1.管理员已登录\n2.拥有套餐查询权限\n3.系统中存在套餐数据", "steps": "1.访问会员套餐管理页面\n2.查看套餐列表\n3.验证套餐信息", "inputData": "API: GET /system/vip/package/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回套餐列表\n2.显示套餐价格、时长等信息\n3.包含套餐状态", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_002", "scenario": "会员套餐管理 - 新增套餐", "preconditions": "1.管理员已登录\n2.拥有套餐新增权限\n3.准备套餐信息", "steps": "1.点击新增套餐\n2.填写套餐信息\n3.设置价格和时长\n4.保存套餐", "inputData": "API: POST /system/vip/package\n数据: {packageName:'月卡套餐', price:100.00, duration:30, warehouseId:1}", "expectedResult": "1.套餐创建成功\n2.返回成功提示\n3.套餐列表更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_REFUND_001", "scenario": "退款管理 - 查询退款记录", "preconditions": "1.管理员已登录\n2.拥有退款查询权限\n3.系统中存在退款记录", "steps": "1.访问退款管理页面\n2.设置查询条件\n3.查看退款记录", "inputData": "API: GET /system/refund/list\n参数: pageNum=1, pageSize=10, refundStatus='1'", "expectedResult": "1.返回退款记录列表\n2.显示退款详细信息\n3.支持状态筛选", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_REFUND_002", "scenario": "退款管理 - 根据订单号查询退款记录", "preconditions": "1.管理员已登录\n2.拥有退款查询权限(refund:refund:query)\n3.存在退款记录", "steps": "1.输入订单号\n2.点击查询按钮\n3.查看退款记录详情", "inputData": "API: GET /system/refund/tradeId/{tradeId}\n参数: tradeId='ORDER123456'", "expectedResult": "1.返回对应订单的退款记录\n2.显示退款详细信息\n3.包含退款状态和时间", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_INVOICE_001", "scenario": "发票管理 - 查询发票记录", "preconditions": "1.管理员已登录\n2.拥有发票查询权限(operation:invoiceRecord:query)\n3.系统中存在发票记录", "steps": "1.访问发票管理页面\n2.设置查询条件\n3.查看发票记录", "inputData": "API: GET /system/operation/invoiceRecord/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回发票记录列表\n2.显示发票详细信息\n3.支持开票状态筛选", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_INVOICE_002", "scenario": "发票管理 - 发票重开", "preconditions": "1.管理员已登录\n2.拥有发票重开权限(operation:invoiceRecord:reopen)\n3.存在已开具的发票", "steps": "1.选择已开具发票\n2.点击重开按钮\n3.确认重开操作", "inputData": "API: POST /system/operation/invoiceRecord/reopen/{id}\n参数: id=1", "expectedResult": "1.发票重开成功\n2.生成新的发票记录\n3.原发票状态更新", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_INVOICE_003", "scenario": "发票管理 - 发票冲红", "preconditions": "1.管理员已登录\n2.拥有发票冲红权限(operation:invoiceRecord:reverse)\n3.存在已开具的发票", "steps": "1.选择已开具发票\n2.点击冲红按钮\n3.确认冲红操作", "inputData": "API: POST /system/operation/invoiceRecord/reverse/{id}\n参数: id=1", "expectedResult": "1.发票冲红成功\n2.生成冲红发票记录\n3.原发票状态更新", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_ADVERT_001", "scenario": "广告配置 - 查询广告列表", "preconditions": "1.管理员已登录\n2.拥有广告查询权限(system:advertConfig:query)\n3.系统中存在广告配置", "steps": "1.访问广告配置页面\n2.查看广告列表\n3.验证广告信息", "inputData": "API: GET /system/advertConfig/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回广告列表\n2.显示广告内容和位置\n3.包含有效期信息", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_ADVERT_002", "scenario": "广告配置 - 新增广告", "preconditions": "1.管理员已登录\n2.拥有广告新增权限(system:advertConfig:add)\n3.准备广告素材和信息", "steps": "1.点击新增广告\n2.填写广告信息\n3.上传广告图片\n4.设置投放时间\n5.保存广告配置", "inputData": "API: POST /system/advertConfig\n数据: {title:'测试广告', content:'广告内容', position:'首页轮播', startTime:'2024-01-01', endTime:'2024-12-31'}", "expectedResult": "1.广告创建成功\n2.返回成功提示\n3.广告列表更新", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_WXUSER_001", "scenario": "微信用户管理 - 查询用户列表", "preconditions": "1.管理员已登录\n2.拥有微信用户查询权限\n3.系统中存在微信用户", "steps": "1.访问微信用户管理页面\n2.设置查询条件\n3.查看用户列表", "inputData": "API: GET /system/owner/wxuser/list\n参数: pageNum=1, pageSize=10, nickName='测试'", "expectedResult": "1.返回微信用户列表\n2.显示用户基本信息\n3.支持昵称模糊查询", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_ROLE_003", "scenario": "角色管理 - 分配用户到角色", "preconditions": "1.管理员已登录\n2.拥有角色编辑权限(system:role:edit)\n3.存在角色和用户", "steps": "1.选择角色\n2.点击分配用户按钮\n3.选择要分配的用户\n4.确认分配", "inputData": "API: PUT /system/role/authUser/selectAll\n参数: roleId=2&userIds=3,4,5", "expectedResult": "1.用户分配成功\n2.用户获得角色权限\n3.角色用户列表更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_006", "scenario": "系统用户管理 - 用户状态修改", "preconditions": "1.管理员已登录\n2.拥有用户编辑权限\n3.存在可修改状态的用户", "steps": "1.选择用户\n2.点击状态切换按钮\n3.确认状态修改", "inputData": "API: PUT /system/user/changeStatus\n数据: {userId:2, status:'1'}", "expectedResult": "1.用户状态修改成功\n2.用户被停用或启用\n3.影响用户登录权限", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_USER_007", "scenario": "系统用户管理 - 查询用户授权角色", "preconditions": "1.管理员已登录\n2.拥有用户查询权限\n3.存在已分配角色的用户", "steps": "1.选择用户\n2.点击分配角色按钮\n3.查看用户当前角色", "inputData": "API: GET /system/user/authRole/{userId}\n参数: userId=2", "expectedResult": "1.返回用户角色信息\n2.显示已分配和未分配角色\n3.支持角色重新分配", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_GATE_002", "scenario": "车辆出入场记录 - 导出出入场数据", "preconditions": "1.管理员已登录\n2.拥有出入场记录导出权限(order:gateParkingInfo:export)\n3.存在出入场数据", "steps": "1.设置导出条件\n2.点击导出按钮\n3.下载导出文件", "inputData": "API: POST /system/order/gateParkingInfo/export\n参数: startTime='2024-01-01', endTime='2024-12-31'", "expectedResult": "1.生成Excel文件\n2.包含出入场详细记录\n3.文件下载成功", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_PACKAGE_003", "scenario": "会员套餐管理 - 修改套餐状态", "preconditions": "1.管理员已登录\n2.拥有套餐编辑权限(vip:package:edit)\n3.存在可修改的套餐", "steps": "1.选择套餐\n2.点击修改按钮\n3.更新套餐状态\n4.保存修改", "inputData": "API: PUT /system/vip/package\n数据: {id:1, status:'1', remark:'套餐下架'}", "expectedResult": "1.套餐状态更新成功\n2.影响套餐可购买性\n3.列表状态更新", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MEMBER_002", "scenario": "会员管理 - 新增会员", "preconditions": "1.管理员已登录\n2.拥有会员新增权限(vip:member:add)\n3.准备会员基本信息", "steps": "1.点击新增会员\n2.填写会员信息\n3.选择会员套餐\n4.保存会员", "inputData": "API: POST /system/vip/member\n数据: {phoneNumber:'13800138001', plateNo:'京A12345', packageId:1, warehouseId:1}", "expectedResult": "1.会员创建成功\n2.自动生成会员编号\n3.会员列表更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_DICT_001", "scenario": "字典管理 - 查询字典类型", "preconditions": "1.管理员已登录\n2.拥有字典查询权限(system:dict:query)\n3.系统中存在字典数据", "steps": "1.访问字典管理页面\n2.查看字典类型列表\n3.验证字典信息", "inputData": "API: GET /system/dict/type/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回字典类型列表\n2.显示字典名称和类型\n3.支持状态筛选", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_NOTICE_001", "scenario": "通知公告 - 查询公告列表", "preconditions": "1.管理员已登录\n2.拥有公告查询权限(system:notice:query)\n3.系统中存在公告数据", "steps": "1.访问通知公告页面\n2.查看公告列表\n3.验证公告信息", "inputData": "API: GET /system/notice/list\n参数: pageNum=1, pageSize=10", "expectedResult": "1.返回公告列表\n2.显示公告标题和内容\n3.包含发布状态", "priority": "P2", "tested": false, "testResult": null}]