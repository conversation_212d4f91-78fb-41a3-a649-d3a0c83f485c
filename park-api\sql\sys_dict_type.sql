/*
 Navicat MySQL Data Transfer

 Source Server         : 远程测试数据库
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : 127.0.0.1:3306
 Source Schema         : parknew

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 01/08/2025 10:06:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 137 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '广告状态', 'advert_status', '0', 1, '2025-06-06 13:52:02', 1, '2025-07-02 23:24:56', '广告配置状态列表');
INSERT INTO `sys_dict_type` VALUES (101, '厂商环境类型', 'manufacturer_env_type', '0', 1, '2025-06-11 11:59:04', 1, '2025-07-02 23:24:56', '道闸厂商API环境类型：正式环境、测试环境');
INSERT INTO `sys_dict_type` VALUES (102, '场库状态', 'warehouse_status', '0', 1, '2025-06-18 11:57:11', 1, '2025-07-02 23:24:56', '场库营业状态');
INSERT INTO `sys_dict_type` VALUES (103, '智慧场库等级', 'warehouse_smart_level', '0', 1, '2025-06-18 11:57:25', 1, '2025-07-02 23:24:56', '智慧场库等级分类');
INSERT INTO `sys_dict_type` VALUES (104, 'VIP车位管理', 'vip_park_sign', '0', 1, '2025-06-18 11:57:41', 1, '2025-07-02 23:24:56', 'VIP车位管理标识');
INSERT INTO `sys_dict_type` VALUES (105, '异常订单处理状态', 'exception_order_handle_status', '0', 1, '2025-06-19 15:04:29', 1, '2025-07-02 23:24:56', '异常订单处理状态列表');
INSERT INTO `sys_dict_type` VALUES (106, '异常订单类型', 'exception_order_type', '0', 1, '2025-06-19 15:04:44', 1, '2025-07-02 23:24:56', '异常订单类型列表');
INSERT INTO `sys_dict_type` VALUES (107, '停车订单状态', 'parking_order_status', '0', 1, '2025-06-19 15:05:02', 1, '2025-07-02 23:24:56', '停车订单状态列表');
INSERT INTO `sys_dict_type` VALUES (110, '停车场类型', 'parking_lot_type', '0', 1, '2025-06-20 15:26:42', 1, '2025-07-02 23:24:56', '停车场类型分类');
INSERT INTO `sys_dict_type` VALUES (111, '停车场状态', 'parking_lot_status', '0', 1, '2025-06-20 15:26:57', 1, '2025-07-02 23:24:56', '停车场使用状态');
INSERT INTO `sys_dict_type` VALUES (112, 'VIP套餐类型', 'vip_package_type', '0', 1, '2025-06-20 15:28:50', 1, '2025-07-02 23:24:56', 'VIP套餐类型分类');
INSERT INTO `sys_dict_type` VALUES (113, 'VIP套餐状态', 'vip_package_status', '0', 1, '2025-06-20 15:29:08', 1, '2025-07-02 23:24:56', 'VIP套餐状态');
INSERT INTO `sys_dict_type` VALUES (118, '运营商状态', 'operator_status', '0', 1, '2025-06-20 15:37:34', 1, '2025-07-02 23:24:56', '运营商状态');
INSERT INTO `sys_dict_type` VALUES (119, '支付状态', 'pay_status', '0', 1, '2025-06-20 15:41:58', 1, '2025-07-02 23:24:56', '订单支付状态');
INSERT INTO `sys_dict_type` VALUES (120, '支付方式', 'pay_method', '0', 1, '2025-06-20 15:41:58', 1, '2025-07-02 23:24:56', '支付方式类型');
INSERT INTO `sys_dict_type` VALUES (121, '优先级', 'priority_level', '0', 1, '2025-06-26 09:30:36', 1, '2025-07-02 23:24:56', '异常订单优先级字典');
INSERT INTO `sys_dict_type` VALUES (122, '来源类型', 'source_type', '0', 1, '2025-06-26 09:30:51', 1, '2025-07-02 23:24:56', '异常订单来源类型字典');
INSERT INTO `sys_dict_type` VALUES (123, '车牌类型', 'plate_type', '0', 1, '2025-06-26 10:28:55', 1, '2025-07-02 23:24:56', '车牌类型字典');
INSERT INTO `sys_dict_type` VALUES (127, '会员类型', 'vip_member_type', '0', 1, '2025-07-02 09:15:39', 1, '2025-07-02 23:24:56', '会员类型字典');
INSERT INTO `sys_dict_type` VALUES (128, '小程序用户状态', 'wx_user_status', '0', 1, '2025-07-04 10:18:31', NULL, '2025-07-04 10:18:31', '小程序用户状态列表');
INSERT INTO `sys_dict_type` VALUES (129, '小程序用户类型', 'wx_user_type', '0', 1, '2025-07-04 10:18:51', NULL, '2025-07-04 10:18:51', '小程序用户类型列表');
INSERT INTO `sys_dict_type` VALUES (131, '道闸操作类型', 'gate_operation_type', '0', 1, '2025-07-11 08:16:27', NULL, '2025-07-11 08:16:27', '道闸系统支持的操作类型（数字化）');
INSERT INTO `sys_dict_type` VALUES (132, '道闸品牌', 'gate_vendor', '0', 1, '2025-07-11 08:16:45', NULL, '2025-07-11 08:16:45', '支持的道闸品牌列表（数字化）');
INSERT INTO `sys_dict_type` VALUES (133, '道闸API路径', 'gate_api_path', '0', 1, '2025-07-11 08:17:01', NULL, '2025-07-11 08:17:01', '各品牌道闸的API路径配置');
INSERT INTO `sys_dict_type` VALUES (134, '错误数据码', 'error_data_code', '0', 1, '2025-07-11 16:18:47', NULL, '2025-07-11 16:18:47', '错误数据日志错误码列表');
INSERT INTO `sys_dict_type` VALUES (135, '支付类型', 'pay_type', '0', 1, '2025-07-19 00:13:05', NULL, '2025-07-19 00:13:05', '银联支付类型：小程序、C扫B、H5等');
INSERT INTO `sys_dict_type` VALUES (136, '协议类型', 'agreement_type', '0', 1, '2025-07-22 10:01:30', NULL, '2025-07-22 10:01:30', '系统协议类型列表');

SET FOREIGN_KEY_CHECKS = 1;
