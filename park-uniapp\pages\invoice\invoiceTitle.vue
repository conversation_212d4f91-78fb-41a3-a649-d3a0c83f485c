<template>
  <view class="invoice-title-container">
    <!-- 发票抬头列表 -->
    <view class="invoice-title-list" v-if="invoiceTitleList.length > 0">
      <view
        class="invoice-title-item"
        v-for="item in invoiceTitleList"
        :key="item.id"
      >
        <view class="item-card">
          <view class="item-header">
            <view class="item-type">
              <up-icon
                :name="getTypeIcon(item)"
                :color="getTypeIconColor(item)"
                size="16"
                class="type-icon"
              ></up-icon>
              <text class="type-text">{{ getTypeText(item) }}</text>
            </view>
            <view class="item-actions">
              <view class="action-btn edit-btn" @tap="handleEdit(item)">
                <up-icon
                  name="edit-pen"
                  color="#9e9e9e"
                  size="16"
                  class="action-icon"
                ></up-icon>
                <text>编辑</text>
              </view>
              <view class="action-btn delete-btn" @tap="handleDelete(item)">
                <up-icon
                  name="trash"
                  color="#9e9e9e"
                  size="16"
                  class="action-icon"
                ></up-icon>
                <text>删除</text>
              </view>
            </view>
          </view>
          <view class="item-content">
            <view class="content-title">{{ item.invoiceTitleContent }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <up-empty text="暂无发票抬头信息" />
    </view>

    <!-- 底部固定的添加按钮 -->
    <view class="bottom-add-btn" @tap="addInvoiceTitle">
      <up-icon name="plus" color="#fff" size="18" class="add-icon"></up-icon>
      <text class="add-text">添加发票抬头</text>
    </view>
  </view>
</template>

<script setup>
import { ref, nextTick } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { getInvoiceTitleList, deleteInvoiceTitle } from "@/api/invoice";

const invoiceTitleList = ref([]);

onShow(() => {
  fetchInvoiceTitleList();
});

// 获取类型图标
const getTypeIcon = (item) => {
  if (item.invoiceType === 1) {
    return "file-text"; // 专用发票使用文件图标
  }
  return item.titleType === 1 ? "home" : "account"; // 公司用房子图标，个人用用户图标
};

// 获取类型图标颜色
const getTypeIconColor = (item) => {
  if (item.invoiceType === 1) {
    return "#4BA1FC"; // 专用发票用蓝色
  }
  return item.titleType === 1 ? "#FF9500" : "#34C759"; // 公司用橙色，个人用绿色
};

// 获取类型文本
const getTypeText = (item) => {
  if (item.invoiceType === 1) {
    return "专用发票抬头";
  }
  const typeText = item.titleType === 1 ? "公司" : "个人";
  return `${typeText} · 普通发票抬头`;
};

// 获取发票抬头列表
const fetchInvoiceTitleList = () => {
  getInvoiceTitleList()
    .then((res) => {
      console.log(res);
      // 确保数据正确更新
      invoiceTitleList.value = [];
      nextTick(() => {
        invoiceTitleList.value = res.data || [];
      });
    })
    .catch((err) => {
      console.error("获取发票抬头列表失败:", err);
      uni.showToast({
        title: "获取数据失败",
        icon: "none",
      });
    });
};

// 添加发票抬头
const addInvoiceTitle = () => {
  uni.navigateTo({
    url: "/pages/invoice/addInvoiceTitle?isEdit=false",
  });
};

// 编辑发票抬头
const handleEdit = (item) => {
  const obj = JSON.stringify(item);
  uni.navigateTo({
    url:
      "/pages/invoice/addInvoiceTitle?isEdit=true&obj=" +
      encodeURIComponent(obj),
  });
};

// 删除发票抬头
const handleDelete = (item) => {
  uni.showModal({
    content: "确定要删除这个发票抬头吗？",
    cancelText: "取消",
    confirmText: "确认",
    success: (res) => {
      if (res.confirm) {
        // 显示加载提示
        uni.showLoading({
          title: "删除中...",
        });

        deleteInvoiceTitle({ id: item.id })
          .then((res) => {
            uni.hideLoading();
            if (res.code === 200) {
              uni.showToast({
                title: "删除成功",
                icon: "success",
              });
              // 重新获取列表
              fetchInvoiceTitleList();
            } else {
              uni.showToast({
                title: res.msg || "删除失败",
                icon: "none",
              });
            }
          })
          .catch((err) => {
            uni.hideLoading();
            console.error("删除发票抬头失败:", err);
            uni.showToast({
              title: "删除失败，请重试",
              icon: "none",
            });
          });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.invoice-title-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24rpx;
  padding-bottom: 140rpx;
  position: relative;
}

.invoice-title-list {
  .invoice-title-item {
    margin-bottom: 24rpx;

    .item-card {
      background: #fff;
      border-radius: 24rpx;
      padding: 32rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

      .item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 20rpx;
        border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

        .item-type {
          display: flex;
          align-items: center;

          .type-icon {
            margin-right: 8rpx;
          }

          .type-text {
            font-size: 24rpx;
            font-weight: bold;
            color: #616161;
            line-height: 44rpx;
          }
        }

        .item-actions {
          display: flex;
          align-items: center;
          gap: 24rpx;

          .action-btn {
            display: flex;
            align-items: center;
            font-size: 28rpx;
            font-weight: 400;
            color: #9e9e9e;
            line-height: 44rpx;

            .action-icon {
              margin-right: 8rpx;
            }
          }
        }
      }

      .item-content {
        padding-top: 20rpx;

        .content-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #212121;
          padding-bottom: 8rpx;
          line-height: 44rpx;
        }

        .content-subtitle {
          font-size: 24rpx;
          font-weight: 400;
          color: #9e9e9e;
          line-height: 44rpx;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

/* 底部固定的添加按钮 */
.bottom-add-btn {
  position: fixed;
  bottom: 40rpx;
  width: 85%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);
  color: #fff;
  padding: 24rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  .add-icon {
    margin-right: 12rpx;
  }

  .add-text {
    font-size: 32rpx;
    font-weight: 500;
  }
}
</style>