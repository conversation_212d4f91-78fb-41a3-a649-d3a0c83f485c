<template>
    <view class="invoice-manage">
        <view class="invoice-content">
            <template v-if="invoiceList.length > 0">
                <view class="invoice-list">
                    <view 
                        class="invoice-item" 
                        v-for="(item, index) in invoiceList" 
                        :key="item.id"
                        :class="[index === invoiceList.length - 1 ? 'last-item' : '']"
                    >
                        <view class="item-header">
                            <view class="order-type">
                                <!-- functionType 功能类型：1停车，2会员 -->
                                <template v-if="item.functionType === 1">
                                    <image src="/static/order/car.png" mode="aspectFit"></image>
                                    <text class="type-name">停车订单</text>
                                </template>
                                <template v-else-if="item.functionType === 2">
                                    <image src="/static/package/taocantype.png" mode="aspectFit"></image>
                                    <text class="type-name">会员订单</text>
                                </template>
                                <template v-else>
                                    <image src="/static/order/car.png" mode="aspectFit"></image>
                                    <text class="type-name">其他订单</text>
                                </template>
                            </view>
                            <view class="status-badge" :style="{ color: getStatusColor(item.status) }">
                                {{ getStatusText(item.status) }}
                            </view>
                        </view>

                        <view class="item-content">
                            <view class="info-row">
                                <text class="info-label">发票抬头：</text>
                                <text class="info-value">{{ item.invoiceTitleContent || '个人' }}</text>
                            </view>
                            <view class="info-row">
                                <text class="info-label">发票类型：</text>
                                <text class="info-value">{{ item.invoiceType === 0 ? '普通发票' : '专用发票' }}</text>
                            </view>
                            <view class="info-row">
                                <text class="info-label">接收邮箱：</text>
                                <text class="info-value">{{ item.notifyEmail || '-' }}</text>
                            </view>
                            <template v-if="item.status === 'CLOSED'">
                                <view class="info-row">
                                    <text class="info-label">失败原因：</text>
                                    <text class="info-value error">{{ item.remark || '-' }}</text>
                                </view>
                            </template>
                        </view>

                        <view class="item-footer">
                            <view class="issue-time">
                                <up-icon name="clock" color="#666666" size="14"></up-icon>
                                <text>{{ item.issueDate || '-' }}</text>
                            </view>
                            <view class="invoice-amount">
                                <text class="amount-label">开票金额：</text>
                                <text class="amount-value">¥{{ item.totalMoney || '0' }}</text>
                            </view>
                        </view>
                    </view>
                </view>


            </template>

            <template v-else>
                <view class="empty-state">
                    <up-empty text="暂无发票记录" color="#64748b"></up-empty>
                </view>
            </template>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getInvoiceRecordList } from '@/api/invoice'

// 响应式数据
const invoiceList = ref([])
const loading = ref(false)

// 页面加载
onMounted(() => {
    fetchInvoiceList()
})

// 获取发票列表
const fetchInvoiceList = async () => {
    if (loading.value) return
    
    try {
        loading.value = true
        
        uni.showLoading({
            title: '加载中...',
            mask: true
        })

        const res = await getInvoiceRecordList()
        console.log('发票记录数据:', res)

        // 适配后端返回格式
        invoiceList.value = res.data || res.rows || []

    } catch (error) {
        console.error('获取发票列表失败:', error)
        uni.showToast({
            title: '获取发票列表失败',
            icon: 'none'
        })
    } finally {
        loading.value = false
        uni.hideLoading()
    }
}



// 获取状态文本
const getStatusText = (status) => {
    switch (status) {
        case 'PENDING':
            return '待开具'
        case 'ISSUING':
            return '开具中'
        case 'ISSUED':
            return '已开具'
        case 'REVERSING':
            return '红冲中'
        case 'REVERSED':
            return '已红冲'
        case 'CLOSED':
            return '已关闭'
        default:
            return '-'
    }
}

// 获取状态颜色
const getStatusColor = (status) => {
    switch (status) {
        case 'PENDING':
            return '#3b82f6'
        case 'ISSUING':
            return '#f59e0b'
        case 'ISSUED':
            return '#16a34a'
        case 'REVERSING':
            return '#ef4444'
        case 'REVERSED':
            return '#6b7280'
        case 'CLOSED':
            return '#9ca3af'
        default:
            return '#6b7280'
    }
}

// 下拉刷新
const onPullDownRefresh = async () => {
    await fetchInvoiceList()
    uni.stopPullDownRefresh()
}

// 暴露方法给页面使用
defineExpose({
    onPullDownRefresh
})
</script>

<style lang="scss" scoped>
.invoice-manage {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.invoice-content {
    padding: 32rpx;
}

.invoice-list {
    .invoice-item {
        background: white;
        border-radius: 20rpx;
        padding: 32rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        
        &.last-item {
            margin-bottom: 0;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;
            
            .order-type {
                display: flex;
                align-items: center;
                
                image {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 12rpx;
                }
                
                .type-name {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333333;
                }
            }
            
            .status-badge {
                font-size: 26rpx;
                font-weight: 500;
                padding: 8rpx 16rpx;
                border-radius: 16rpx;
                background: rgba(59, 130, 246, 0.1);
            }
        }
        
        .item-content {
            border-bottom: 1rpx solid #f0f0f0;
            padding-bottom: 24rpx;
            margin-bottom: 24rpx;
            
            .info-row {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;
                
                &:last-child {
                    margin-bottom: 0;
                }
                
                .info-label {
                    font-size: 26rpx;
                    color: #666666;
                    min-width: 140rpx;
                }
                
                .info-value {
                    font-size: 26rpx;
                    color: #333333;
                    flex: 1;
                    
                    &.error {
                        color: #ef4444;
                    }
                }
            }
        }
        
        .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .issue-time {
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #666666;
                
                text {
                    margin-left: 8rpx;
                }
            }
            
            .invoice-amount {
                display: flex;
                align-items: center;
                
                .amount-label {
                    font-size: 24rpx;
                    color: #3b82f6;
                }
                
                .amount-value {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #3b82f6;
                    margin-left: 8rpx;
                }
            }
        }
    }
}



.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 40rpx;
    margin-top: 60rpx;
}
</style> 