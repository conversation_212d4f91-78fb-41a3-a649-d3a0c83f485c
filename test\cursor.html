<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="UTF-8">
<title>管理员端功能测试用例清单</title>
<style>
body{font-family:Arial,Helvetica,sans-serif;margin:20px;}
h1{margin-top:0;}
h2{background:#f2f2f2;padding:6px;border-left:4px solid #2196f3;}
table{width:100%;border-collapse:collapse;margin-bottom:30px;}
th,td{border:1px solid #d0d0d0;padding:6px;font-size:14px;}
th{background:#fafafa;}
tr:nth-child(even){background:#fcfcfc;}
.done{background:#c8e6c9 !important;}
</style>
<script>
function toggle(box){
  const tr = box.closest('tr');
  box.checked ? tr.classList.add('done') : tr.classList.remove('done');
}
</script>
</head>
<body>
<h1>停车场后台 - 管理员端功能测试用例</h1>
<p>说明：勾选左侧复选框即可标记该测试已完成。</p>

<!-- ======= 登录与仪表盘 ======= -->
<h2>1. 登录 / 仪表盘</h2>
<table>
<tr><th>✔</th><th>测试点</th><th>预期结果 / 判断逻辑</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>正确账号密码登录</td><td>跳转首页，收到 token，菜单正常渲染</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>错误密码</td><td>提示“用户名或密码错误”，不发放 token</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>验证码刷新 &amp; 校验</td><td>验证码正确才允许登录，错误提示重输</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>token 过期重新登录</td><td>前端拦截 401 &amp; 跳转登录页</td></tr>
</table>

<!-- ======= 系统管理 ======= -->
<h2>2. 系统管理</h2>

<!-- 用户管理 -->
<h3>2.1 用户管理 (`/system/user`)</h3>
<table>
<tr><th>✔</th><th>测试点</th><th>预期结果 / 判断逻辑</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>查询列表</td><td>根据用户名/手机号/状态过滤；分页准确</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>新增用户（必填校验）</td><td>未填用户名/角色等提示必填；提交成功列表刷新</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>用户名重复</td><td>后端返回 code=400，前端 toast “已存在”</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>编辑用户信息</td><td>修改后立即生效；再次查询数据已更新</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>重置密码</td><td>二次确认弹窗；成功提示</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>启/停用切换</td><td>状态标签实时切换，接口 `/changeStatus` 返回 success</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>批量删除</td><td>勾选多行—删除—刷新后消失</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>导入 Excel 模板</td><td>文件格式校验，成功后新增数据可查询</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>导出数据</td><td>下载文件内容与筛选条件一致</td></tr>
</table>

<!-- 角色管理 -->
<h3>2.2 角色管理 (`/system/role`)</h3>
<table>
<tr><th>✔</th><th>测试点</th><th>预期</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>新增角色 &amp; 菜单授权</td><td>保存后菜单树关联正确；接口 `/role` POST 200</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>数据权限范围设置</td><td>选中部门后，返回字段 `dataScope` 正确</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>角色状态切换</td><td>禁用角色后关联用户无法登录</td></tr>
</table>

<!-- 其余通用模块简表 -->
<h3>2.3 其他通用模块</h3>
<table>
<tr><th>✔</th><th>模块</th><th>关键测试</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>菜单管理</td><td>新增/编辑/隐藏菜单；路由可见性</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>部门管理</td><td>树形新增/父子节点校验；递归删除</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>岗位管理</td><td>岗位编码唯一；启停用</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>字典管理</td><td>字典类型唯一；字典选项增删改</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>参数设置</td><td>系统参数修改实时生效</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>日志管理</td><td>操作日志、登录日志分页 &amp; 搜索</td></tr>
</table>

<!-- ======= 监控中心 ======= -->
<h2>3. 系统监控</h2>
<table>
<tr><th>✔</th><th>模块</th><th>测试点</th><th>预期</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>在线用户</td><td>强退某用户</td><td>被强退账号立即 401</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>服务监控</td><td>查看服务详情</td><td>接口 `/actuator/health` 正常</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>定时任务</td><td>立即执行/暂停/恢复</td><td>任务状态变化与后台一致</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>缓存监控</td><td>清空缓存</td><td>再次查询缓存为空</td></tr>
</table>

<!-- ======= 运营相关 ======= -->
<h2>4. 运营管理</h2>
<table>
<tr><th>✔</th><th>模块（API 文件）</th><th>关键用例</th><th>预期/接口</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>dashboard.js</td><td>数据概览日期筛选</td><td>图表与指标随筛选刷新</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>order/\*.js</td><td>订单列表查询/详情</td><td>分页、导出、退款入口</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>refund/\*.js</td><td>申请退款 &amp; 审核</td><td>状态流转正确（申请→同意/驳回）</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>operation/\*.js</td><td>出入口记录检索</td><td>卡口、车牌模糊查询</td></tr>
</table>

<!-- ======= 会员 / 套餐 ======= -->
<h2>5. 会员中心</h2>
<table>
<tr><th>✔</th><th>模块</th><th>测试点</th><th>预期</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>vip/member.js</td><td>新增会员</td><td>会员号自动生成，重复手机号校验</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>vip/package.js</td><td>创建套餐</td><td>金额必须&gt;0；有效期逻辑正确</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>vip/transaction.js</td><td>购买套餐 &amp; 支付回调</td><td>订单生成，支付成功后状态改“已生效”</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>vip/specialUser.js</td><td>特殊用户名单</td><td>增删后实时生效于收费逻辑</td></tr>
</table>

<!-- ======= 平台配置 ======= -->
<h2>6. 平台配置</h2>
<table>
<tr><th>✔</th><th>模块</th><th>测试点</th><th>预期</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>platform/advert.js</td><td>广告图片上传</td><td>格式/大小限制，OSS 路径返回</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>platform/agreement.js</td><td>协议内容编辑</td><td>富文本保存后前台展示更新</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>platform/invoice.js</td><td>发票抬头审核</td><td>审批通过后用户可开票</td></tr>
</table>

<!-- ======= 公共用例 ======= -->
<h2>7. 通用/边界场景</h2>
<table>
<tr><th>✔</th><th>测试点</th><th>预期</th></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>表单必填校验</td><td>前端红框 &amp; 后端 400 校验一致</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>特殊字符 &amp; XSS</td><td>前端转义，后端过滤</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>长文本 / 超长数字</td><td>提示字符超限，不截断</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>网络中断重试</td><td>重连后自动刷新 token 继续操作</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>无权限访问路由</td><td>跳转 401 页面</td></tr>
<tr><td><input type="checkbox" onclick="toggle(this)"></td><td>同时多窗口登录</td><td>最后一次登录有效，旧 token 失效</td></tr>
</table>

<footer><p style="text-align:right">生成时间：<script>document.write(new Date().toLocaleString())</script></p></footer>
</body>
</html>