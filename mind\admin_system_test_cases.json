[{"id": "TC_ADMIN_LOGIN_001", "scenario": "管理员登录功能", "preconditions": "1.网络正常\n2.有效的管理员账号\n3.系统正常运行", "steps": "1.打开管理后台登录页面\n2.输入用户名和密码\n3.点击登录按钮\n4.验证登录结果", "inputData": "用户名：admin\n密码：admin123", "expectedResult": "1.登录成功\n2.跳转到系统首页\n3.显示用户信息\n4.加载菜单权限", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ADMIN_LOGIN_002", "scenario": "错误密码登录", "preconditions": "1.网络正常\n2.管理后台登录页面已打开", "steps": "1.输入正确用户名\n2.输入错误密码\n3.点击登录按钮\n4.观察错误提示", "inputData": "用户名：admin\n密码：wrongpassword", "expectedResult": "1.登录失败\n2.显示密码错误提示\n3.不跳转页面\n4.清空密码输入框", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ADMIN_DASHBOARD_001", "scenario": "系统首页数据展示", "preconditions": "1.管理员已登录\n2.有权限访问首页", "steps": "1.登录后进入首页\n2.观察数据统计卡片\n3.检查图表展示\n4.验证数据准确性", "inputData": "无", "expectedResult": "1.显示系统统计数据\n2.图表正常加载\n3.数据实时更新\n4.响应速度正常", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_MGMT_001", "scenario": "用户管理-查询用户列表", "preconditions": "1.管理员已登录\n2.有用户管理权限\n3.系统中有用户数据", "steps": "1.进入用户管理页面\n2.观察用户列表加载\n3.使用搜索功能\n4.验证分页功能", "inputData": "搜索条件：用户名、手机号等", "expectedResult": "1.用户列表正常显示\n2.搜索功能正常\n3.分页功能正常\n4.数据准确完整", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_MGMT_002", "scenario": "用户管理-新增用户", "preconditions": "1.管理员已登录\n2.有用户新增权限", "steps": "1.点击新增用户按钮\n2.填写用户信息\n3.选择角色权限\n4.提交保存\n5.验证新增结果", "inputData": "用户名：testuser\n密码：123456\n邮箱：<EMAIL>\n手机号：13800138000", "expectedResult": "1.弹出新增用户对话框\n2.表单验证正常\n3.保存成功\n4.列表中显示新用户", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_MGMT_003", "scenario": "用户管理-编辑用户", "preconditions": "1.管理员已登录\n2.有用户编辑权限\n3.存在可编辑的用户", "steps": "1.点击编辑用户按钮\n2.修改用户信息\n3.保存修改\n4.验证修改结果", "inputData": "修改邮箱：<EMAIL>", "expectedResult": "1.弹出编辑对话框\n2.预填充用户信息\n3.修改保存成功\n4.列表显示更新信息", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_MGMT_004", "scenario": "用户管理-删除用户", "preconditions": "1.管理员已登录\n2.有用户删除权限\n3.存在可删除的用户", "steps": "1.选择要删除的用户\n2.点击删除按钮\n3.确认删除操作\n4.验证删除结果", "inputData": "选择的用户ID", "expectedResult": "1.弹出删除确认框\n2.确认后删除成功\n3.列表中移除该用户\n4.显示删除成功提示", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_USER_MGMT_005", "scenario": "用户管理-分配角色", "preconditions": "1.管理员已登录\n2.有角色分配权限\n3.存在用户和角色", "steps": "1.选择用户\n2.点击分配角色\n3.选择角色\n4.保存分配\n5.验证角色分配", "inputData": "用户ID、角色列表", "expectedResult": "1.弹出角色分配对话框\n2.显示可用角色列表\n3.分配成功\n4.用户权限更新", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ROLE_MGMT_001", "scenario": "角色管理-查询角色列表", "preconditions": "1.管理员已登录\n2.有角色管理权限", "steps": "1.进入角色管理页面\n2.观察角色列表\n3.检查角色信息\n4.测试搜索功能", "inputData": "搜索条件：角色名称", "expectedResult": "1.角色列表正常显示\n2.角色信息完整\n3.搜索功能正常\n4.权限信息准确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ROLE_MGMT_002", "scenario": "角色管理-新增角色", "preconditions": "1.管理员已登录\n2.有角色新增权限", "steps": "1.点击新增角色\n2.填写角色信息\n3.分配菜单权限\n4.保存角色", "inputData": "角色名称：测试角色\n角色标识：test_role\n描述：测试用角色", "expectedResult": "1.弹出新增对话框\n2.权限树正常显示\n3.保存成功\n4.列表显示新角色", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_MENU_MGMT_001", "scenario": "菜单管理-查看菜单树", "preconditions": "1.管理员已登录\n2.有菜单管理权限", "steps": "1.进入菜单管理页面\n2.观察菜单树结构\n3.展开/收起菜单节点\n4.检查菜单信息", "inputData": "无", "expectedResult": "1.菜单树正常显示\n2.层级结构清晰\n3.展开收起正常\n4.菜单信息完整", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_MENU_MGMT_002", "scenario": "菜单管理-新增菜单", "preconditions": "1.管理员已登录\n2.有菜单新增权限", "steps": "1.点击新增菜单\n2.选择父级菜单\n3.填写菜单信息\n4.设置权限标识\n5.保存菜单", "inputData": "菜单名称：测试菜单\n路由地址：/test\n权限标识：test:list", "expectedResult": "1.弹出新增对话框\n2.父级菜单选择正常\n3.保存成功\n4.菜单树更新", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_ORDER_MGMT_001", "scenario": "停车订单-查询订单列表", "preconditions": "1.管理员已登录\n2.有订单查询权限\n3.系统中有订单数据", "steps": "1.进入停车订单页面\n2.观察订单列表\n3.使用筛选条件\n4.测试分页功能", "inputData": "筛选条件：时间范围、车牌号、状态", "expectedResult": "1.订单列表正常显示\n2.筛选功能正常\n3.分页功能正常\n4.订单信息准确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_ORDER_MGMT_002", "scenario": "停车订单-查看订单详情", "preconditions": "1.管理员已登录\n2.有订单查看权限\n3.存在订单记录", "steps": "1.点击订单详情\n2.查看订单信息\n3.检查支付信息\n4.验证停车时长", "inputData": "订单ID", "expectedResult": "1.弹出详情对话框\n2.订单信息完整\n3.支付信息准确\n4.时长计算正确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_EXCEPTION_ORDER_001", "scenario": "异常订单-查询异常订单", "preconditions": "1.管理员已登录\n2.有异常订单权限\n3.存在异常订单", "steps": "1.进入异常订单页面\n2.查看异常订单列表\n3.使用筛选功能\n4.检查异常类型", "inputData": "筛选条件：异常类型、处理状态", "expectedResult": "1.异常订单列表显示\n2.筛选功能正常\n3.异常类型准确\n4.状态显示正确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_EXCEPTION_ORDER_002", "scenario": "异常订单-处理异常订单", "preconditions": "1.管理员已登录\n2.有异常处理权限\n3.存在待处理异常", "steps": "1.选择异常订单\n2.点击处理按钮\n3.填写处理信息\n4.提交处理结果", "inputData": "处理方式、处理说明", "expectedResult": "1.弹出处理对话框\n2.处理信息保存成功\n3.订单状态更新\n4.记录处理日志", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_MGMT_001", "scenario": "场库管理-查询场库列表", "preconditions": "1.管理员已登录\n2.有场库管理权限\n3.系统中有场库数据", "steps": "1.进入场库管理页面\n2.观察场库列表\n3.使用搜索功能\n4.检查场库信息", "inputData": "搜索条件：场库名称、地址", "expectedResult": "1.场库列表正常显示\n2.搜索功能正常\n3.场库信息完整\n4.状态显示准确", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_MGMT_002", "scenario": "场库管理-新增场库", "preconditions": "1.管理员已登录\n2.有场库新增权限", "steps": "1.点击新增场库\n2.填写场库基本信息\n3.设置收费标准\n4.配置场库参数\n5.保存场库信息", "inputData": "场库名称：测试停车场\n地址：测试地址\n车位数：100\n收费标准：5元/小时", "expectedResult": "1.弹出新增对话框\n2.表单验证正常\n3.保存成功\n4.列表显示新场库", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_MGMT_003", "scenario": "场库管理-编辑场库信息", "preconditions": "1.管理员已登录\n2.有场库编辑权限\n3.存在可编辑场库", "steps": "1.选择场库\n2.点击编辑按钮\n3.修改场库信息\n4.保存修改", "inputData": "修改收费标准：6元/小时", "expectedResult": "1.弹出编辑对话框\n2.预填充场库信息\n3.修改保存成功\n4.信息更新显示", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_WAREHOUSE_MGMT_004", "scenario": "场库管理-启用/禁用场库", "preconditions": "1.管理员已登录\n2.有场库状态管理权限\n3.存在场库记录", "steps": "1.选择场库\n2.点击启用/禁用按钮\n3.确认操作\n4.验证状态变更", "inputData": "场库ID、目标状态", "expectedResult": "1.弹出确认对话框\n2.状态变更成功\n3.列表状态更新\n4.影响相关业务", "priority": "P0", "tested": false, "testResult": null}, {"id": "TC_VIP_MEMBER_001", "scenario": "VIP会员-查询会员列表", "preconditions": "1.管理员已登录\n2.有VIP管理权限\n3.系统中有VIP会员", "steps": "1.进入VIP会员页面\n2.观察会员列表\n3.使用筛选条件\n4.检查会员信息", "inputData": "筛选条件：会员等级、状态", "expectedResult": "1.会员列表正常显示\n2.筛选功能正常\n3.会员信息完整\n4.等级状态准确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_VIP_MEMBER_002", "scenario": "VIP会员-升级会员等级", "preconditions": "1.管理员已登录\n2.有会员等级管理权限\n3.存在普通会员", "steps": "1.选择普通会员\n2.点击升级按钮\n3.选择目标等级\n4.确认升级操作", "inputData": "会员ID、目标等级：VIP", "expectedResult": "1.弹出升级对话框\n2.等级选择正常\n3.升级成功\n4.会员权限更新", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_VIP_PACKAGE_001", "scenario": "VIP套餐-查询套餐列表", "preconditions": "1.管理员已登录\n2.有套餐管理权限\n3.系统中有套餐配置", "steps": "1.进入VIP套餐页面\n2.观察套餐列表\n3.检查套餐信息\n4.验证价格配置", "inputData": "无", "expectedResult": "1.套餐列表正常显示\n2.套餐信息完整\n3.价格配置准确\n4.状态显示正确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_VIP_PACKAGE_002", "scenario": "VIP套餐-新增套餐", "preconditions": "1.管理员已登录\n2.有套餐新增权限", "steps": "1.点击新增套餐\n2.填写套餐信息\n3.设置价格和时长\n4.配置适用范围\n5.保存套餐", "inputData": "套餐名称：月度VIP\n价格：100元\n时长：30天", "expectedResult": "1.弹出新增对话框\n2.表单验证正常\n3.保存成功\n4.列表显示新套餐", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_BLACKLIST_001", "scenario": "黑名单管理-查询黑名单", "preconditions": "1.管理员已登录\n2.有黑名单管理权限\n3.系统中有黑名单数据", "steps": "1.进入黑名单管理页面\n2.观察黑名单列表\n3.使用搜索功能\n4.检查黑名单信息", "inputData": "搜索条件：车牌号、手机号", "expectedResult": "1.黑名单列表正常显示\n2.搜索功能正常\n3.黑名单信息完整\n4.加入原因清晰", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_BLACKLIST_002", "scenario": "黑名单管理-添加黑名单", "preconditions": "1.管理员已登录\n2.有黑名单添加权限", "steps": "1.点击添加黑名单\n2.输入车牌号或手机号\n3.填写加入原因\n4.设置有效期\n5.保存黑名单", "inputData": "车牌号：京A12345\n原因：违规停车\n有效期：永久", "expectedResult": "1.弹出添加对话框\n2.信息验证正常\n3.保存成功\n4.列表显示新记录", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_BLACKLIST_003", "scenario": "黑名单管理-移除黑名单", "preconditions": "1.管理员已登录\n2.有黑名单移除权限\n3.存在黑名单记录", "steps": "1.选择黑名单记录\n2.点击移除按钮\n3.填写移除原因\n4.确认移除操作", "inputData": "移除原因：问题已解决", "expectedResult": "1.弹出移除确认框\n2.移除成功\n3.列表更新\n4.记录移除日志", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_ADVERT_MGMT_001", "scenario": "广告配置-查询广告列表", "preconditions": "1.管理员已登录\n2.有广告管理权限\n3.系统中有广告配置", "steps": "1.进入广告配置页面\n2.观察广告列表\n3.检查广告信息\n4.验证显示状态", "inputData": "无", "expectedResult": "1.广告列表正常显示\n2.广告信息完整\n3.状态显示准确\n4.排序功能正常", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_ADVERT_MGMT_002", "scenario": "广告配置-新增广告", "preconditions": "1.管理员已登录\n2.有广告新增权限", "steps": "1.点击新增广告\n2.上传广告图片\n3.填写广告信息\n4.设置显示时间\n5.保存广告配置", "inputData": "广告标题：新年促销\n链接地址：http://example.com\n显示时间：2024-01-01到2024-01-31", "expectedResult": "1.弹出新增对话框\n2.图片上传成功\n3.信息保存成功\n4.列表显示新广告", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_ADVERT_MGMT_003", "scenario": "广告配置-启用/禁用广告", "preconditions": "1.管理员已登录\n2.有广告状态管理权限\n3.存在广告记录", "steps": "1.选择广告\n2.点击启用/禁用按钮\n3.确认操作\n4.验证状态变更", "inputData": "广告ID、目标状态", "expectedResult": "1.状态切换成功\n2.列表状态更新\n3.前端显示同步\n4.操作日志记录", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_INVOICE_MGMT_001", "scenario": "发票记录-查询发票列表", "preconditions": "1.管理员已登录\n2.有发票管理权限\n3.系统中有发票记录", "steps": "1.进入发票记录页面\n2.观察发票列表\n3.使用筛选条件\n4.检查发票信息", "inputData": "筛选条件：开票时间、发票状态", "expectedResult": "1.发票列表正常显示\n2.筛选功能正常\n3.发票信息完整\n4.状态显示准确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_INVOICE_MGMT_002", "scenario": "发票记录-查看发票详情", "preconditions": "1.管理员已登录\n2.有发票查看权限\n3.存在发票记录", "steps": "1.点击发票详情\n2.查看发票信息\n3.检查开票内容\n4.验证金额计算", "inputData": "发票ID", "expectedResult": "1.弹出详情对话框\n2.发票信息完整\n3.开票内容准确\n4.金额计算正确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_REFUND_MGMT_001", "scenario": "退款管理-查询退款申请", "preconditions": "1.管理员已登录\n2.有退款管理权限\n3.系统中有退款申请", "steps": "1.进入退款管理页面\n2.观察退款列表\n3.使用筛选条件\n4.检查申请信息", "inputData": "筛选条件：申请时间、处理状态", "expectedResult": "1.退款列表正常显示\n2.筛选功能正常\n3.申请信息完整\n4.状态显示准确", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_REFUND_MGMT_002", "scenario": "退款管理-处理退款申请", "preconditions": "1.管理员已登录\n2.有退款处理权限\n3.存在待处理退款", "steps": "1.选择退款申请\n2.点击处理按钮\n3.选择处理结果\n4.填写处理说明\n5.提交处理结果", "inputData": "处理结果：同意/拒绝\n处理说明：具体原因", "expectedResult": "1.弹出处理对话框\n2.处理结果保存成功\n3.申请状态更新\n4.用户收到通知", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_SYSTEM_CONFIG_001", "scenario": "系统配置-查看参数配置", "preconditions": "1.管理员已登录\n2.有系统配置权限", "steps": "1.进入系统配置页面\n2.观察配置列表\n3.检查配置项\n4.验证配置值", "inputData": "无", "expectedResult": "1.配置列表正常显示\n2.配置项分类清晰\n3.配置值准确\n4.描述信息完整", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_SYSTEM_CONFIG_002", "scenario": "系统配置-修改参数配置", "preconditions": "1.管理员已登录\n2.有配置修改权限\n3.存在可修改配置", "steps": "1.选择配置项\n2.点击编辑按钮\n3.修改配置值\n4.保存修改", "inputData": "配置项：停车费率\n新值：6元/小时", "expectedResult": "1.弹出编辑对话框\n2.配置值验证正常\n3.修改保存成功\n4.系统配置生效", "priority": "P1", "tested": false, "testResult": null}, {"id": "TC_LOG_MGMT_001", "scenario": "日志管理-查询操作日志", "preconditions": "1.管理员已登录\n2.有日志查看权限\n3.系统中有操作日志", "steps": "1.进入操作日志页面\n2.观察日志列表\n3.使用筛选条件\n4.检查日志详情", "inputData": "筛选条件：操作时间、操作人、操作类型", "expectedResult": "1.日志列表正常显示\n2.筛选功能正常\n3.日志信息完整\n4.操作详情准确", "priority": "P2", "tested": false, "testResult": null}, {"id": "TC_LOG_MGMT_002", "scenario": "日志管理-查询登录日志", "preconditions": "1.管理员已登录\n2.有日志查看权限\n3.系统中有登录日志", "steps": "1.进入登录日志页面\n2.观察登录记录\n3.使用筛选条件\n4.检查登录信息", "inputData": "筛选条件：登录时间、用户名、登录状态", "expectedResult": "1.登录日志正常显示\n2.筛选功能正常\n3.登录信息完整\n4.状态显示准确", "priority": "P2", "tested": false, "testResult": null}]